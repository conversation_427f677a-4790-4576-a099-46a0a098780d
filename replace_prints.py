#!/usr/bin/env python3
import os
import re
import sys

def replace_prints_in_file(file_path):
    """Replace print statements with <PERSON><PERSON><PERSON><PERSON><PERSON> calls in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Check if AppLogger is already imported
        has_logger_import = "import '../utils/app_logger.dart'" in content or "import 'utils/app_logger.dart'" in content
        
        # Add import if not present and file has print statements
        if not has_logger_import and 'print(' in content:
            # Find where to add import
            import_pattern = r"(import\s+['\"][^'\"]*['\"];?\s*\n)"
            imports = re.findall(import_pattern, content)
            
            if imports:
                # Add after last import
                last_import = imports[-1]
                last_import_index = content.rfind(last_import)
                if last_import_index != -1:
                    insert_pos = last_import_index + len(last_import)
                    # Determine correct import path
                    if '/lib/' in file_path:
                        if file_path.count('/') - file_path.find('/lib/') > 2:
                            import_line = "import '../utils/app_logger.dart';\n"
                        else:
                            import_line = "import 'utils/app_logger.dart';\n"
                    else:
                        import_line = "import 'utils/app_logger.dart';\n"
                    
                    content = content[:insert_pos] + import_line + content[insert_pos:]
        
        # Replace different types of print statements
        replacements = [
            # Debug prints
            (r"print\('Debug[:\s]*([^']*)'", r"AppLogger.debug('\1'"),
            (r'print\("Debug[:\s]*([^"]*)"', r'AppLogger.debug("\1"'),
            
            # Error prints
            (r"print\('Error[:\s]*([^']*)'", r"AppLogger.error('\1'"),
            (r'print\("Error[:\s]*([^"]*)"', r'AppLogger.error("\1"'),
            
            # Warning prints
            (r"print\('Warning[:\s]*([^']*)'", r"AppLogger.warning('\1'"),
            (r'print\("Warning[:\s]*([^"]*)"', r'AppLogger.warning("\1"'),
            
            # Info prints (general)
            (r"print\('([^']*)'", r"AppLogger.info('\1'"),
            (r'print\("([^"]*)"', r'AppLogger.info("\1"'),
            
            # Handle prints with variables
            (r"print\('([^']*\$[^']*)'", r"AppLogger.info('\1'"),
            (r'print\("([^"]*\$[^"]*)"', r'AppLogger.info("\1"'),
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        # Handle multi-line prints and complex cases
        content = re.sub(r"print\(\s*'([^']*)'[^)]*\)", r"AppLogger.info('\1')", content)
        content = re.sub(r'print\(\s*"([^"]*)"[^)]*\)', r'AppLogger.info("\1")', content)
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Updated: {file_path}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    # Find all Dart files in lib directory
    dart_files = []
    for root, dirs, files in os.walk('lib'):
        for file in files:
            if file.endswith('.dart'):
                dart_files.append(os.path.join(root, file))
    
    print(f"Found {len(dart_files)} Dart files")
    
    updated_files = []
    for file_path in dart_files:
        if replace_prints_in_file(file_path):
            updated_files.append(file_path)
    
    print(f"\nUpdated {len(updated_files)} files:")
    for file_path in updated_files:
        print(f"  - {file_path}")

if __name__ == "__main__":
    main()
