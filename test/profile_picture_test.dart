import 'package:flutter_test/flutter_test.dart';
import 'package:tangazoletusacco/models/profile_picture_model.dart';
import 'dart:typed_data';

void main() {
  group('ProfilePictureModel Tests', () {
    test('should create ProfilePictureModel from base64', () {
      const base64Image = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
      
      final model = ProfilePictureModel.fromBase64(base64Image);
      
      expect(model.base64Image, equals(base64Image));
      expect(model.isFromServer, isTrue);
      expect(model.lastUpdated, isNotNull);
    });

    test('should create ProfilePictureModel from bytes', () {
      final imageBytes = Uint8List.fromList([1, 2, 3, 4, 5]);
      
      final model = ProfilePictureModel.fromBytes(imageBytes);
      
      expect(model.imageBytes, equals(imageBytes));
      expect(model.isFromServer, isTrue);
      expect(model.lastUpdated, isNotNull);
    });

    test('should create ProfilePictureModel from path', () {
      const imagePath = '/path/to/image.jpg';
      
      final model = ProfilePictureModel.fromPath(imagePath);
      
      expect(model.imagePath, equals(imagePath));
      expect(model.isFromServer, isFalse);
      expect(model.lastUpdated, isNotNull);
    });

    test('should convert to and from JSON', () {
      const base64Image = 'test_base64_string';
      final originalModel = ProfilePictureModel.fromBase64(base64Image);
      
      final json = originalModel.toJson();
      final recreatedModel = ProfilePictureModel.fromJson(json);
      
      expect(recreatedModel.base64Image, equals(originalModel.base64Image));
      expect(recreatedModel.isFromServer, equals(originalModel.isFromServer));
    });

    test('should create copy with updated fields', () {
      const originalPath = '/original/path.jpg';
      const newPath = '/new/path.jpg';
      
      final originalModel = ProfilePictureModel.fromPath(originalPath);
      final copiedModel = originalModel.copyWith(imagePath: newPath);
      
      expect(copiedModel.imagePath, equals(newPath));
      expect(copiedModel.isFromServer, equals(originalModel.isFromServer));
      expect(copiedModel.lastUpdated, equals(originalModel.lastUpdated));
    });
  });

  group('ProfilePictureResponse Tests', () {
    test('should create successful response', () {
      const message = 'Success';
      final profilePicture = ProfilePictureModel.fromBase64('test_base64');
      
      final response = ProfilePictureResponse.success(
        message: message,
        profilePicture: profilePicture,
      );
      
      expect(response.success, isTrue);
      expect(response.message, equals(message));
      expect(response.profilePicture, equals(profilePicture));
      expect(response.errorCode, isNull);
    });

    test('should create error response', () {
      const message = 'Error occurred';
      const errorCode = '500';
      
      final response = ProfilePictureResponse.error(
        message: message,
        errorCode: errorCode,
      );
      
      expect(response.success, isFalse);
      expect(response.message, equals(message));
      expect(response.errorCode, equals(errorCode));
      expect(response.profilePicture, isNull);
    });

    test('should create response from API response - success with image', () {
      final apiResponse = {
        'responseCode': '00',
        'message': 'Profile picture fetched successfully',
        'profilePicture': 'test_base64_image_data',
      };
      
      final response = ProfilePictureResponse.fromApiResponse(apiResponse);
      
      expect(response.success, isTrue);
      expect(response.message, equals('Profile picture fetched successfully'));
      expect(response.profilePicture, isNotNull);
      expect(response.profilePicture!.base64Image, equals('test_base64_image_data'));
    });

    test('should create response from API response - success without image', () {
      final apiResponse = {
        'status': 'success',
        'message': 'Profile picture deleted successfully',
      };
      
      final response = ProfilePictureResponse.fromApiResponse(apiResponse);
      
      expect(response.success, isTrue);
      expect(response.message, equals('Profile picture deleted successfully'));
      expect(response.profilePicture, isNull);
    });

    test('should create response from API response - error', () {
      final apiResponse = {
        'responseCode': '404',
        'message': 'Profile picture not found',
      };
      
      final response = ProfilePictureResponse.fromApiResponse(apiResponse);
      
      expect(response.success, isFalse);
      expect(response.message, equals('Profile picture not found'));
      expect(response.errorCode, equals('404'));
      expect(response.profilePicture, isNull);
    });
  });
}
