package com.example.tangazoletusacco

import android.os.Build
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "sacco_config"
    private val SMS_DEBUG_CHANNEL = "sms_autofill_debug"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        // Existing sacco config channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "getClientId" -> {
                    try {
                        val clientId = BuildConfig.CLIENT_ID
                        result.success(clientId)
                    } catch (e: Exception) {
                        result.error("UNAVAILABLE", "Client ID not available", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
        
        // SMS autofill debug channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SMS_DEBUG_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "getAndroidInfo" -> {
                    try {
                        val androidInfo = mapOf(
                            "version" to Build.VERSION.RELEASE,
                            "sdkInt" to Build.VERSION.SDK_INT,
                            "brand" to Build.BRAND,
                            "model" to Build.MODEL,
                            "manufacturer" to Build.MANUFACTURER
                        )
                        result.success(androidInfo)
                    } catch (e: Exception) {
                        result.error("UNAVAILABLE", "Android info not available", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}
