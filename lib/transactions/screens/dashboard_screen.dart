import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'loan_screens.dart';
import 'apply_loan_screen.dart';
import 'services_screen.dart';
import 'transaction_form_screen.dart';
import 'full_statement_screen.dart';
import 'mini_statement_screen.dart';
import 'balance_enquiry_screen.dart';
import 'form_journey_screen.dart';
import '../../screens/support_screen.dart';
import '../../utils/color_palette.dart';
import '../../screens/profile_screen.dart';
import '../../providers/profile_image_provider.dart';
import 'package:carousel_slider/carousel_slider.dart';
import '../../models/account_model.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import '../../screens/notifications_screen.dart';
import 'package:provider/provider.dart';
import '../../utils/services/account_manager.dart';
import '../../utils/services/api_service.dart';
import '../../utils/services/api_endpoints.dart';
import '../../utils/services/cryptographer.dart';
import '../../utils/services/shared_preferences_helper.dart';
import '../../utils/services/dashboard_tiles_service.dart';
import '../../utils/services/token_refresh_service.dart';
import '../../utils/services/latest_transactions_service.dart';
import '../../models/transaction_model.dart';

import '../../utils/app_logger.dart';
import 'dart:ui';

class DashboardScreen extends StatefulWidget {
  final String userName;

  const DashboardScreen({super.key, required this.userName});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;
  bool _isBalanceVisible = false;
  final PageController _pageController = PageController();
  int _currentAccountIndex = 0;
  
  // Real accounts from API
  List<dynamic> _realAccounts = [];
  String _memberName = '';
  bool _isLoadingAccounts = true;
  bool _isRefreshing = false;

  // Service ID to Service Code mapping for dynamic forms
  final Map<String, String> _serviceCodeMapping = {
    'deposit': 'DEP',
    'withdraw': 'WTH',
    'transfer_to_bank': 'TTB',
    'pay_to_till': 'PTT',
    'mpesa_float': 'MFL',
    'dstv': 'DSTV',
    'zuku': 'ZUKU',
    'startimes': 'STAR',
    'nairobi_water': 'NWTR',
  };

  // Define structure for services

  // State for currently selected favorites (Initialize with defaults)
  List<Map<String, dynamic>> _favoriteServices = [];
  List<Map<String, dynamic>> _dashboardTiles = [];
  bool _isLoadingTiles = true;

  // State for latest transactions
  List<TransactionModel> _transactions = [];
  bool _isLoadingTransactions = true;
  Timer? _transactionRefreshTimer;

  // Convert real accounts to AccountModel for UI consistency
  List<AccountModel> get _accounts {
    if (_realAccounts.isEmpty) {
      // Return empty list if no real accounts loaded yet
      return [];
    }
    
    return _realAccounts.map<AccountModel>((account) {
      return AccountModel(
        name: account['accountName'] ?? 'Unknown Account',
        balance: _formatBalance(account['balance']),
        backgroundColor: _getAccountColor(account),
        icon: _getAccountIcon(account),
        allowedActions: _getAccountActions(account),
        accountNo: account['accountNo'],
        rawAccount: account, // Store raw account data
      );
    }).toList();
  }

  @override
  void initState() {
    super.initState();
    _initializeAccounts();
    _loadDashboardTiles();
    _loadFavoriteServices(); // Load user's favorite selections
    _loadLatestTransactions(); // Load latest transactions from API

    // Initialize the token refresh service
    TokenRefreshService.instance.initialize();
    AppLogger.info('Token refresh service initialized on dashboard');
  }

  /// Load dashboard tiles from configuration
  Future<void> _loadDashboardTiles() async {
    setState(() {
      _isLoadingTiles = true;
    });

    try {
      // First try to load cached tiles
      List<Map<String, dynamic>> tiles = await DashboardTilesService.getFilteredDashboardTiles();
      
      if (tiles.isEmpty) {
        // If no cached tiles, fetch from API
        AppLogger.info('No cached tiles found, fetching from API...');
        bool success = await DashboardTilesService.fetchDashboardTiles();
        
        if (success) {
          // Reload tiles after fetching
          tiles = await DashboardTilesService.getFilteredDashboardTiles();
          AppLogger.info('After API fetch: ${tiles.length} tiles loaded');
        } else {
          AppLogger.info('API fetch failed - could not retrieve device configuration');
          throw Exception('API fetch failed');
        }
      }

      if (tiles.isEmpty) {
        throw Exception('No dashboard tiles found after API fetch');
      }

      setState(() {
        _dashboardTiles = tiles;
        // Only set default favorites if user hasn't customized them yet
        if (_favoriteServices.isEmpty) {
          // Convert tiles to favorite services format for backward compatibility
          _favoriteServices = tiles.take(6).map((tile) => {
            'id': tile['name'] ?? 'unknown',
            'label': tile['title'] ?? 'Unknown',
            'type': 'icon',
            'value': _getTileIcon(tile['name'] ?? ''),
            'serviceCode': tile['serviceCode'] ?? '',
            'tile': tile, // Store full tile data
          }).toList();
          AppLogger.info('Set default favorites from API tiles: ${_favoriteServices.length} items');
        } else {
          AppLogger.info('User has custom favorites, keeping existing selection: ${_favoriteServices.length} items');
        }
        _isLoadingTiles = false;
      });

      AppLogger.info('Loaded ${tiles.length} dashboard tiles');

    } catch (e) {
      AppLogger.error('loading dashboard tiles: $e. Using fallback data.');
      
      // Fallback to debug method to load hardcoded tiles
      await _debugRefreshTiles();
    }
  }

  /// Get icon for tile based on name
  IconData _getTileIcon(String tileName) {
    switch (tileName.toLowerCase()) {
      case 'withdrawal':
        return Icons.real_estate_agent_sharp;
      case 'deposit':
        return Icons.account_balance_wallet;
      case 'interaccounttransfer':
        return Icons.compare_arrows;
      case 'full statement':
        return Icons.receipt_long;
      case 'buyairtime':
        return Icons.payments_outlined;
      case 'checkeligibility':
        return Icons.check_circle;
      case 'applyloans':
        return Icons.pan_tool_alt_outlined;
      case 'repayloans':
        return Icons.credit_score_outlined;
      case 'ministatement':
        return Icons.payment;
      case 'balanceenquiry':
        return Icons.account_circle_outlined;
      case 'fosatobank':
        return Icons.other_houses_rounded;
      case 'payutility':
        return Icons.lightbulb_outlined;
      case 'registermerchant':
        return Icons.app_registration;
      default:
        return Icons.help_outline;
    }
  }

  /// Handle navigation for configurable tiles
  void _handleTileNavigation(Map<String, dynamic> service) {
    String serviceId = service['id'] ?? '';
    String serviceCode = service['serviceCode'] ?? '';
    Map<String, dynamic>? tileData = service['tile'];

    AppLogger.info("=== DASHBOARD TILE NAVIGATION: $serviceId (ServiceCode: $serviceCode) ===");

    // Check if tile has child services
    if (tileData != null && tileData.containsKey('childServices') && tileData['childServices'] != null) {
      List<dynamic> childServices = tileData['childServices'];
      AppLogger.info("Child services found for $serviceId. Count: ${childServices.length}");
      _showChildServicesDialog(tileData, childServices);
      return;
    }

    // If we have tile data with service code, use dynamic form
    if (tileData != null && serviceCode.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TransactionFormScreen(
            transactionType: service['label'] as String,
            serviceCode: serviceCode,
            accounts: _realAccounts,
          ),
        ),
      );
      return;
    }

    // Check if service has a dynamic form (service code mapping)
    if (_serviceCodeMapping.containsKey(serviceId)) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TransactionFormScreen(
            transactionType: service['label'] as String,
            serviceCode: _serviceCodeMapping[serviceId],
            accounts: _realAccounts,
          ),
        ),
      );
      return;
    }

    // Fallback to existing static screens for services without dynamic forms
    switch (serviceId) {
      case 'enquiries':
      case 'balanceenquiry':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const BalanceEnquiryScreen(),
          ),
        );
        break;
      case 'my_loans':
      case 'applyloans':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const LoanScreen()),
        );
        break;
      case 'apply_loan':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ApplyLoanScreen()),
        );
        break;
      case 'full_statement':
      case 'fullstatement':
                      if (mounted) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => const FullStatementScreen()),
                        );
                      }
        break;
      case 'ministatement':
                      if (mounted) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => const MiniStatementScreen()),
                        );
                      }
        break;
      case 'email_statement':
                      if (mounted) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => const FullStatementScreen()),
                        );
                      }
        break;
      case 'transfer_to_bank':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TransactionFormScreen(
              transactionType: 'Transfer to Bank',
              serviceCode: 'TTB',
              accounts: _realAccounts,
            ),
          ),
        );
        break;
      case 'pay_to_till':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TransactionFormScreen(
              transactionType: 'Pay to Till',
              serviceCode: 'PTT',
              accounts: _realAccounts,
            ),
          ),
        );
        break;
      case 'mpesa_float':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TransactionFormScreen(
              transactionType: 'Purchase Mpesa Float',
              serviceCode: 'MFL',
              accounts: _realAccounts,
            ),
          ),
        );
        break;
      case 'dstv':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TransactionFormScreen(
              transactionType: 'DSTV Payment',
              serviceCode: 'DSTV',
              accounts: _realAccounts,
            ),
          ),
        );
        break;
      case 'zuku':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TransactionFormScreen(
              transactionType: 'Zuku Payment',
              serviceCode: 'ZUKU',
              accounts: _realAccounts,
            ),
          ),
        );
        break;
      case 'startimes':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TransactionFormScreen(
              transactionType: 'Star Times Payment',
              serviceCode: 'STAR',
              accounts: _realAccounts,
            ),
          ),
        );
        break;
      case 'nairobi_water':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TransactionFormScreen(
              transactionType: 'Nairobi Water Payment',
              serviceCode: 'NWTR',
              accounts: _realAccounts,
            ),
          ),
        );
        break;
      default:
        AppLogger.info("Unknown tile tapped: $serviceId");
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Service "$serviceId" is not yet implemented'),
                              backgroundColor: Colors.orange,
                            ),
                          );
                        }
    }
  }

  /// Show child services dialog for tiles with sub-services
  void _showChildServicesDialog(Map<String, dynamic> parentTile, List<dynamic> childServices) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final isApplyLoansModal = (parentTile['name']?.toString().toLowerCase() == 'applyloans' || parentTile['title']?.toString().toLowerCase() == 'apply loans');
    
    // Get appropriate icon based on modal type
    IconData headerIcon;
    if (isApplyLoansModal) {
      headerIcon = Icons.pan_tool_alt_outlined;
    } else {
      headerIcon = Icons.help_outline;
    }
    
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '${parentTile['title'] ?? 'Service'} Modal',
      barrierColor: Colors.black.withValues(alpha: 0.3),
      pageBuilder: (context, anim1, anim2) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 6, sigmaY: 6),
          child: Center(
            child: Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: isDark ? Colors.grey[900] : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: ColorPalette.secondary,
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            headerIcon,
                            color: Colors.white,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              parentTile['title'] ?? 'Select Service',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: childServices.map((childService) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: _buildDashboardEnquiryOption(
                              context,
                              childService: childService,
                              onTap: () {
                                Navigator.pop(context); // Close dialog
                                _navigateToChildService(childService);
                              },
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      transitionBuilder: (context, anim1, anim2, child) {
        return FadeTransition(
          opacity: anim1,
          child: child,
        );
      },
    );
  }

  /// Build beautiful enquiry option for dashboard modal
  Widget _buildDashboardEnquiryOption(
    BuildContext context, {
    required Map<String, dynamic> childService,
    required VoidCallback onTap,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    // Get appropriate icon and subtitle based on service
    IconData icon;
    String subtitle;
    String title = childService['title'] ?? 'Service';
    String serviceCode = childService['serviceCode'] ?? '';
    
    // Handle inter-account transfer options
    if (serviceCode == 'IATOW') {
      title = 'Transfer to own accounts';
    } else if (serviceCode == 'IATOT') {
      title = 'Transfer to other members';
    }
    
    switch (serviceCode) {
      case 'SBE':
        icon = Icons.account_balance_wallet;
        subtitle = 'Check your savings account balance';
        break;
      case 'SCBE':
        icon = Icons.pie_chart;
        subtitle = 'Check your share capital balance';
        break;
      case 'NWDB':
        icon = Icons.savings;
        subtitle = 'Check your investment account balance';
        break;
      case 'LB':
        icon = Icons.monetization_on;
        subtitle = 'Check your loan balance';
        break;
      case 'IATOW':
        icon = Icons.swap_horiz;
        subtitle = '';
        break;
      case 'IATOT':
        icon = Icons.send;
        subtitle = '';
        break;
      case 'AL':
        icon = Icons.monetization_on;
        subtitle = 'Apply for E-Loan';
        break;
      case 'IDL':
        icon = Icons.phone_android;
        subtitle = 'Apply for Digital Loan';
        break;
      default:
        icon = Icons.help_outline;
        subtitle = 'Check your account balance';
    }
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? Colors.grey[800] : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: ColorPalette.secondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: ColorPalette.secondary,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  if (subtitle.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: isDark ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }

  /// Navigate to child service
  void _navigateToChildService(Map<String, dynamic> childService) {
    String serviceCode = childService['serviceCode'] ?? '';
    String title = childService['title'] ?? 'Service';

    AppLogger.info("=== CHILD SERVICE NAVIGATION: "+(childService['name']??'')+" (ServiceCode: $serviceCode) ===");

    // For deposit types, filter and lock account selection
    final depositCodes = {'STKSAVINGS', 'STKSHARES', 'STKLOANS', 'STK'};
    if (depositCodes.contains(serviceCode)) {
      // Determine account type by serviceCode
      String accountTypeKey;
      switch (serviceCode) {
        case 'STKSAVINGS':
          accountTypeKey = 'savings';
          break;
        case 'STKSHARES':
          accountTypeKey = 'share';
          break;
        case 'STKLOANS':
          accountTypeKey = 'loan';
          break;
        case 'STK':
          accountTypeKey = 'nwd';
          break;
        default:
          accountTypeKey = '';
      }
      // Find the relevant account
      final filtered = _realAccounts.where((acc) {
        final name = (acc['accountName']??'').toString().toLowerCase();
        if (accountTypeKey == 'savings') return name.contains('savings') || acc['isSavingsAccount'] == 'Yes';
        if (accountTypeKey == 'share') return name.contains('share') || acc['isShareCapital'] == 'Yes';
        if (accountTypeKey == 'loan') return name.contains('loan') || acc['isLoanAccount'] == 'Yes';
        if (accountTypeKey == 'nwd') return name.contains('nwd') || name.contains('deposit') || acc['isNWD'] == 'Yes';
        return false;
      }).toList();
      final lockedAccounts = filtered.isNotEmpty ? filtered : _realAccounts.take(1).toList();
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TransactionFormScreen(
            transactionType: title,
            serviceCode: serviceCode,
            accounts: lockedAccounts,
            tileResponse: childService,
            lockAccountSelection: true,
          ),
        ),
      );
      return;
    }

    // Special handling for inter-account transfers
    if (serviceCode == 'IATOW' || serviceCode == 'IATOT') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => FormJourneyScreen(
            serviceCode: serviceCode,
            accounts: _realAccounts,
            tileResponse: childService,
          ),
        ),
      );
      return;
    }

    if (serviceCode.isNotEmpty) {
      // Check if this is a balance enquiry service - use FormJourneyScreen for beautiful UI
      Set<String> balanceEnquiryCodes = {'SCBE', 'SBE', 'NWDB', 'LB', 'ASB'};
      if (balanceEnquiryCodes.contains(serviceCode)) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => FormJourneyScreen(
              serviceCode: serviceCode,
              accounts: _realAccounts,
            ),
          ),
        );
      } else {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TransactionFormScreen(
              transactionType: title,
              serviceCode: serviceCode,
              accounts: _realAccounts,
              tileResponse: childService,
            ),
          ),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Service code not found for $title'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  /// Refresh tiles from API by clearing cache and fetching fresh data
  Future<void> _refreshTilesFromAPI() async {
    AppLogger.info("=== REFRESHING TILES FROM API ===");
    
    // Clear cached tiles first
    await DashboardTilesService.clearCachedTiles();
    
    // Reload tiles (this will force an API call)
    await _loadDashboardTiles();
  }

  /// Debug method to test API call and refresh tiles
  Future<void> _debugRefreshTiles() async {
    AppLogger.info("=== DEBUG: Refreshing dashboard tiles ===");
    
    // Clear cached tiles first
    await DashboardTilesService.clearCachedTiles();
    
    // Show loading
    setState(() {
      _isLoadingTiles = true;
    });

    try {
      // Save the provided tiles data manually (for testing)
      List<dynamic> testTiles = [
        {
          "position": 1,
          "tab": "enquiries",
          "name": "balanceEnquiry",
          "title": "Balance Enquiry",
          "serviceCode": "SBE",
          "icon": "balanceEnquiry",
          "childServices": [
            {
              "position": 1,
              "tab": "enquiries",
              "name": "savingsBalance",
              "title": "Ordinary Savings",
              "serviceCode": "SBE",
              "icon": "balanceEnquiry"
            },
            {
              "position": 2,
              "tab": "enquiries",
              "name": "shareBalance",
              "title": "Share Capital",
              "serviceCode": "SCBE",
              "icon": "shareBalance"
            },
            {
              "position": 3,
              "tab": "enquiries",
              "name": "nwdBalance",
              "title": "Share deposit",
              "serviceCode": "NWDB",
              "icon": "nwdBalance"
            },
            {
              "position": 4,
              "tab": "enquiries",
              "name": "loanBalance",
              "title": "Loan Balance",
              "serviceCode": "LB",
              "icon": "loanBalance"
            }
          ]
        },
        {
          "position": 5,
          "tab": "enquiries",
          "name": "miniStatement",
          "title": "Mini Statement",
          "serviceCode": "MS",
          "icon": "miniStatement"
        },
        {
          "position": 6,
          "tab": "enquiries",
          "name": "emailStatement",
          "title": "Statement to Email",
          "serviceCode": "AS",
          "icon": "miniStatement"
        },
        {
          "position": 1,
          "tab": "transactions",
          "name": "withdrawal",
          "title": "Withdrawal",
          "serviceCode": "WD",
          "icon": "withdrawal"
        },
        {
          "position": 2,
          "tab": "transactions",
          "name": "interAccountTransfer",
          "title": "Inter Account",
          "serviceCode": "STKLOANS",
          "icon": "interAccountTransfer",
          "childServices": [
            {
              "position": 1,
              "tab": "transactions",
              "name": "inter account transfer",
              "title": "Transfer to own",
              "serviceCode": "IATOW",
              "icon": "deposit_to_savings_icon"
            },
            {
              "position": 2,
              "tab": "transactions",
              "name": "transfer_to_other",
              "title": "Transfer to other",
              "serviceCode": "IATOT",
              "icon": "share_contribution_icon"
            }
          ]
        },
        {
          "position": 3,
          "tab": "transactions",
          "name": "deposit",
          "title": "Deposit",
          "serviceCode": "STKLOANS",
          "icon": "deposit",
          "childServices": [
            {
              "position": 1,
              "tab": "transactions",
              "name": "deposit",
              "title": "Savings Deposit",
              "serviceCode": "STKSAVINGS",
              "icon": "deposit_to_savings_icon"
            },
            {
              "position": 2,
              "tab": "transactions",
              "name": "deposit",
              "title": "Shares Deposit",
              "serviceCode": "STKSHARES",
              "icon": "share_contribution_icon"
            },
            {
              "position": 3,
              "tab": "transactions",
              "name": "deposit",
              "title": "Repay Loan",
              "serviceCode": "STKLOANS",
              "icon": "repayLoans"
            },
            {
              "position": 4,
              "tab": "transactions",
              "name": "deposit",
              "title": "NWD Deposit",
              "serviceCode": "STK",
              "icon": "nwdBalance"
            }
          ]
        },
        {
          "position": 4,
          "tab": "transactions",
          "name": "buyAirtime",
          "title": "Buy Airtime",
          "serviceCode": "AT",
          "icon": "buyAirtime"
        },
        {
          "position": 5,
          "tab": "transactions",
          "name": "payUtility",
          "title": "Pay Utility Bills",
          "serviceCode": "UTILITYSERVICES",
          "icon": "payUtility"
        },
        {
          "position": 7,
          "tab": "transactions",
          "name": "fosaToBank",
          "title": "Fosa To Bank",
          "serviceCode": "FOSATOBANK",
          "icon": "fosaToBank"
        },
        {
          "position": 1,
          "tab": "loans",
          "name": "applyLoans",
          "title": "Apply Loans",
          "serviceCode": "AL",
          "icon": "applyLoans",
          "childServices": [
            {
              "position": 1,
              "name": "Apply Loans",
              "title": "E-Loan",
              "serviceCode": "AL",
              "icon": "applyLoans"
            },
            {
              "position": 2,
              "name": "Apply Loans",
              "title": "Digital Loan",
              "serviceCode": "IDL",
              "icon": "applyLoans",
              "childServices": [
                {
                  "position": 1,
                  "name": "Fosa Loan",
                  "title": "FOSA LOAN",
                  "serviceCode": "IDL",
                  "eLoanCode": "FL351",
                  "icon": "applyLoans"
                },
                {
                  "position": 2,
                  "name": "Fanikisha Loan",
                  "title": "FANIKISHA LOAN",
                  "serviceCode": "IDL",
                  "eLoanCode": "FL352",
                  "icon": "applyLoans"
                }
              ]
            }
          ]
        },
        {
          "position": 3,
          "tab": "loans",
          "name": "repayLoans",
          "title": "Repay Loan",
          "serviceCode": "STKLOANS",
          "icon": "repayLoans"
        },
        {
          "position": 4,
          "tab": "loans",
          "name": "myDigitalLoans",
          "title": "My Loans",
          "serviceCode": "STKLOANS",
          "icon": "myLoans",
          "childServices": [
            {
              "position": 1,
              "name": "My Loans",
              "title": "Active Loans",
              "serviceCode": "DL",
              "icon": "applyLoans"
            },
            {
              "position": 2,
              "name": "Pending Loans",
              "title": "Pending Loans",
              "serviceCode": "PL",
              "icon": "applyLoans"
            }
          ]
        },
        {
          "position": 5,
          "tab": "loans",
          "name": "approveLoans",
          "title": "Approve Loans",
          "serviceCode": "APPROVELOANS",
          "icon": "digital_loan"
        }
      ];

      bool success = await DashboardTilesService.saveManualTilesData(testTiles);
      AppLogger.info("Manual tiles save result: $success");
      
      if (success) {
        // Reload tiles
        await _loadDashboardTiles();
        
      } else {
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Failed to save tiles data'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
      }
    } catch (e) {
      AppLogger.error("refreshing tiles: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error refreshing tiles: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoadingTiles = false;
      });
    }
  }

  /// Initialize accounts from cache or API
  Future<void> _initializeAccounts() async {
    try {
      // Try to get cached data first
      final cachedName = await AccountManager.getCachedMemberName();
      final cachedAccounts = await AccountManager.getCachedAccounts();

      // If we have valid cached data and aren't forcing a refresh
      if (cachedName != null && cachedAccounts != null && !_isRefreshing) {
        setState(() {
          _realAccounts = cachedAccounts;
          _memberName = cachedName;
          _isLoadingAccounts = false;
        });
        return;
      }

      // No valid cache or forcing refresh - fetch fresh data
      await _fetchAccounts();
    } catch (e) {
      AppLogger.error('initializing accounts: $e');
      // On any error, try direct fetch
      await _fetchAccounts();
    }
  }

  /// Load user's favorite services from SharedPreferences
  Future<void> _loadFavoriteServices() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? favoritesJson = prefs.getString('favorite_services');

      if (favoritesJson != null) {
        List<dynamic> favoritesList = jsonDecode(favoritesJson);
        setState(() {
          _favoriteServices = favoritesList.cast<Map<String, dynamic>>();
        });
        AppLogger.info('Loaded ${_favoriteServices.length} favorite services from preferences');
      } else {
        AppLogger.info('No saved favorite services found, will use default tiles');
      }
    } catch (e) {
      AppLogger.error('loading favorite services: $e');
    }
  }

  /// Save user's favorite services to SharedPreferences
  Future<void> _saveFavoriteServices() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String favoritesJson = jsonEncode(_favoriteServices);
      await prefs.setString('favorite_services', favoritesJson);
      AppLogger.info('Saved ${_favoriteServices.length} favorite services to preferences');
    } catch (e) {
      AppLogger.error('saving favorite services: $e');
    }
  }

  /// Load latest transactions from API
  Future<void> _loadLatestTransactions() async {
    setState(() {
      _isLoadingTransactions = true;
    });

    try {
      List<TransactionModel> transactions = await LatestTransactionsService.fetchLatestTransactions();

      setState(() {
        _transactions = transactions;
        _isLoadingTransactions = false;
      });

      // Start periodic refresh if we only have balance enquiries (suggesting recent transactions might be pending)
      if (transactions.isNotEmpty && transactions.every((t) => t.amount == 0)) {
        _startPeriodicRefresh();
      }
    } catch (e) {
      AppLogger.error('loading latest transactions: $e');
      setState(() {
        _transactions = [];
        _isLoadingTransactions = false;
      });
    }
  }

  /// Start periodic refresh for pending transactions
  void _startPeriodicRefresh() {
    // Cancel any existing timer
    _transactionRefreshTimer?.cancel();

    // Start a timer that refreshes every 30 seconds for up to 5 minutes
    int refreshCount = 0;
    const maxRefreshes = 10; // 10 * 30 seconds = 5 minutes

    _transactionRefreshTimer = Timer.periodic(Duration(seconds: 30), (timer) async {
      // If the widget is no longer in the tree, stop the timer.
      if (!mounted) {
        timer.cancel();
        return;
      }
      
      refreshCount++;

      try {
        List<TransactionModel> transactions = await LatestTransactionsService.refreshTransactions();

        if (mounted) {
          setState(() {
            _transactions = transactions;
          });
        }

        // Stop auto-refresh if we found monetary transactions or reached max attempts
        bool hasMonetaryTransactions = transactions.any((t) => t.amount != 0);
        if (hasMonetaryTransactions || refreshCount >= maxRefreshes) {
          timer.cancel();
        }
      } catch (e) {
        if (refreshCount >= maxRefreshes) {
          timer.cancel();
        }
      }
    });
  }

  /// Get available services from dashboard tiles for selection
  List<Map<String, dynamic>> _getAvailableServicesFromTiles() {
    // Start with API tiles
    List<Map<String, dynamic>> availableServices = _dashboardTiles.map((tile) => {
      'id': tile['name'] ?? 'unknown',
      'label': tile['title'] ?? 'Unknown',
      'type': 'icon',
      'value': _getTileIcon(tile['name'] ?? ''),
      'serviceCode': tile['serviceCode'] ?? '',
      'tile': tile, // Store full tile data
    }).toList();

    // Add additional services from services screen that might not be in API tiles
    List<Map<String, dynamic>> additionalServices = [
      // General Transactions
      {'id': 'email_statement', 'label': 'Email statement', 'type': 'icon', 'value': Icons.email, 'serviceCode': 'EMAIL_STMT'},

      // Loans
      {'id': 'my_loans', 'label': 'My loans', 'type': 'icon', 'value': Icons.request_quote_rounded, 'serviceCode': 'MY_LOANS'},

      // B2B Services
      {'id': 'transfer_to_bank', 'label': 'Transfer to bank', 'type': 'image', 'value': 'assets/images/transfer_to_bank.png', 'serviceCode': 'TTB'},
      {'id': 'pay_to_till', 'label': 'Pay to till', 'type': 'image', 'value': 'assets/images/safaricom-payto_till.png', 'serviceCode': 'PTT'},
      {'id': 'mpesa_float', 'label': 'Purchase mpesa float', 'type': 'image', 'value': 'assets/images/mpesa.png', 'serviceCode': 'MFL'},

      // Utility Bills
      {'id': 'dstv', 'label': 'Dstv', 'type': 'image', 'value': 'assets/images/dstv.png', 'serviceCode': 'DSTV'},
      {'id': 'zuku', 'label': 'Zuku', 'type': 'image', 'value': 'assets/images/zuku.png', 'serviceCode': 'ZUKU'},
      {'id': 'startimes', 'label': 'Star times', 'type': 'image', 'value': 'assets/images/startimes.png', 'serviceCode': 'STAR'},
      {'id': 'nairobi_water', 'label': 'Nairobi water', 'type': 'image', 'value': 'assets/images/nairobi_water.png', 'serviceCode': 'NWTR'},
    ];

    // Add additional services that are not already in the API tiles
    for (var additionalService in additionalServices) {
      bool alreadyExists = availableServices.any((service) => service['id'] == additionalService['id']);
      if (!alreadyExists) {
        availableServices.add(additionalService);
      }
    }

    AppLogger.info('Total available services for selection: ${availableServices.length}');
    return availableServices;
  }

  /// Fetch accounts from API
  Future<void> _fetchAccounts() async {
    setState(() {
      _isLoadingAccounts = true;
    });

    try {
      ApiService apiService = ApiService();
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? phoneNumber = prefs.getString('msisdn');

      // Early return if missing phone number
      if (phoneNumber == null) {
        setState(() {
          _isLoadingAccounts = false;
        });
        AppLogger.error('Phone number is null');
        return;
      }

      var clientId = await SharedPreferencesHelper.getClientId();

      var requestBody = {
        'phoneNumber': phoneNumber,
        'clientId': clientId,
      };

      AppLogger.info('Fetching accounts with request body: $requestBody');
      
      var response = await apiService.postRequest(
        ApiEndpoints.accounts,
        requestBody,
      );

      AesEncryption aesEncryption = AesEncryption();
      String? secretKey = prefs.getString('_tajemnica');

      if (secretKey == null) {
        throw Exception("Secret key is null");
      }

      String decrypt = aesEncryption.decryptWithBase64Key(
          response['hashedBody'], secretKey);
      
      AppLogger.info("Fetched Accounts >>> $decrypt");

      var jsonResponse = jsonDecode(decrypt);
      var fetchedAccounts = jsonResponse['accounts']['accounts'];
      var fetchedMemberName = jsonResponse['accounts']['member']['name'].trim();

      // Cache both the accounts and member name
      await AccountManager.saveAccounts(fetchedAccounts, fetchedMemberName);

      // Validate that accounts aren't empty
      if (fetchedAccounts == null || (fetchedAccounts is List && fetchedAccounts.isEmpty)) {
        AppLogger.warning('Fetched accounts is empty or null');
        // Try to use cache instead
        final cachedAccounts = await AccountManager.getCachedAccounts();
        if (cachedAccounts != null && cachedAccounts.isNotEmpty) {
          setState(() {
            _realAccounts = cachedAccounts;
            _isLoadingAccounts = false;
          });
          return;
        }
      }

      // Update state with fetched accounts
      if (fetchedAccounts != null) {
        setState(() {
          _realAccounts = fetchedAccounts.where((account) =>
              account != null && account.isNotEmpty).toList();
          _memberName = fetchedMemberName;
          _isLoadingAccounts = false;
        });
      }
    } catch (e) {
      AppLogger.error('fetching accounts: $e');

      // Try to recover with cached data
      final cachedAccounts = await AccountManager.getCachedAccounts();
      final cachedName = await AccountManager.getCachedMemberName();

      setState(() {
        _isLoadingAccounts = false;
        if (cachedAccounts != null && cachedAccounts.isNotEmpty) {
          _realAccounts = cachedAccounts;
        }
        if (cachedName != null) {
          _memberName = cachedName;
        }
      });

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to fetch accounts. Using cached data.'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  /// Refresh accounts
  Future<void> _refreshAccounts() async {
    setState(() {
      _isRefreshing = true;
    });

    try {
      await _fetchAccounts();
    } finally {
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  /// Format balance for display
  String _formatBalance(dynamic balance) {
    if (balance == null) return '0.00';
    
    double balanceValue;
    if (balance is String) {
      balanceValue = double.tryParse(balance.replaceAll(',', '')) ?? 0.0;
    } else if (balance is num) {
      balanceValue = balance.toDouble();
    } else {
      balanceValue = 0.0;
    }
    
    return balanceValue.toStringAsFixed(2).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }

  /// Get account color based on account type
  Color _getAccountColor(Map<String, dynamic> account) {
    String accountType = account['accountType']?.toString() ?? '';
    String accountName = account['accountName']?.toString().toLowerCase() ?? '';
    
    if (accountName.contains('savings') || account['isSavingsAccount'] == 'Yes') {
      return ColorPalette.secondary;
    } else if (accountName.contains('share') || account['isShareCapital'] == 'Yes') {
      return ColorPalette.primary;
    } else if (accountName.contains('loan') || account['isLoanAccount'] == 'Yes') {
      return Colors.teal;
    } else if (accountName.contains('nwd') || accountName.contains('deposit') || account['isNWD'] == 'Yes') {
      return Colors.purple; // Purple for deposit accounts
    }
    
    // Default colors based on account type
    switch (accountType) {
      case '1': return Colors.teal;     // Loan
      case '2': return Colors.purple;   // NWD/Deposit
      case '3': return ColorPalette.secondary; // Savings
      case '4': return ColorPalette.primary;   // Shares
      default: return ColorPalette.primary;
    }
  }

  /// Get account icon based on account type
  IconData _getAccountIcon(Map<String, dynamic> account) {
    String accountName = account['accountName']?.toString().toLowerCase() ?? '';
    
    if (accountName.contains('savings') || account['isSavingsAccount'] == 'Yes') {
      return Icons.account_balance_rounded;
    } else if (accountName.contains('share') || account['isShareCapital'] == 'Yes') {
      return Icons.pie_chart_rounded;
    } else if (accountName.contains('loan') || account['isLoanAccount'] == 'Yes') {
      return Icons.monetization_on_rounded;
    } else if (accountName.contains('nwd') || account['isNWD'] == 'Yes') {
      return Icons.savings_rounded;
    }
    
    return Icons.account_balance_wallet_rounded;
  }

  /// Get allowed actions based on account properties
  List<String> _getAccountActions(Map<String, dynamic> account) {
    List<String> actions = [];
    
    // Check if account can deposit
    if (account['canDeposit'] == 'Yes' || account['canDeposit'] == true) {
      actions.add('deposit');
    }
    
    // Check if account can withdraw
    if (account['canWithdraw'] == 'Yes' || account['canWithdraw'] == true) {
      actions.add('withdraw');
    }
    
    // For savings accounts, typically allow inter-account transfers
    if (account['isSavingsAccount'] == 'Yes' || account['isSavingsAccount'] == true) {
      actions.addAll(['inter_account', 'statements']);
    }
    
    // All accounts should support statements
    if (!actions.contains('statements')) {
      actions.add('statements');
    }
    
    // If no specific actions defined, add deposit as default
    if (actions.isEmpty) {
      actions.add('deposit');
    }
    
    return actions;
  }

  /// Get greeting message based on time of day
  String _getGreeting() {
    final hour = DateTime.now().hour;
    String greeting;
    String emoji;
    
    if (hour < 12) {
      greeting = 'Good morning';
      emoji = '🌅';
    } else if (hour < 17) {
      greeting = 'Good afternoon';
      emoji = '☀️';
    } else {
      greeting = 'Good evening';
      emoji = '🌙';
    }
    
    // Use string interpolation
    return '$greeting $emoji !';
  }


  Widget _buildDashboard() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return RefreshIndicator(
      onRefresh: _refreshAccounts,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(parent: BouncingScrollPhysics()),
        child: Container(
          color: isDark ? Theme.of(context).scaffoldBackgroundColor : Colors.grey[200],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: ColorPalette.secondary,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Consumer<ProfileImageProvider>(
                          builder: (context, profileImageProvider, child) {
                            return GestureDetector(
                              onTap: () {
                                _pageController.animateToPage(
                                  3, // Profile screen index
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              },
                              child: CircleAvatar(
                                radius: 25,
                                backgroundColor: Colors.white.withValues(alpha: 0.2),
                                child: ClipOval(
                                  child: profileImageProvider.profileImage != null
                                      ? Image.file(
                                          profileImageProvider.profileImage!,
                                          width: 50,
                                          height: 50,
                                          fit: BoxFit.cover,
                                          errorBuilder: (context, error, stackTrace) {
                                            AppLogger.error('Profile image display error: $error');
                                            return Icon(Icons.person, size: 30, color: Colors.white);
                                          },
                                        )
                                      : Icon(Icons.person, size: 30, color: Colors.white),
                                ),
                              ),
                            );
                          },
                        ),
                        const SizedBox(width: 15),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: '${_getGreeting().split(' ')[0]} ',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  TextSpan(
                                    text: _getGreeting().split(' ')[1],
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                  WidgetSpan(
                                    child: Padding(
                                      padding: const EdgeInsets.only(left: 4),
                                      child: Text(
                                        _getGreeting().split(' ')[2],
                                        style: const TextStyle(
                                          fontSize: 16,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _memberName.isNotEmpty ? _memberName : widget.userName,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: Icon(Icons.notifications_outlined, color: Colors.white),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => const NotificationsScreen()),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 5),
                    Text(
                      'My Accounts',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: ColorPalette.primary,
                      ),
                    ),
                    const SizedBox(height: 15),
                    // Show loading state or accounts carousel
                    _isLoadingAccounts 
                      ? _buildLoadingAccountsCarousel()
                      : _accounts.isEmpty
                        ? _buildNoAccountsMessage()
                        : Column(
                            children: [
                              CarouselSlider.builder(
                                itemCount: _accounts.length,
                                itemBuilder: (context, index, realIndex) {
                                  return _buildAccountCard(_accounts[index]);
                                },
                                options: CarouselOptions(
                                  height: 280,
                                  viewportFraction: 0.95,
                                  enlargeCenterPage: true,
                                  enableInfiniteScroll: _accounts.length > 1,
                                  onPageChanged: (index, reason) {
                                    setState(() {
                                      _currentAccountIndex = index;
                                    });
                                  },
                                ),
                              ),
                              const SizedBox(height: 4),
                              if (_accounts.length > 1)
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: _accounts.asMap().entries.map((entry) {
                                    return Container(
                                      width: 8.0,
                                      height: 8.0,
                                      margin: const EdgeInsets.symmetric(horizontal: 4.0),
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: _currentAccountIndex == entry.key
                                            ? ColorPalette.primary
                                            : Colors.grey.withValues(alpha: 0.3),
                                      ),
                                    );
                                  }).toList(),
                                ),
                            ],
                          ),
                    const SizedBox(height: 30),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Favorites',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: ColorPalette.primary,
                          ),
                        ),
                        Row(
                          children: [
                            TextButton.icon(
                              onPressed: _showEditFavoritesDialog,
                              icon: Icon(
                                Icons.edit,
                                color: ColorPalette.primary,
                                size: 18,
                              ),
                              label: Text(
                                'Edit',
                                style: TextStyle(
                                  color: ColorPalette.primary,
                                ),
                              ),
                            ),
                            TextButton.icon(
                              onPressed: _refreshTilesFromAPI,
                              icon: Icon(
                                Icons.refresh,
                                color: ColorPalette.secondary,
                                size: 18,
                              ),
                              label: Text(
                                'Refresh',
                                style: TextStyle(
                                  color: ColorPalette.secondary,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 15),
                    // Show loading state or favorites grid
                    _isLoadingTiles 
                      ? _buildLoadingFavoritesGrid()
                      : _favoriteServices.isEmpty
                        ? _buildNoFavoritesMessage()
                        : GridView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _favoriteServices.length > 6 ? 6 : _favoriteServices.length, // Limit to 6 items
                            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2, // 2 columns
                              childAspectRatio: 2.2, // Adjust aspect ratio for wider tiles
                              crossAxisSpacing: 12, // Spacing between columns
                              mainAxisSpacing: 12, // Spacing between rows
                            ),
                            itemBuilder: (context, index) {
                              final service = _favoriteServices[index];
                              return _buildServiceItem(service); // Pass service data
                            },
                          ),
                    const SizedBox(height: 30),
                    _buildRecentTransactions(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAccountCard(AccountModel account) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: account.backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    account.name,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: ColorPalette.textLight,
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(
                    account.icon,
                    color: ColorPalette.textLight,
                  ),
                  onPressed: () {},
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Available balance',
              style: TextStyle(
                fontSize: 14,
                color: ColorPalette.textLight,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Flexible(
                        child: Text(
                          _isBalanceVisible ? account.balance : '**** ',
                          style: const TextStyle(
                            fontSize: 26,
                            fontWeight: FontWeight.bold,
                            color: ColorPalette.textLight,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        ' KES',
                        style: TextStyle(
                          fontSize: 26,
                          fontWeight: FontWeight.bold,
                          color: ColorPalette.textLight,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(
                    _isBalanceVisible ? Icons.visibility : Icons.visibility_off,
                    color: ColorPalette.textLight,
                  ),
                  onPressed: () {
                    setState(() {
                      _isBalanceVisible = !_isBalanceVisible;
                    });
                  },
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                    if (account.allowedActions.contains('deposit'))
                      _buildAccountAction(Icons.account_balance_wallet, 'Deposit', () {
                        // Pass only the selected account, not all accounts
                        List<dynamic> selectedAccountOnly = [account.rawAccount];
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => TransactionFormScreen(
                              transactionType: 'Deposit',
                              serviceCode: 'STKSAVINGS',
                              accounts: selectedAccountOnly,
                            ),
                          ),
                        );
                      }),
                    if (account.allowedActions.contains('withdraw'))
                      _buildAccountAction(Icons.payments, 'Withdraw', () {
                        // Pass only the selected account, not all accounts
                        List<dynamic> selectedAccountOnly = [account.rawAccount];
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => TransactionFormScreen(
                              transactionType: 'Withdraw',
                              serviceCode: 'WD',
                              accounts: selectedAccountOnly,
                            ),
                          ),
                        );
                      }),
                    if (account.allowedActions.contains('inter_account'))
                      _buildAccountAction(Icons.swap_horiz, 'Inter account', () {
                        // Find the 'interAccountTransfer' tile from the loaded dashboard tiles
                        final interAccountTile = _dashboardTiles.firstWhere(
                          (tile) => tile['name'] == 'interAccountTransfer',
                          orElse: () => <String, dynamic>{}, // Return an empty map if not found
                        );

                        if (interAccountTile.isNotEmpty &&
                            interAccountTile['childServices'] != null &&
                            (interAccountTile['childServices'] as List).isNotEmpty) {
                          // If the tile and its children are found, show the dialog
                          _showChildServicesDialog(interAccountTile, interAccountTile['childServices']);
                        } else {
                          // Fallback if the service isn't configured properly
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Inter-account transfer service is not available.'),
                              backgroundColor: Colors.orange,
                            ),
                          );
                        }
                      }),
                    if (account.allowedActions.contains('statements'))
                      _buildAccountAction(Icons.description, 'Statements', () {
                        _showStatementOptions(context);
                      }),
                  ],
                ),
              ),
            ],
        ),
      ),
    );
  }

  Widget _buildAccountAction(IconData icon, String label, VoidCallback onTap) {
    return Padding(
      padding: const EdgeInsets.only(right: 16),
      child: InkWell(
        onTap: onTap,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: ColorPalette.textLight,
                size: 20,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 11,
                color: ColorPalette.textLight,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceItem(Map<String, dynamic> service) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    void handleTileNavigation() => _handleTileNavigation(service);

    return InkWell(
      onTap: handleTileNavigation,
      child: Container(
        padding: const EdgeInsets.all(12), // Add padding
        decoration: BoxDecoration(
          color: isDark ? Colors.black : Colors.white, // White background for light mode
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            if (!isDark)
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
          ],
        ),
        child: Row( // Use Row for horizontal layout
          children: [
            // Icon/Image Container
            Container(
              width: 40, // Consistent size for icon background
              height: 40,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                // Use a slightly different color for the icon background if needed
                color: isDark ? Colors.black : Colors.pink.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: service['type'] == 'icon'
                  ? Icon(
                      service['value'] as IconData,
                      color: isDark ? Colors.white70 : ColorPalette.secondary, // Reverted to secondary color for light mode
                      size: 20,
                    )
                  : Image.asset(
                      service['value'] as String,
                      // No specific width/height needed if using fit
                    ),
            ),
            const SizedBox(width: 12), // Space between icon and text
            // Text Column
            Expanded( // Allow text to take remaining space
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start, // Align text left
                mainAxisAlignment: MainAxisAlignment.center, // Center text vertically in the row
                children: [
                  Text(
                    service['label'] as String,
                    style: TextStyle(
                      fontSize: 14, // Slightly larger title font
                      fontWeight: FontWeight.bold, // Bold title
                      color: isDark ? Colors.white : ColorPalette.textDark,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactions() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Container(
      decoration: BoxDecoration(
        color: isDark ? ColorPalette.black : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Transactions',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: ColorPalette.primary,
                ),
              ),
              Row(
                children: [
                  // Refresh button
                  GestureDetector(
                    onTap: () async {
                      // Force refresh transactions (bypass cache)
                      setState(() {
                        _isLoadingTransactions = true;
                      });

                      try {
                        List<TransactionModel> transactions = await LatestTransactionsService.refreshTransactions();

                        setState(() {
                          _transactions = transactions;
                          _isLoadingTransactions = false;
                        });

                        // Show info message about transaction delays
                        if (mounted && transactions.isNotEmpty && transactions.every((t) => t.amount == 0)) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Monetary transactions (withdrawals, deposits) may take several minutes to appear in transaction history due to processing requirements. Balance enquiries appear immediately.',
                                style: TextStyle(fontSize: 12),
                              ),
                              duration: Duration(seconds: 6),
                              backgroundColor: Colors.orange,
                              action: SnackBarAction(
                                label: 'OK',
                                textColor: Colors.white,
                                onPressed: () {
                                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                                },
                              ),
                            ),
                          );
                        }
                      } catch (e) {
                        setState(() {
                          _isLoadingTransactions = false;
                        });
                      }
                    },
                    child: Icon(
                      Icons.refresh,
                      size: 20,
                      color: ColorPalette.primary,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Loading state
          if (_isLoadingTransactions)
            _buildTransactionsLoadingState()
          // Empty state
          else if (_transactions.isEmpty)
            _buildNoTransactionsState()
          // Transactions list
          else
            Column(
              children: [
                // Info message for balance enquiry only transactions
                if (_transactions.isNotEmpty && _transactions.every((t) => t.amount == 0))
                  Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue, size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Monetary transactions may take time to process and appear here.',
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.blue[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                for (int i = 0; i < _transactions.length && i < 3; i++) ...[
                  _buildTransactionCard(_transactions[i]),
                  if (i < _transactions.length - 1 && i < 2) const Divider(height: 1),
                ],
              ],
            ),
        ],
      ),
    );
  }

  /// Build loading state for transactions
  Widget _buildTransactionsLoadingState() {
    return Column(
      children: [
        for (int i = 0; i < 3; i++) ...[
          _buildTransactionLoadingSkeleton(),
          if (i < 2) const Divider(height: 1),
        ],
      ],
    );
  }

  /// Build loading skeleton for individual transaction
  Widget _buildTransactionLoadingSkeleton() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          // Icon skeleton
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[700] : Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          const SizedBox(width: 12),
          // Text skeleton
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  height: 14,
                  decoration: BoxDecoration(
                    color: isDark ? Colors.grey[700] : Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  width: 120,
                  height: 12,
                  decoration: BoxDecoration(
                    color: isDark ? Colors.grey[700] : Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
          // Amount skeleton
          Container(
            width: 80,
            height: 14,
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[700] : Colors.grey[300],
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  /// Build empty state for transactions
  Widget _buildNoTransactionsState() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 48,
              color: isDark ? Colors.grey[600] : Colors.grey[400],
            ),
            const SizedBox(height: 8),
            Text(
              'No recent transactions',
              style: TextStyle(
                fontSize: 14,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Your recent transactions will appear here',
              style: TextStyle(
                fontSize: 12,
                color: isDark ? Colors.grey[500] : Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build transaction card using TransactionModel
  Widget _buildTransactionCard(TransactionModel transaction) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          // Transaction Icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isDark
                  ? Colors.grey.shade800.withValues(alpha: 0.5)
                  : transaction.amount == 0
                      ? Colors.blue.withValues(alpha: 0.1)
                      : transaction.isPositive
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              transaction.amount == 0
                  ? Icons.info_outline
                  : transaction.isPositive
                      ? Icons.add_circle_outline
                      : Icons.remove_circle_outline,
              color: transaction.amount == 0
                  ? Colors.blue
                  : transaction.isPositive
                      ? Colors.green
                      : Colors.red,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),

          // Transaction Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  transaction.formattedDate,
                  style: TextStyle(
                    fontSize: 12,
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // Amount
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isDark
                  ? (transaction.amount == 0
                      ? Colors.blue.withValues(alpha: 0.2)
                      : transaction.isPositive
                          ? Colors.green.withValues(alpha: 0.2)
                          : Colors.red.withValues(alpha: 0.2))
                  : (transaction.amount == 0
                      ? Colors.blue.withValues(alpha: 0.1)
                      : transaction.isPositive
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.red.withValues(alpha: 0.1)),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              transaction.formattedAmount,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: transaction.amount == 0
                    ? Colors.blue
                    : transaction.isPositive
                        ? Colors.green
                        : Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }


  void _showStatementOptions(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: 'Statement Options',
      barrierColor: Colors.black.withValues(alpha: 0.3),
      pageBuilder: (context, anim1, anim2) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 6, sigmaY: 6),
          child: Center(
            child: Dialog(
              backgroundColor: Colors.transparent,
              insetPadding: EdgeInsets.zero,
              child: Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isDark ? Colors.grey[900] : Colors.grey[100],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: ColorPalette.unselectedNavItemColor,
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                      ),
                      child: Center(
                        child: Text(
                          'Statement Options',
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          _buildStatementOption(
                            context,
                            'Mini Statement',
                            Icons.receipt_outlined,
                            () {
                              Navigator.pop(context);
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const MiniStatementScreen(),
                                ),
                              );
                            },
                          ),
                          const SizedBox(height: 12),
                          _buildStatementOption(
                            context,
                            'Full Statement',
                            Icons.description_outlined,
                            () {
                              Navigator.pop(context);
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const FullStatementScreen(),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      transitionBuilder: (context, anim1, anim2, child) {
        return FadeTransition(
          opacity: anim1,
          child: child,
        );
      },
    );
  }

  Widget _buildStatementOption(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? Colors.white.withValues(alpha: 0.05) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isDark ? Colors.white24 : Colors.grey.shade200,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: ColorPalette.secondary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: ColorPalette.secondary,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isDark ? Colors.white : ColorPalette.textDark,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.chevron_right,
              color: isDark ? Colors.white70 : Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }

  /// Handle user interaction for token refresh
  void _onUserInteraction([_]) {
    // Reset the token refresh timer on any user interaction
    TokenRefreshService.instance.resetInactivityTimer();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) async {
        if (didPop) return;
        
        final bool shouldPop = await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              elevation: 10,
              backgroundColor: Colors.transparent,
              child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[900]
                    : Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Icon
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.exit_to_app_rounded,
                      size: 40,
                      color: Colors.red[600],
                    ),
                  ),
                  const SizedBox(height: 20),
                  // Title
                  Text(
                    'Exit App',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Content
                  Text(
                    'Are you sure you want to exit the app?',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey[300]
                          : Colors.grey[600],
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 30),
                  // Buttons
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            Navigator.of(context).pop(false);
                          },
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.grey[600]!
                                    : Colors.grey[300]!,
                              ),
                            ),
                          ),
                          child: Text(
                            'Cancel',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.grey[300]
                                  : Colors.grey[700],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop(true);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red[600],
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                          child: const Text(
                            'Exit',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            ),
          ),
        ) ?? false;
        
        if (shouldPop) {
          Navigator.of(context).pop();
        }
      },
      child: GestureDetector(
        onTap: _onUserInteraction,
        onPanDown: _onUserInteraction,
        onScaleStart: _onUserInteraction,
        child: Scaffold(
          body: SafeArea(
            child: Column(
              children: [
                Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _selectedIndex = index;
                  });
                },
                children: [
                  _buildDashboard(),
                  const ServicesScreen(),
                  const SupportScreen(),
                  const ProfileScreen(),
                ],
              ),
            ),
            GNav(
              rippleColor: isDarkMode ? Colors.white.withValues(alpha: 0.1) : Colors.black.withValues(alpha: 0.1),
              hoverColor: isDarkMode ? Colors.white.withValues(alpha: 0.1) : Colors.black.withValues(alpha: 0.1),
              gap: 8,
              activeColor: isDarkMode ? Colors.white : Colors.white, // Selected item color
              iconSize: 24,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              duration: const Duration(milliseconds: 300),
              tabBackgroundColor: ColorPalette.secondary, // Use ColorPalette.unselectedNavItemColor in both modes
              color: isDarkMode ? Colors.white70 : Colors.grey[600]!, // Unselected item color
              selectedIndex: _selectedIndex,
              onTabChange: (index) {
                _pageController.animateToPage(
                  index,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              },
              tabs: const [
                GButton(
                  icon: Icons.home_rounded,
                  text: 'Home',
                ),
                GButton(
                  icon: Icons.widgets_rounded,
                  text: 'Services',
                ),
                GButton(
                  icon: Icons.support_agent_rounded,
                  text: 'Support',
                ),
                GButton(
                  icon: Icons.person_rounded,
                  text: 'Profile',
                ),
              ],
            ),
          ],
        ),
      ),
    ),  // Close GestureDetector
      )
    );  // Close PopScope
  }

  // Function to show the Edit Favorites dialog
  void _showEditFavoritesDialog() {
    // Get available services from dashboard tiles
    final availableServices = _getAvailableServicesFromTiles();

    if (availableServices.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('No services available for selection. Please wait for tiles to load.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        // Use a StatefulWidget to manage the state within the dialog
        return StatefulBuilder(
          builder: (context, setDialogState) {
            final isDark = Theme.of(context).brightness == Brightness.dark;
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              backgroundColor: isDark ? Theme.of(context).dialogTheme.backgroundColor : Colors.grey[200],
              titlePadding: EdgeInsets.zero,
              title: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: ColorPalette.unselectedNavItemColor,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                ),
                child: Center(
                  child: Text(
                    'Edit Favorites',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              content: SizedBox(
                width: double.maxFinite,
                height: MediaQuery.of(context).size.height * 0.5, // Constrain height to 50% of screen
                child: SingleChildScrollView(
                  child: GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(), // Disable GridView scrolling, let SingleChildScrollView handle it
                    itemCount: availableServices.length,
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      childAspectRatio: 0.85, // Make items taller to accommodate content
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                    ),
                    itemBuilder: (context, index) {
                      final service = availableServices[index];
                      final isFavorite = _favoriteServices.any((fav) => fav['id'] == service['id']);
                      final canAddMore = _favoriteServices.length < 6;

                      return InkWell(
                        onTap: () {
                          setDialogState(() { // Update dialog state
                            setState(() { // Update main screen state
                              if (isFavorite) {
                                _favoriteServices.removeWhere((fav) => fav['id'] == service['id']);
                                AppLogger.info('Removed ${service['label']} from favorites. Total: ${_favoriteServices.length}');
                              } else if (canAddMore) {
                                _favoriteServices.add(service);
                                AppLogger.info('Added ${service['label']} to favorites. Total: ${_favoriteServices.length}');
                              } else {
                                AppLogger.info('Cannot add more favorites. Limit reached (6)');
                              }
                            });
                          });
                        },
                        child: Opacity(
                          opacity: isFavorite || canAddMore ? 1.0 : 0.5, // Dim if can't add
                          child: Column(
                            mainAxisSize: MainAxisSize.min, // Use minimum space needed
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: isFavorite
                                      ? (isDark ? ColorPalette.primary.withValues(alpha: 0.25) : ColorPalette.primary.withValues(alpha: 0.15))
                                      : (isDark ? Colors.grey[700] : Theme.of(context).primaryColor.withValues(alpha: 0.05)),
                                  borderRadius: BorderRadius.circular(16), // Softer corners
                                  border: Border.all(
                                    color: isFavorite ? ColorPalette.primary : Colors.transparent,
                                    width: 2,
                                  ),
                                ),
                                child: Stack(
                                  children: [
                                    Center(
                                      child: service['type'] == 'icon'
                                          ? Icon(
                                              service['value'] as IconData,
                                              color: isDark ? Colors.white70 : ColorPalette.secondary,
                                              size: 24,
                                            )
                                          : Image.asset(
                                              service['value'] as String,
                                              width: 24,
                                              height: 24,
                                            ),
                                    ),
                                    if (isFavorite)
                                      Positioned(
                                        top: 0,
                                        right: 0,
                                        child: Container(
                                          padding: EdgeInsets.all(2),
                                          decoration: BoxDecoration(
                                            color: ColorPalette.primary,
                                            shape: BoxShape.circle,
                                          ),
                                          child: Icon(
                                            Icons.check,
                                            size: 12,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 6), // Reduced spacing
                              Flexible(
                                child: Text(
                                  service['label'] as String,
                                  style: TextStyle(
                                    fontSize: 10, // Smaller font to fit better
                                    color: isDark ? Colors.white : ColorPalette.textDark,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              actions: [
                ElevatedButton( // Changed to ElevatedButton
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white, // Ensuring text is visible
                  ),
                  onPressed: () async {
                    final navigator = Navigator.of(context);
                    final messenger = ScaffoldMessenger.of(context);
                    
                    // Save the favorites before closing
                    await _saveFavoriteServices();
                    if (!mounted) return;
                    navigator.pop();

                    // Show confirmation
                    messenger.showSnackBar(
                      SnackBar(
                        content: Text('Favorites saved successfully!'),
                        backgroundColor: Colors.green,
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                  child: Text('Done'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildLoadingAccountsCarousel() {
    return Column(
      children: [
        Container(
          height: 250,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: ColorPalette.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(ColorPalette.primary),
                ),
                const SizedBox(height: 16),
                Text(
                  'Loading accounts...',
                  style: TextStyle(
                    fontSize: 16,
                    color: ColorPalette.primary,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 4),
      ],
    );
  }

  Widget _buildNoAccountsMessage() {
    return Column(
      children: [
        Container(
          height: 250,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.account_balance_wallet_outlined,
                  size: 48,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                Text(
                  'No accounts found',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Unable to load your accounts',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _refreshAccounts,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorPalette.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: Text('Retry'),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 4),
      ],
    );
  }

  Widget _buildLoadingFavoritesGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 6, // Show 6 loading placeholders
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 2.2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemBuilder: (context, index) {
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNoFavoritesMessage() {
    return Container(
      height: 120,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.dashboard_customize_outlined,
              size: 32,
              color: Colors.grey.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 8),
            Text(
              'No dashboard tiles configured',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              'Please contact support to configure services for your account',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _transactionRefreshTimer?.cancel();
    TokenRefreshService.instance.dispose();
    super.dispose();
  }
}