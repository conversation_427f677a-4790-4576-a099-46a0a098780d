import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import '../../utils/color_palette.dart';
import 'otp_verification_screen.dart';
import '../widgets/transaction_confirmation_form.dart';

class TransactionDetailsScreen extends StatefulWidget {
  final String transactionType;
  final String accountName;
  final String accountBalance;
  final Color accountColor;
  final bool showAccountDropdown;

  const TransactionDetailsScreen({
    super.key,
    required this.transactionType,
    required this.accountName,
    required this.accountBalance,
    required this.accountColor,
    this.showAccountDropdown = true,
  });

  @override
  State<TransactionDetailsScreen> createState() => _TransactionDetailsScreenState();
}

class _TransactionDetailsScreenState extends State<TransactionDetailsScreen> {
  String? _selectedRecipient = 'To own';
  String? _selectedAccount;
  String? _selectedToAccount;
  String? _selectedPaymentMethod;
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _pinController = TextEditingController();
  bool _showPin = false;
  bool _isValidPhone = false;
  bool _showPrefixError = false;

  // Hardcoded accounts for dropdown
  final List<Map<String, dynamic>> _accounts = [
    {
      'name': 'Savings Account',
      'balance': '250,000',
    },
    {
      'name': 'Shares Account',
      'balance': '100,000',
    },
    {
      'name': 'NWD Account',
      'balance': '50,000',
    },
    {
      'name': 'Loan Account',
      'balance': '150,000',
    },
  ];

  @override
  void initState() {
    super.initState();
    // Set default selected account
    if (_accounts.isNotEmpty) {
      _selectedAccount = _accounts[0]['name'];
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final isWithdrawal = widget.transactionType.toLowerCase() == 'withdraw';
    final isInterAccount = widget.transactionType.toLowerCase() == 'inter account';
    final isDeposit = widget.transactionType.toLowerCase() == 'deposit';

    return Scaffold(
      appBar: AppBar(
        backgroundColor: widget.accountColor,
        title: Text(
          widget.transactionType,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
      ),
      body: Container(
        color: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).scaffoldBackgroundColor
            : Colors.grey[200],
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Account Info Card
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: widget.accountColor,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(20),
                    bottomRight: Radius.circular(20),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isDeposit ? 'To Account' : 'From Account',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if ((isWithdrawal && widget.showAccountDropdown) || (isDeposit && widget.accountName.isEmpty)) ...[  // Show dropdown for withdrawal if allowed or deposit if no account specified
                      DropdownButtonHideUnderline(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.3),
                            ),
                          ),
                          child: DropdownButton<String>(
                            value: _selectedAccount,
                            isExpanded: true,
                            dropdownColor: widget.accountColor,
                            hint: Text(
                              isDeposit ? 'Select account to deposit to' : 'Select account',
                              style: const TextStyle(color: Colors.white70),
                            ),
                            items: _accounts
                                .map((account) => DropdownMenuItem<String>(
                                      value: account['name'],
                                      child: Text(
                                        account['name'],
                                        style: const TextStyle(color: Colors.white),
                                      ),
                                    ))
                                .toList(),
                            onChanged: (String? newValue) {
                              if (newValue != null) {
                                setState(() {
                                  _selectedAccount = newValue;
                                });
                              }
                            },
                            icon: Icon(Icons.arrow_drop_down, color: Colors.white),
                          ),
                        ),
                      ),
                    ]
                    else ...[
                      Text(
                        widget.accountName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                    const SizedBox(height: 16),
                    Text(
                      'Available Balance',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          ((isWithdrawal && _selectedAccount != null) || (isDeposit && _selectedAccount != null))
                              ? _accounts.firstWhere((account) => account['name'] == _selectedAccount)['balance']
                              : widget.accountBalance,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          ' KES',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Transaction Form
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Transaction Details',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: ColorPalette.primary,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Recipient selection for withdrawal and inter account
                    if (isWithdrawal || isInterAccount) ...[
                      Text(
                        'Recipient',
                        style: TextStyle(
                          color: ColorPalette.primary,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonHideUnderline(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: isDark ? Colors.grey[800] : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                            ),
                          ),
                          child: DropdownButton<String>(
                            value: _selectedRecipient,
                            isExpanded: true,
                            hint: Text(
                              'Select recipient',
                              style: TextStyle(color: isDark ? Colors.grey[400] : Colors.grey[600]),
                            ),
                            items: ['To own', 'To other']
                                .map((String value) => DropdownMenuItem<String>(
                                      value: value,
                                      child: Text(
                                        value,
                                        style: TextStyle(color: isDark ? Colors.white : Colors.black87),
                                      ),
                                    ))
                                .toList(),
                            onChanged: (String? newValue) {
                              if (newValue != null) {
                                setState(() {
                                  _selectedRecipient = newValue;
                                  if (newValue == 'To own') {
                                    _phoneController.clear();
                                  }
                                });
                              }
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],

                    // To Account selection for inter account transfers when recipient is "To own"
                    if (isInterAccount && _selectedRecipient == 'To own') ...[
                      Text(
                        'To Account',
                        style: TextStyle(
                          color: ColorPalette.primary,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonHideUnderline(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: isDark ? Colors.grey[800] : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                            ),
                          ),
                          child: DropdownButton<String>(
                            value: _selectedToAccount,
                            isExpanded: true,
                            hint: Text(
                              'Select destination account',
                              style: TextStyle(color: isDark ? Colors.grey[400] : Colors.grey[600]),
                            ),
                            items: _accounts
                                .where((account) => account['name'] != _selectedAccount)
                                .map((account) => DropdownMenuItem<String>(
                                      value: account['name'],
                                      child: Text(
                                        account['name'],
                                        style: TextStyle(color: isDark ? Colors.white : Colors.black87),
                                      ),
                                    ))
                                .toList(),
                            onChanged: (String? newValue) {
                              if (newValue != null) {
                                setState(() {
                                  _selectedToAccount = newValue;
                                });
                              }
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],

                    // Phone number field for "To other" recipient
                    if ((isWithdrawal || isInterAccount) && _selectedRecipient == 'To other') ...[
                      Text(
                        'Phone Number',
                        style: TextStyle(
                          color: ColorPalette.primary,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _phoneController,
                        keyboardType: TextInputType.phone,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(12),
                          TextInputFormatter.withFunction((oldValue, newValue) {
                            final text = newValue.text;
                            if (text.startsWith('254')) {
                              if (text.length > 12) {
                                return oldValue;
                              }
                            } else if (text.startsWith('0')) {
                              if (text.length > 10) {
                                return oldValue;
                              }
                            }
                            return newValue;
                          }),
                        ],
                        onChanged: (value) {
                          final isValidPrefix = value.startsWith('07') || value.startsWith('01') || value.startsWith('254');
                          setState(() {
                            _isValidPhone = RegExp(r'^(07|01)\d{8}$|^254\d{9}$').hasMatch(value);
                            _showPrefixError = !isValidPrefix && value.isNotEmpty;
                          });
                        },
                        decoration: InputDecoration(
                          hintText: 'Enter phone number',
                          filled: true,
                          fillColor: isDark ? Colors.grey[800] : Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: ColorPalette.secondary,
                              width: 2,
                            ),
                          ),
                          hintStyle: TextStyle(
                            color: isDark ? Colors.grey[400] : Colors.grey[600],
                          ),
                          suffixIcon: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (_isValidPhone)
                                Icon(
                                  Icons.check_circle,
                                  color: ColorPalette.primary,
                                ).animate(
                                  onPlay: (controller) => controller.repeat(),
                                ).scale(
                                  duration: 400.ms,
                                  curve: Curves.easeInOut,
                                ).then().shake(
                                  duration: 400.ms,
                                  rotation: 0.1,
                                ),
                              IconButton(
                                icon: Icon(
                                  Icons.contact_phone,
                                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                                ),
                                onPressed: () async {
                                  if (await FlutterContacts.requestPermission()) {
                                    final contact = await FlutterContacts.openExternalPick();
                                    if (contact != null && contact.phones.isNotEmpty) {
                                      setState(() {
                                        _phoneController.text = contact.phones.first.number.replaceAll(RegExp(r'[^0-9]'), '');
                                        final isValidPrefix = _phoneController.text.startsWith('07') || _phoneController.text.startsWith('01') || _phoneController.text.startsWith('254');
                                        _isValidPhone = RegExp(r'^(07|01)\d{8}$|^254\d{9}$').hasMatch(_phoneController.text);
                                        _showPrefixError = !isValidPrefix && _phoneController.text.isNotEmpty;
                                      });
                                    }
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                        style: TextStyle(
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                      ),
                      if (_showPrefixError && _phoneController.text.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0, top: 4.0),
                          child: Text(
                            'Phone number must start with 07, 01, or 254',
                            style: TextStyle(
                              color: Colors.red,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      const SizedBox(height: 20),
                    ],

                    // Amount field
                    Text(
                      'Amount',
                      style: TextStyle(
                        color: ColorPalette.primary,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _amountController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      decoration: InputDecoration(
                        hintText: 'Enter amount',
                        filled: true,
                        fillColor: isDark ? Colors.grey[800] : Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: ColorPalette.secondary,
                            width: 2,
                          ),
                        ),
                        hintStyle: TextStyle(
                          color: isDark ? Colors.grey[400] : Colors.grey[600],
                        ),
                        prefixIcon: Icon(
                          Icons.attach_money,
                          color: ColorPalette.primary,
                        ),
                      ),
                      style: TextStyle(
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Payment method selection for deposits only
                    if (isDeposit) ...[
                      Text(
                        'Select Deposit Method',
                        style: TextStyle(
                          color: ColorPalette.primary,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildPaymentMethod(
                        icon: 'assets/icons/mpesa.png',
                        label: 'M-Pesa',
                        onTap: () => setState(() => _selectedPaymentMethod = 'M-Pesa'),
                        isSelected: _selectedPaymentMethod == 'M-Pesa',
                      ),
                      _buildPaymentMethod(
                        icon: 'assets/icons/airtel_money.png',
                        label: 'Airtel Money',
                        onTap: () => setState(() => _selectedPaymentMethod = 'Airtel Money'),
                        isSelected: _selectedPaymentMethod == 'Airtel Money',
                      ),
                      const SizedBox(height: 20),
                    ],

                    // PIN field
                    Text(
                      'PIN',
                      style: TextStyle(
                        color: ColorPalette.primary,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _pinController,
                      obscureText: !_showPin,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      maxLength: 4,
                      decoration: InputDecoration(
                        hintText: 'Enter PIN',
                        filled: true,
                        fillColor: isDark ? Colors.grey[800] : Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: ColorPalette.secondary,
                            width: 2,
                          ),
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _showPin ? Icons.visibility : Icons.visibility_off,
                            color: isDark ? Colors.grey[400] : Colors.grey[600],
                          ),
                          onPressed: () {
                            setState(() {
                              _showPin = !_showPin;
                            });
                          },
                        ),
                        hintStyle: TextStyle(
                          color: isDark ? Colors.grey[400] : Colors.grey[600],
                        ),
                        prefixIcon: Icon(
                          Icons.lock_outline,
                          color: ColorPalette.primary,
                        ),
                      ),
                      style: TextStyle(
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 30),

                    // Action buttons
                    Padding(
                      padding: const EdgeInsets.only(bottom: 160),
                      child: Row(
                        children: [
                          // Cancel button
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey[300],
                                foregroundColor: Colors.black87,
                                padding: const EdgeInsets.symmetric(vertical: 15),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                'Cancel',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          // Continue button
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                // Validate inputs
                                if (_amountController.text.isEmpty || _pinController.text.isEmpty) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('Please fill all required fields'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }

                                if (isWithdrawal && _selectedAccount == null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('Please select an account'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }

                                if (isDeposit && _selectedPaymentMethod == null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('Please select a payment method'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }

                                if ((isWithdrawal || isInterAccount) && _selectedRecipient == 'To other' && _phoneController.text.isEmpty) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('Please enter a phone number'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }

                                if (isInterAccount && _selectedRecipient == 'To own' && _selectedToAccount == null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('Please select a destination account'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }

                                // Handle deposits differently - show completion dialog instead of OTP
                                if (isDeposit) {
                                  _showDepositCompletionDialog(context);
                                } else {
                                  // Show confirmation dialog for other transaction types
                                  showDialog(
                                    context: context,
                                    builder: (context) => TransactionConfirmationForm(
                                      transactionType: widget.transactionType.toLowerCase(),
                                      transactionDetails: {
                                        'amount': _amountController.text,
                                        'account_type': (isWithdrawal && _selectedAccount != null) 
                                            ? _selectedAccount! 
                                            : widget.accountName,
                                        'total_amount': _amountController.text,
                                        if (isWithdrawal || isInterAccount) 'recipient_type': _selectedRecipient ?? 'To own',
                                        if ((isWithdrawal || isInterAccount) && _selectedRecipient == 'To other')
                                          'recipient_phone': _phoneController.text,
                                        if (isInterAccount && _selectedRecipient == 'To own' && _selectedToAccount != null)
                                          'to_account': _selectedToAccount!,
                                      },
                                      onConfirm: (details) {
                                        Navigator.pop(context);
                                        showDialog(
                                          context: context,
                                          builder: (context) => OtpVerificationScreen(
                                            phoneNumber: '+254*****5673',
                                            transactionType: widget.transactionType.toLowerCase(),
                                            amount: details['amount']!,
                                            transactionDetails: details,
                                          ),
                                        );
                                      },
                                    ),
                                  );
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: ColorPalette.primary,
                                foregroundColor: ColorPalette.white,
                                padding: const EdgeInsets.symmetric(vertical: 15),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                'Continue',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _amountController.dispose();
    _pinController.dispose();
    super.dispose();
  }

  String _getSelectedAccountName() {
    // For deposits: if widget.accountName is empty (from favorites), use selected account
    // If widget.accountName is provided (from account cards), use that
    if (widget.accountName.isEmpty) {
      return _selectedAccount ?? 'Savings Account';
    } else {
      return widget.accountName;
    }
  }

  Widget _buildPaymentMethod({
    required dynamic icon,
    required String label,
    required VoidCallback onTap,
    bool isIcon = false,
    bool isSelected = false,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isSelected 
            ? (isDark ? ColorPalette.primary.withValues(alpha: 0.2) : ColorPalette.primary.withValues(alpha: 0.1))
            : (isDark ? Colors.grey[800] : Colors.white),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected 
              ? ColorPalette.primary
              : (isDark ? Colors.grey[600]! : Colors.grey[300]!),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            isIcon 
                ? Icon(
                    icon as IconData, 
                    size: 32, 
                    color: isSelected ? ColorPalette.primary : Colors.blue
                  )
                : Image.asset(
                    icon as String,
                    width: 32,
                    height: 32,
                  ),
            if (isSelected) ...[
              const SizedBox(width: 6),
              Container(
                padding: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  color: ColorPalette.primary,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 12,
                ),
              ),
            ],
          ],
        ),
        title: Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected 
                ? ColorPalette.primary 
                : (isDark ? Colors.white : Colors.black87),
          ),
        ),
        trailing: Icon(
          isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
          color: isSelected ? ColorPalette.primary : Colors.grey,
          size: 20,
        ),
        onTap: onTap,
      ),
    );
  }

  void _showDepositCompletionDialog(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        insetPadding: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          decoration: BoxDecoration(
            color: isDark ? Colors.grey[900] : Colors.grey[100],
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: ColorPalette.primary,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.schedule,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Deposit Initiated',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: ColorPalette.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: ColorPalette.primary.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Column(
                        children: [
                          Text(
                            'KES ${_amountController.text}',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: ColorPalette.primary,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Deposit via $_selectedPaymentMethod',
                            style: TextStyle(
                              fontSize: 14,
                              color: isDark ? Colors.grey[400] : Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'To: ${_getSelectedAccountName()}',
                            style: TextStyle(
                              fontSize: 14,
                              color: isDark ? Colors.grey[400] : Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Your deposit request has been sent to $_selectedPaymentMethod. Please check your phone for a payment prompt and complete the transaction.',
                      style: TextStyle(
                        fontSize: 14,
                        color: isDark ? Colors.grey[300] : Colors.grey[700],
                        height: 1.4,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isDark ? Colors.grey[800] : Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: ColorPalette.primary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'You will receive an SMS confirmation once the deposit is processed.',
                              style: TextStyle(
                                fontSize: 12,
                                color: isDark ? Colors.grey[400] : Colors.grey[600],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context); // Close dialog
                          Navigator.pop(context); // Return to previous screen
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorPalette.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'OK',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
