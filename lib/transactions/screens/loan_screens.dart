import 'package:flutter/material.dart';
import 'payment_screen.dart';
import '../../utils/color_palette.dart';

class LoanScreen extends StatefulWidget {
  const LoanScreen({super.key});

  @override
  State<LoanScreen> createState() => _LoanScreenState();
}

class _LoanScreenState extends State<LoanScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;
  late PageController _loanCardsController; // Add controller for loan cards
  String _selectedStatus = 'Pending';
  int _currentLoanIndex = 0; // Track current loan being viewed

  // Define loan data for different loan types
  final Map<String, Map<String, dynamic>> _loanData = {
    'biashara': {
      'details': {
        'Payment period': '6 Months',
        'Interest Rate: 8%': 'KES. 24,000',
        'Loan Amount': 'KES. 300,000',
        'Payable Amount': 'KES. 324,000',
      },
      'guarantors': [
        {'name': '<PERSON>', 'image': 'assets/images/trevor_ombija.png', 'amount': 'KES 150,000', 'status': 'Approved'},
        {'name': '<PERSON>ku', 'image': 'assets/images/henry_oliver.png', 'amount': 'KES 100,000', 'status': 'Pending'},
        {'name': 'Peter Mwangi', 'image': 'assets/images/henry_nganga.png', 'amount': 'KES 50,000', 'status': 'Approved'},
      ]
    },
    'kilimo': {
      'details': {
        'Payment period': '12 Months',
        'Interest Rate: 7%': 'KES. 14,000',
        'Loan Amount': 'KES. 200,000',
        'Payable Amount': 'KES. 214,000',
      },
      'guarantors': [
        {'name': 'Grace Nyambura', 'image': 'assets/images/lilly_oduk.png', 'amount': 'KES 80,000', 'status': 'Approved'},
        {'name': 'Samuel Kiprotich', 'image': 'assets/images/zakaria_kinyua.png', 'amount': 'KES 70,000', 'status': 'Pending'},
        {'name': 'Faith Wanjiru', 'image': 'assets/images/george_wachira.png', 'amount': 'KES 50,000', 'status': 'Rejected'},
      ]
    }
  };

  // Define active loan data
  final Map<String, Map<String, dynamic>> _activeLoanData = {
    'school': {
      'details': {
        'Payment period': '3 Months',
        'Interest Rate: 5%': 'KES. 2,000',
        'Loan Amount': 'KES. 40,000',
        'Payable Amount': 'KES. 42,000',
      },
      'guarantors': [
        {'name': 'Trevor Ombija', 'image': 'assets/images/trevor_ombija.png', 'amount': 'KES 20,000', 'status': 'Approved'},
        {'name': 'Stephanie Oduk', 'image': 'assets/images/henry_oliver.png', 'amount': 'KES 15,000', 'status': 'Approved'},
        {'name': 'Henry Nga\'nga', 'image': 'assets/images/henry_nganga.png', 'amount': 'KES 5,000', 'status': 'Approved'},
      ]
    },
    'home': {
      'details': {
        'Payment period': '24 Months',
        'Interest Rate: 2.5%': 'KES. 5,000',
        'Loan Amount': 'KES. 100,000',
        'Payable Amount': 'KES. 105,000',
      },
      'guarantors': [
        {'name': 'Alice Muthoni', 'image': 'assets/images/lilly_oduk.png', 'amount': 'KES 50,000', 'status': 'Approved'},
        {'name': 'David Ochieng', 'image': 'assets/images/trevor_ombija.png', 'amount': 'KES 30,000', 'status': 'Approved'},
        {'name': 'Rose Akinyi', 'image': 'assets/images/henry_oliver.png', 'amount': 'KES 20,000', 'status': 'Approved'},
      ]
    }
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _pageController = PageController();
    _loanCardsController = PageController(viewportFraction: 0.9);

    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        _pageController.animateToPage(
          _tabController.index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    _loanCardsController.dispose();
    super.dispose();
  }


  Widget _buildLoanDetail({required String title, required String value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildLoanDetails() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    // Get current loan data based on selected status and loan index
    Map<String, dynamic> currentLoanData;
    List<dynamic> currentGuarantors;
    
    if (_selectedStatus == 'Pending') {
      String loanKey = _currentLoanIndex == 0 ? 'biashara' : 'kilimo';
      currentLoanData = _loanData[loanKey]!['details'];
      currentGuarantors = _loanData[loanKey]!['guarantors'];
    } else {
      String loanKey = _currentLoanIndex == 0 ? 'school' : 'home';
      currentLoanData = _activeLoanData[loanKey]!['details'];
      currentGuarantors = _activeLoanData[loanKey]!['guarantors'];
    }
    
    return Column(
      children: [
        // Loan Details Card
        Card(
          color: isDark ? ColorPalette.white.withValues(alpha: 0.05) : Colors.white,
          margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: isDark ? 0 : 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Loan Details',
                  style: TextStyle(
                    color: isDark ? Colors.white : ColorPalette.primary,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 6),
                ...currentLoanData.entries.map((entry) => 
                  _buildDetailRow(entry.key, entry.value)
                ),
              ],
            ),
          ),
        ),

        // Guarantors Card
        Card(
          color: isDark ? ColorPalette.white.withValues(alpha: 0.05) : Colors.white,
          margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: isDark ? 0 : 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Guarantors',
                  style: TextStyle(
                    color: isDark ? Colors.white : ColorPalette.primary,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 1),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Selected guarantors',
                      style: TextStyle(
                        fontSize: 14,
                        color: isDark ? Colors.white70 : Colors.grey,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const GuarantorsScreen(),
                          ),
                        );
                      },
                      child: Text(
                        'See all',
                        style: TextStyle(
                          color: isDark ? Colors.white : ColorPalette.primary,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: currentGuarantors.map((guarantor) => 
                      _buildGuarantorAvatar(
                        context, 
                        guarantor['name'], 
                        guarantor['image'], 
                        guarantor['amount'],
                        guarantor['status']
                      )
                    ).toList(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoanOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String interestRate,
    required String duration,
    required String maxAmount,
    bool isActive = false,
  }) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF563D67),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              IconButton(
                icon: Icon(
                  Icons.more_vert,
                  color: Colors.white,
                ),
                onPressed: () {},
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildLoanDetail(
                title: 'Interest rate',
                value: interestRate,
              ),
              _buildLoanDetail(
                title: 'Duration',
                value: duration,
              ),
              _buildLoanDetail(
                title: 'Maximum amount',
                value: maxAmount,
              ),
            ],
          ),
          if (isActive)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorPalette.primary,
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: () {
                  // Get current loan details based on the current loan index
                  String loanKey = _currentLoanIndex == 0 ? 'school' : 'home';
                  Map<String, dynamic> currentLoanDetails = _activeLoanData[loanKey]!['details'];
                  
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PaymentScreen(
                        loanTitle: title,
                        loanAmount: maxAmount,
                        interestRate: interestRate,
                        duration: duration,
                        loanDetails: currentLoanDetails,
                      ),
                    ),
                  );
                },
                child: Text(
                  'Pay Now',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Theme.of(context).scaffoldBackgroundColor
          : Colors.grey[200],
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'My loans',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        centerTitle: true,
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(30),
            ),
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedStatus = 'Pending';
                        _currentLoanIndex = 0; // Reset to first loan when switching tabs
                      });
                      _pageController.animateToPage(
                        0,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        color: _selectedStatus == 'Pending' ? Colors.pink : Colors.transparent,
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: Text(
                        'Pending',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: _selectedStatus == 'Pending' ? Colors.white : Colors.black,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedStatus = 'Active';
                        _currentLoanIndex = 0; // Reset to first loan when switching tabs
                      });
                      _pageController.animateToPage(
                        1,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        color: _selectedStatus == 'Active' ? Colors.grey[800] : Colors.transparent,
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: Text(
                        'Active',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: _selectedStatus == 'Active' ? Colors.white : Colors.black,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                _tabController.index = index;
                setState(() {
                  if (index == 0) {
                    _selectedStatus = 'Pending';
                  } else {
                    _selectedStatus = 'Active';
                  }
                  _currentLoanIndex = 0; // Reset loan index when switching tabs
                });
              },
              children: [
                // Pending Loans
                SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 180,
                        child: PageView(
                          controller: _loanCardsController,
                          onPageChanged: (index) {
                            setState(() {
                              _currentLoanIndex = index;
                            });
                          },
                          children: [
                            _buildLoanOption(
                              context: context,
                              icon: Icons.business,
                              title: 'Biashara loan',
                              interestRate: '8% p.a',
                              duration: '6months',
                              maxAmount: '300,000KES',
                            ),
                            _buildLoanOption(
                              context: context,
                              icon: Icons.agriculture,
                              title: 'Kilimo loan',
                              interestRate: '7% p.a',
                              duration: '1year',
                              maxAmount: '200,000KES',
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 8.0,
                            height: 8.0,
                            margin: const EdgeInsets.symmetric(horizontal: 4.0),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _currentLoanIndex == 0
                                  ? ColorPalette.primary
                                  : Colors.grey.withValues(alpha: 0.3),
                            ),
                          ),
                          Container(
                            width: 8.0,
                            height: 8.0,
                            margin: const EdgeInsets.symmetric(horizontal: 4.0),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _currentLoanIndex == 1
                                  ? ColorPalette.primary
                                  : Colors.grey.withValues(alpha: 0.3),
                            ),
                          ),
                        ],
                      ),
                      _buildLoanDetails(),
                    ],
                  ),
                ),
                // Active Loans
                SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 260,
                        child: PageView(
                          onPageChanged: (index) {
                            setState(() {
                              _currentLoanIndex = index;
                            });
                          },
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 20),
                              child: _buildLoanOption(
                                context: context,
                                icon: Icons.school,
                                title: 'School fees loan',
                                interestRate: '5% p.a',
                                duration: '3months',
                                maxAmount: '40,000KES',
                                isActive: true,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 20),
                              child: _buildLoanOption(
                                context: context,
                                icon: Icons.home,
                                title: 'Home loan',
                                interestRate: '2.5% p.a',
                                duration: '2years',
                                maxAmount: '100,000KES',
                                isActive: true,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 8.0,
                            height: 8.0,
                            margin: const EdgeInsets.symmetric(horizontal: 4.0),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _currentLoanIndex == 0
                                  ? ColorPalette.primary
                                  : Colors.grey.withValues(alpha: 0.3),
                            ),
                          ),
                          Container(
                            width: 8.0,
                            height: 8.0,
                            margin: const EdgeInsets.symmetric(horizontal: 4.0),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _currentLoanIndex == 1
                                  ? ColorPalette.primary
                                  : Colors.grey.withValues(alpha: 0.3),
                            ),
                          ),
                        ],
                      ),
                      _buildLoanDetails(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGuarantorAvatar(BuildContext context, String name, String imagePath, String amount, [String? status]) {
    String guarantorStatus = status ?? 'Pending';

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => GuarantorDetailsScreen(
              name: name,
              imagePath: imagePath,
              status: guarantorStatus,
              amountToGuarantee: amount,
              previousLoans: name == 'Trevor Ombija' ? [
                {'type': 'Car loan', 'amount': '20,000'},
                {'type': 'House loan', 'amount': '20,000'},
                {'type': 'School fees loan', 'amount': '20,000'},
              ] : null,
            ),
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.only(right: 15),
        child: Column(
          children: [
            CircleAvatar(
              radius: 25,
              backgroundImage: AssetImage(imagePath),
            ),
            const SizedBox(height: 5),
            Text(
              name,
              style: const TextStyle(
                fontSize: 12,
              ),
            ),
            Text(
              amount,
              style: const TextStyle(
                fontSize: 10,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class GuarantorDetailsScreen extends StatelessWidget {
  final String name;
  final String imagePath;
  final String status;
  final List<Map<String, String>>? previousLoans;
  final String amountToGuarantee;

  const GuarantorDetailsScreen({
    super.key,
    required this.name,
    required this.imagePath,
    required this.status,
    this.previousLoans,
    required this.amountToGuarantee,
  });

  Widget _buildPreviousLoans() {
    if (previousLoans == null || previousLoans!.isEmpty) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 20),
          Text(
            'No previously guaranteed loans',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 20),
          Icon(
            Icons.person_search,
            size: 48,
            color: ColorPalette.secondary,
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Loans previously Guaranteed',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 15),
        ...previousLoans!.map((loan) => Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Row(
            children: [
              Icon(
                loan['type'] == 'Car loan' ? Icons.directions_car :
                loan['type'] == 'House loan' ? Icons.home :
                Icons.school,
                color: Colors.pink,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                loan['type']!,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              Text(
                '${loan['amount']}KES',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Theme.of(context).scaffoldBackgroundColor
          : Colors.grey[200],
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Guarantor Details',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              width: double.infinity,
              height: 250,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(imagePath),
                  fit: BoxFit.cover,
                ),
              ),
              child: status == 'Approved' ? const Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: EdgeInsets.all(12.0),
                  child: Icon(
                    Icons.fullscreen,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ) : null,
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Color(0xFF6B4E71),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Guarantor Details',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 15),
                  Row(
                    children: [
                      Text(
                        'Name',
                        style: TextStyle(
                          color: Colors.white70,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    children: [
                      Text(
                        'Amount to guarantee',
                        style: TextStyle(
                          color: Colors.white70,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        amountToGuarantee,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    children: [
                      Text(
                        'Status',
                        style: TextStyle(
                          color: Colors.white70,
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: status == 'Approved' ? ColorPalette.primary :
                          status == 'Rejected' ? ColorPalette.secondary :
                          Colors.grey,
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: Text(
                          status,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: _buildPreviousLoans(),
            ),
          ],
        ),
      ),
    );
  }
}

class GuarantorsScreen extends StatefulWidget {
  const GuarantorsScreen({super.key});

  @override
  State<GuarantorsScreen> createState() => _GuarantorsScreenState();
}

class _GuarantorsScreenState extends State<GuarantorsScreen> {
  Widget _buildGuarantorCard(String name, String status, String imagePath, String amount) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Card(
      color: isDark ? ColorPalette.white.withValues(alpha: 0.05) : Colors.white,
      margin: const EdgeInsets.only(bottom: 15),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: isDark ? 0 : 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundImage: AssetImage(imagePath),
                ),
                const SizedBox(width: 15),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isDark ? Colors.white : ColorPalette.textDark,
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        'Amount: $amount',
                        style: TextStyle(
                          fontSize: 14,
                          color: isDark ? Colors.white70 : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 5,
                  ),
                  decoration: BoxDecoration(
                    color: status == 'Approved' ? ColorPalette.primary :
                          status == 'Rejected' ? ColorPalette.secondary :
                          Colors.grey,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Text(
                    status,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            if (status == 'Pending')
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(
                        color: isDark ? Colors.red[400]! : Colors.red,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onPressed: () {
                      _showReplaceGuarantorDialog(name);
                    },
                    child: Text(
                      'Replace',
                      style: TextStyle(
                        color: isDark ? Colors.red[400] : Colors.red,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showReplaceGuarantorDialog(String name) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Replace Guarantor'),
        content: Text('Are you sure you want to replace $name?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Add logic to replace guarantor
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('$name has been replaced')),
              );
            },
            child: Text(
              'Replace',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Theme.of(context).scaffoldBackgroundColor
          : Colors.grey[200],
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Guarantors',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(20),
        children: [
          _buildGuarantorCard('Henry Oliver', 'Approved', 'assets/images/henry_oliver.png', 'KES 200,000'),
          _buildGuarantorCard('Zakaria Kinyua', 'Pending', 'assets/images/zakaria_kinyua.png', 'KES 150,000'),
          _buildGuarantorCard('George Wachira', 'Rejected', 'assets/images/george_wachira.png', 'KES 100,000'),
          _buildGuarantorCard('Shely Wavinya', 'Rejected', 'assets/images/shely_wavinya.png', 'KES 50,000'),
          _buildGuarantorCard('Pendo Amina', 'Pending', 'assets/images/pendo_amina.png', 'KES 75,000'),
          _buildGuarantorCard('Lillian Wambua', 'Approved', 'assets/images/lilian_wambua.png', 'KES 300,000'),
        ],
      ),
    );
  }
}
