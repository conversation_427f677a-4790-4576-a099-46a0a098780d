import 'package:flutter/material.dart';
import '../../utils/color_palette.dart';
import 'transaction_form_screen.dart';
import 'full_statement_screen.dart';
import 'mini_statement_screen.dart';
import 'loan_screens.dart';
import 'form_journey_screen.dart';
import '../../utils/services/account_manager.dart';

import '../../utils/app_logger.dart';
import 'dart:ui';
class ServicesScreen extends StatefulWidget {
  const ServicesScreen({super.key});

  @override
  State<ServicesScreen> createState() => _ServicesScreenState();
}

class _ServicesScreenState extends State<ServicesScreen> {
  // Service ID to Service Code mapping for dynamic forms
  final Map<String, String> _serviceCodeMapping = {
    'deposit': 'DEP',
    'withdraw': 'WTH',
    'transfer_to_bank': 'TTB',
    'pay_to_till': 'PTT',
    'mpesa_float': 'MFL',
    'dstv': 'DSTV',
    'zuku': 'ZUKU',
    'startimes': 'STAR',
    'nairobi_water': 'NWTR',
  };

  // This will hold the real accounts fetched from the cache
  List<dynamic> _realAccounts = [];
  bool _isLoadingAccounts = true;

  @override
  void initState() {
    super.initState();
    _initializeAccounts();
  }

  /// Load accounts from the cache (populated by the dashboard)
  Future<void> _initializeAccounts() async {
    try {
      final cachedAccounts = await AccountManager.getCachedAccounts();
      if (mounted) {
        setState(() {
          _realAccounts = cachedAccounts ?? [];
          _isLoadingAccounts = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingAccounts = false;
        });
      }
      AppLogger.error('loading accounts on ServicesScreen: $e');
    }
  }

  // Mock account data (same as dashboard)

  /// Navigate to dynamic service using FormJourneyScreen
  Future<void> _navigateToDynamicService(String serviceCode) async {
    // Check if accounts are loaded
    if (_isLoadingAccounts) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Loading accounts, please wait...'),
          backgroundColor: ColorPalette.primary,
        ),
      );
      return;
    }

    // Check if accounts are available (for services that need accounts)
    if (_realAccounts.isEmpty && _requiresAccounts(serviceCode)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('No accounts available for this service'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Navigate to FormJourneyScreen with the service code
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FormJourneyScreen(
          serviceCode: serviceCode,
          accounts: _realAccounts,
        ),
      ),
    );
  }

  /// Check if service requires accounts
  bool _requiresAccounts(String serviceCode) {
    // B2B services typically require accounts
    const accountRequiredServices = ['TTB', 'PTT', 'MFL'];
    return accountRequiredServices.contains(serviceCode);
  }

  /// Show enquiry options dialog
  void _showEnquiryOptions(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[900] : Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: ColorPalette.secondary,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.help_outline,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Select Enquiry Type',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Options
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      _buildEnquiryOption(
                        context,
                        icon: Icons.account_balance_wallet,
                        title: 'Savings Balance',
                        subtitle: 'Check your savings account balance',
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => FormJourneyScreen(
                                serviceCode: 'SBE',
                                accounts: _realAccounts,
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 12),
                      _buildEnquiryOption(
                        context,
                        icon: Icons.pie_chart,
                        title: 'Share Capital Balance',
                        subtitle: 'Check your share capital balance',
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => FormJourneyScreen(
                                serviceCode: 'SCBE',
                                accounts: _realAccounts,
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 12),
                      _buildEnquiryOption(
                        context,
                        icon: Icons.savings,
                        title: 'Investment Balance',
                        subtitle: 'Check your investment account balance',
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => FormJourneyScreen(
                                serviceCode: 'NWDB',
                                accounts: _realAccounts,
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 12),
                      _buildEnquiryOption(
                        context,
                        icon: Icons.monetization_on,
                        title: 'Loan Balance',
                        subtitle: 'Check your loan balance',
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => FormJourneyScreen(
                                serviceCode: 'LB',
                                accounts: _realAccounts,
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Build enquiry option item
  Widget _buildEnquiryOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? Colors.grey[800] : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: ColorPalette.secondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: ColorPalette.secondary,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }

  void _showDepositChildServicesDialog(BuildContext context, Map<String, dynamic> depositTile) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final List<dynamic> childServices = depositTile['childServices'] ?? [];

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: 'Deposit Modal',
      barrierColor: Colors.black.withValues(alpha: 0.3),
      pageBuilder: (context, anim1, anim2) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 6, sigmaY: 6),
          child: Center(
            child: Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: isDark ? Colors.grey[900] : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: ColorPalette.secondary,
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.help_outline,
                            color: Colors.white,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              depositTile['title'] ?? 'Deposit',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: childServices.map((childService) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: _buildDepositOption(context, childService, () {
                              Navigator.pop(context);
                              _navigateToDepositChildService(childService);
                            }),
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      transitionBuilder: (context, anim1, anim2, child) {
        return FadeTransition(
          opacity: anim1,
          child: child,
        );
      },
    );
  }

  Widget _buildDepositOption(BuildContext context, Map<String, dynamic> childService, VoidCallback onTap) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    String title = childService['title'] ?? 'Service';
    String serviceCode = childService['serviceCode'] ?? '';
    IconData icon;
    String subtitle;
    switch (serviceCode) {
      case 'STKSAVINGS':
        icon = Icons.account_balance_wallet;
        subtitle = 'Check your account balance';
        break;
      case 'STKSHARES':
        icon = Icons.pie_chart;
        subtitle = 'Check your account balance';
        break;
      case 'STKLOANS':
        icon = Icons.monetization_on;
        subtitle = 'Check your account balance';
        break;
      case 'STK':
        icon = Icons.savings;
        subtitle = 'Check your account balance';
        break;
      default:
        icon = Icons.help_outline;
        subtitle = 'Check your account balance';
    }
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? Colors.grey[800] : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: ColorPalette.secondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: ColorPalette.secondary,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToDepositChildService(Map<String, dynamic> childService) {
    String serviceCode = childService['serviceCode'] ?? '';
    String title = childService['title'] ?? 'Service';
    final depositCodes = {'STKSAVINGS', 'STKSHARES', 'STKLOANS', 'STK'};
    if (depositCodes.contains(serviceCode)) {
      String accountTypeKey;
      switch (serviceCode) {
        case 'STKSAVINGS':
          accountTypeKey = 'savings';
          break;
        case 'STKSHARES':
          accountTypeKey = 'share';
          break;
        case 'STKLOANS':
          accountTypeKey = 'loan';
          break;
        case 'STK':
          accountTypeKey = 'nwd';
          break;
        default:
          accountTypeKey = '';
      }
      final filtered = _realAccounts.where((acc) {
        final name = (acc['accountName'] ?? '').toString().toLowerCase();
        if (accountTypeKey == 'savings') return name.contains('savings') || acc['isSavingsAccount'] == 'Yes';
        if (accountTypeKey == 'share') return name.contains('share') || acc['isShareCapital'] == 'Yes';
        if (accountTypeKey == 'loan') return name.contains('loan') || acc['isLoanAccount'] == 'Yes';
        if (accountTypeKey == 'nwd') return name.contains('nwd') || name.contains('deposit') || acc['isNWD'] == 'Yes';
        return false;
      }).toList();
      final lockedAccounts = filtered.isNotEmpty ? filtered : _realAccounts.take(1).toList();
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TransactionFormScreen(
            transactionType: title,
            serviceCode: serviceCode,
            accounts: lockedAccounts,
            tileResponse: childService,
            lockAccountSelection: true,
          ),
        ),
      );
      return;
    }
    if (serviceCode.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TransactionFormScreen(
            transactionType: title,
            serviceCode: serviceCode,
            accounts: _realAccounts,
            tileResponse: childService,
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Service code not found for $title'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  /// Show child services dialog for Apply Loans with sub-services
  void _showChildServicesDialog(BuildContext context, List<dynamic> childServices) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: 'Apply Loans Modal',
      barrierColor: Colors.black.withValues(alpha: 0.3),
      pageBuilder: (context, anim1, anim2) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 6, sigmaY: 6),
          child: Center(
            child: Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: isDark ? Colors.grey[900] : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: ColorPalette.secondary,
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.pan_tool_alt_outlined,
                            color: Colors.white,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Apply Loans',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: childServices.map((childService) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: _buildLoanOption(context, childService, () {
                              Navigator.pop(context);
                              _navigateToLoanChildService(childService);
                            }),
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      transitionBuilder: (context, anim1, anim2, child) {
        return FadeTransition(
          opacity: anim1,
          child: child,
        );
      },
    );
  }

  /// Build loan option item
  Widget _buildLoanOption(BuildContext context, Map<String, dynamic> childService, VoidCallback onTap) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    String title = childService['title'] ?? 'Service';
    String serviceCode = childService['serviceCode'] ?? '';
    
    // Get appropriate icon based on service
    IconData icon;
    String subtitle;
    
    switch (serviceCode) {
      case 'AL':
        icon = Icons.monetization_on;
        subtitle = 'Apply for E-Loan';
        break;
      case 'IDL':
        icon = Icons.phone_android;
        subtitle = 'Apply for Digital Loan';
        break;
      default:
        icon = Icons.pan_tool_alt_outlined;
        subtitle = 'Apply for loan';
    }
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? Colors.grey[800] : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: ColorPalette.secondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: ColorPalette.secondary,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }

  /// Navigate to loan child service
  void _navigateToLoanChildService(Map<String, dynamic> childService) {
    String serviceCode = childService['serviceCode'] ?? '';
    String title = childService['title'] ?? 'Service';
    
    AppLogger.info("=== LOAN CHILD SERVICE NAVIGATION: $title (ServiceCode: $serviceCode) ===");
    
    // Directly navigate to the service without checking for nested child services
    // This matches the dashboard's simplified "Digital Loan" behavior
    if (serviceCode.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TransactionFormScreen(
            transactionType: title,
            serviceCode: serviceCode,
            accounts: _realAccounts,
            tileResponse: childService,
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Service code not found for $title'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Theme.of(context).scaffoldBackgroundColor
          : Colors.grey[200],
      appBar: AppBar(
        title: Text(
          'Services',
          style: TextStyle(
            color: ColorPalette.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        automaticallyImplyLeading: false,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: ColorPalette.secondary,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Explore Our Services',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 10),
                ],
              ),
            ),
            SafeArea(
              top: false,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'General Transactions',
                          style: TextStyle(
                            color: ColorPalette.primary,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        GridView(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 2.2,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                          ),
                          children: [
                            _buildServiceItem(
                              context,
                              icon: Icons.account_balance_wallet,
                              label: 'Deposit',
                              onTap: () {
                                // Replicate dashboard's deposit modal
                                final depositTile = {
                                  'title': 'Deposit',
                                  'childServices': [
                                    {
                                      'title': 'Savings Deposit',
                                      'serviceCode': 'STKSAVINGS',
                                    },
                                    {
                                      'title': 'Shares Deposit',
                                      'serviceCode': 'STKSHARES',
                                    },
                                    {
                                      'title': 'Repay Loan',
                                      'serviceCode': 'STKLOANS',
                                    },
                                    {
                                      'title': 'NWD Deposit',
                                      'serviceCode': 'STK',
                                    },
                                  ],
                                };
                                _showDepositChildServicesDialog(context, depositTile);
                              },
                            ),
                            _buildServiceItem(
                              context,
                              icon: Icons.payments,
                              label: 'Withdraw',
                              onTap: () {
                                // Use WD as the withdrawal service code and pass real accounts
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => TransactionFormScreen(
                                      transactionType: 'Withdraw',
                                      serviceCode: 'WD',
                                      accounts: _realAccounts,
                                    ),
                                  ),
                                );
                              },
                            ),
                            _buildServiceItem(
                              context,
                              icon: Icons.email,
                              label: 'Statements',
                              onTap: () {
                                // Show statement options modal (same as dashboard)
                                _showStatementOptions(context);
                              },
                            ),
                            _buildServiceItem(
                              context,
                              icon: Icons.help_outline,
                              label: 'Balance Enquiries',
                              onTap: () {
                                // Show a dialog to select enquiry type
                                _showEnquiryOptions(context);
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),

                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Loans',
                          style: TextStyle(
                            color: ColorPalette.primary,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                GridView(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 2.2,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  children: [
                    _buildServiceItem(
                      context,
                      icon: Icons.request_quote_rounded,
                      label: 'My loans',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const LoanScreen(),
                          ),
                        );
                      },
                    ),
                    _buildServiceItem(
                      context,
                      icon: Icons.request_page_rounded,
                      label: 'Apply loans',
                      onTap: () {
                        final applyLoansTile = {
                          'title': 'Apply Loans',
                          'childServices': [
                            {
                              "position": 1,
                              "name": "Apply Loans",
                              "title": "E-Loan",
                              "serviceCode": "AL",
                              "icon": "applyLoans"
                            },
                            {
                              "position": 2,
                              "name": "Apply Loans",
                              "title": "Digital Loan",
                              "serviceCode": "IDL",
                              "icon": "applyLoans"
                            }
                          ],
                        };
                        _showChildServicesDialog(context, applyLoansTile['childServices'] as List<dynamic>);
                      },
                    ),
                  ],
                ),
                        const SizedBox(height: 24),
                      ],
                    ),

                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'B2B Services',
                          style: TextStyle(
                            color: ColorPalette.primary,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        GridView(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 2.2,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                          ),
                          children: [
                            _buildServiceItem(
                              context,
                              imagePath: 'assets/images/transfer_to_bank.png',
                              label: 'Transfer to bank',
                              onTap: () {
                                // Use dynamic service navigation instead of hardcoded screen
                                _navigateToDynamicService(_serviceCodeMapping['transfer_to_bank']!);
                              },
                            ),
                            _buildServiceItem(
                              context,
                              imagePath: 'assets/images/safaricom-payto_till.png',
                              label: 'Pay to till',
                              onTap: () {
                                // Use dynamic service navigation instead of hardcoded screen
                                _navigateToDynamicService(_serviceCodeMapping['pay_to_till']!);
                              },
                            ),
                            _buildServiceItem(
                              context,
                              imagePath: 'assets/images/mpesa.png',
                              label: 'Purchase mpesa float',
                              onTap: () {
                                // Use dynamic service navigation instead of hardcoded screen
                                _navigateToDynamicService(_serviceCodeMapping['mpesa_float']!);
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),

                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Utility Bills',
                          style: TextStyle(
                            color: ColorPalette.primary,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        GridView(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 2.2,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                          ),
                          children: [
                            _buildServiceItem(
                              context,
                              imagePath: 'assets/images/dstv.png',
                              label: 'Dstv',
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => TransactionFormScreen(
                                      transactionType: 'DSTV Payment',
                                      serviceCode: _serviceCodeMapping['dstv'],
                                      accounts: _realAccounts,
                                    ),
                                  ),
                                );
                              },
                            ),
                            _buildServiceItem(
                              context,
                              imagePath: 'assets/images/zuku.png',
                              label: 'Zuku',
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => TransactionFormScreen(
                                      transactionType: 'Zuku Payment',
                                      serviceCode: _serviceCodeMapping['zuku'],
                                      accounts: _realAccounts,
                                    ),
                                  ),
                                );
                              },
                            ),
                            _buildServiceItem(
                              context,
                              imagePath: 'assets/images/startimes.png',
                              label: 'Star times',
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => TransactionFormScreen(
                                      transactionType: 'Star Times Payment',
                                      serviceCode: _serviceCodeMapping['startimes'],
                                      accounts: _realAccounts,
                                    ),
                                  ),
                                );
                              },
                            ),
                            _buildServiceItem(
                              context,
                              imagePath: 'assets/images/nairobi_water.png',
                              label: 'Nairobi water',
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => TransactionFormScreen(
                                      transactionType: 'Nairobi Water Payment',
                                      serviceCode: _serviceCodeMapping['nairobi_water'],
                                      accounts: _realAccounts,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceItem(
      BuildContext context, {
        String? imagePath,
        IconData? icon,
        required String label,
        required VoidCallback onTap,
      }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.43,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: imagePath != null
                  ? Image.asset(
                imagePath,
                width: 24,
                height: 24,
              )
                  : Icon(
                icon,
                color: Theme.of(context).iconTheme.color,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showStatementOptions(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      barrierColor: Colors.black54,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.zero,
        child: Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isDark ? Colors.grey[900] : Colors.grey[100],
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: ColorPalette.unselectedNavItemColor,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                ),
                child: Center(
                  child: Text(
                    'Statement Options',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildStatementOption(
                      context,
                      'Mini Statement',
                      Icons.receipt_outlined,
                      () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const MiniStatementScreen(),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 12),
                    _buildStatementOption(
                      context,
                      'Full Statement',
                      Icons.description_outlined,
                      () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const FullStatementScreen(),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatementOption(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? Colors.white.withValues(alpha: 0.05) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isDark ? Colors.white24 : Colors.grey.shade200,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: ColorPalette.secondary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: ColorPalette.secondary,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isDark ? Colors.white : ColorPalette.textDark,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.chevron_right,
              color: isDark ? Colors.white70 : Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }
}

