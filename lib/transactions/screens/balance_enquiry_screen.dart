import 'package:flutter/material.dart';
import '../../utils/color_palette.dart';
import 'form_journey_screen.dart';
import '../../utils/services/account_manager.dart';

import '../../utils/app_logger.dart';
class BalanceEnquiryScreen extends StatefulWidget {
  const BalanceEnquiryScreen({super.key});

  @override
  State<BalanceEnquiryScreen> createState() => _BalanceEnquiryScreenState();
}

class _BalanceEnquiryScreenState extends State<BalanceEnquiryScreen> {
  // This will hold the real accounts fetched from the cache
  List<dynamic> _realAccounts = [];
  bool _isLoadingAccounts = true;

  @override
  void initState() {
    super.initState();
    _initializeAccounts();
  }

  /// Load accounts from the cache (populated by the dashboard)
  Future<void> _initializeAccounts() async {
    try {
      final cachedAccounts = await AccountManager.getCachedAccounts();
      if (mounted) {
        setState(() {
          _realAccounts = cachedAccounts ?? [];
          _isLoadingAccounts = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingAccounts = false;
        });
      }
      AppLogger.error('loading accounts on BalanceEnquiryScreen: $e');
    }
  }

  /// Build enquiry option item
  Widget _buildEnquiryOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required String serviceCode,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => FormJourneyScreen(
              serviceCode: serviceCode,
              accounts: _realAccounts,
            ),
          ),
        );
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(20),
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: isDark ? Colors.grey[850] : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
          ),
          boxShadow: [
            if (!isDark)
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: ColorPalette.secondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                icon,
                color: ColorPalette.secondary,
                size: 28,
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 20,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: isDark 
          ? Theme.of(context).scaffoldBackgroundColor 
          : Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'Balance Enquiry',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: ColorPalette.secondary,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Check Your Balance',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 10),
                  Text(
                    'Select an account and enter your PIN to view your current balance.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
            
            // Content
            Padding(
              padding: const EdgeInsets.all(20),
              child: _isLoadingAccounts
                  ? Center(
                      child: CircularProgressIndicator(
                        color: ColorPalette.primary,
                      ),
                    )
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Select Enquiry Type',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: isDark ? Colors.white : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Choose the type of balance enquiry you want to perform',
                          style: TextStyle(
                            fontSize: 14,
                            color: isDark ? Colors.grey[400] : Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 24),
                        
                        // Enquiry Options
                        _buildEnquiryOption(
                          context,
                          icon: Icons.account_balance_wallet,
                          title: 'Savings Balance',
                          subtitle: 'Check your savings account balance',
                          serviceCode: 'SBE',
                        ),
                        _buildEnquiryOption(
                          context,
                          icon: Icons.pie_chart,
                          title: 'Share Capital Balance',
                          subtitle: 'Check your share capital balance',
                          serviceCode: 'SCBE',
                        ),
                        _buildEnquiryOption(
                          context,
                          icon: Icons.savings,
                          title: 'Investment Balance',
                          subtitle: 'Check your investment account balance',
                          serviceCode: 'NWDB',
                        ),
                        _buildEnquiryOption(
                          context,
                          icon: Icons.monetization_on,
                          title: 'Loan Balance',
                          subtitle: 'Check your loan balance',
                          serviceCode: 'LB',
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Security Notice
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: isDark ? Colors.blue.withValues(alpha: 0.1) : Colors.blue.withValues(alpha: 0.05),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.blue.withValues(alpha: 0.2),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.security,
                                color: Colors.blue,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Security Notice',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blue,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Your PIN is encrypted and secure. Never share your PIN with anyone.',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: isDark ? Colors.grey[400] : Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }
} 