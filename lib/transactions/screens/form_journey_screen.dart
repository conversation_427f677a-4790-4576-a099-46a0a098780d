import 'dart:convert';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import '../../configuration/client_config.dart';
import '../../models/form_field_model.dart';
import '../../models/form_journey_model.dart';
import '../../utils/color_palette.dart';
import '../../utils/services/account_filtering_service.dart';
import '../../utils/services/api_service.dart';
import '../../utils/services/api_endpoints.dart';
import '../../utils/services/cryptographer.dart';
import '../../utils/services/shared_preferences_helper.dart';
import '../../widgets/dynamic_form.dart';
import 'transaction_receipt_screen.dart';
import '../../utils/services/transaction_service.dart';
import 'otp_verification_screen.dart';
import '../../utils/services/auth_service.dart';
import '../../utils/services/token_refresh_service.dart';

import '../../utils/app_logger.dart';

class FormJourneyScreen extends StatefulWidget {
  final String serviceCode;
  final String? balanceEnquiryType;
  final List<dynamic>? accounts;
  final Map<String, dynamic>? tileResponse;
  final bool lockAccountSelection;

  const FormJourneyScreen({
    super.key,
    required this.serviceCode,
    this.balanceEnquiryType,
    this.accounts,
    this.tileResponse,
    this.lockAccountSelection = false,
  });

  @override
  _FormJourneyScreenState createState() => _FormJourneyScreenState();
}

class _FormJourneyScreenState extends State<FormJourneyScreen> {
  bool isLoading = true;
  List<FormJourneyModel> journeys = [];
  Map<String, dynamic> formData = {};
  bool isProcessing = false;
  String? errorMessage;
  final AuthService _authService = AuthService();

  Map<String, dynamic>? serviceConfig;
  List<Map<String, dynamic>> allAccounts = [];
  Map<String, List<Map<String, dynamic>>> fieldAccountOptions = {};

  // Phone number validation states
  final TextEditingController _phoneController = TextEditingController();
  bool _isValidPhone = false;
  bool _showPrefixError = false;

  // PIN visibility state
  bool _isPinVisible = false;

  static const Set<String> enquiryServiceCodes = {
    'SCBE',
    'SBE',
    'NWDB',
    'LB',
    'ASB',
    'MS',
    'AS',
  };

  static const Set<String> b2bServiceCodes = {
    'TTB', 'PTT', 'MFL', // Transfer to Bank, Pay to Till, Purchase Mpesa Float
  };

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() async {
    if (widget.accounts != null) {
      allAccounts = widget.accounts!.map((account) {
        if (account is Map) {
          return Map<String, dynamic>.from(account);
        }
        return <String, dynamic>{};
      }).toList();
    }

    _fetchServiceJourney();
  }

  Future<void> _fetchServiceJourney() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      ApiService apiService = ApiService();
      SharedPreferences prefs = await SharedPreferences.getInstance();
      var clientId = await SharedPreferencesHelper.getClientId();
      String? phoneNumber = prefs.getString('msisdn');

      if (phoneNumber == null) {
        throw Exception("Phone number not found");
      }

      AesEncryption aesEncryption = AesEncryption();
      String? secretKey = prefs.getString('_tajemnica');

      if (secretKey == null) {
        throw Exception("Secret key not found");
      }

      String? imei = await apiService.getDeviceId();

      var requestBody = {
        "categoryName": "Spotcash_App",
        "clientId": clientId,
        "serviceCode": widget.serviceCode,
        "phoneNumber":
            aesEncryption.encryptWithBase64Key(phoneNumber, secretKey),
        "imei": imei
      };

      AppLogger.info("=== FETCHING SERVICE JOURNEY ===");
      AppLogger.info("Service Code: ${widget.serviceCode}");
      AppLogger.info("Client ID: $clientId");
      AppLogger.info("Endpoint: ${ApiEndpoints.getServiceJourney}");
      AppLogger.info("Request Body: $requestBody");

      var response = await apiService.postRequest(
        ApiEndpoints.getServiceJourney,
        requestBody,
      );

      AppLogger.info("Raw response: $response");

      // Handle token refresh and decryption key like in dashboard tiles service
      String decryptionKey = secretKey;
      AppLogger.info(
          'Using initial secret key: ${secretKey.substring(0, 10)}...');

      // Handle token refresh if a new token is provided
      if (response.containsKey('znak') &&
          response['znak'] != null &&
          response['znak'].toString().isNotEmpty) {
        String responseToken = response['znak'].toString();
        AppLogger.info(
            'New token found in response: ${responseToken.substring(0, 20)}...');

        try {
          // Decode the response token to get the updated secret key
          Map<String, dynamic> tokenPayload = JwtDecoder.decode(responseToken);
          AppLogger.info('Token payload keys: ${tokenPayload.keys.toList()}');

          if (tokenPayload['tajemnica'] != null) {
            decryptionKey = tokenPayload['tajemnica'];
            AppLogger.info(
                'Using updated secret key from response token: ${decryptionKey.substring(0, 10)}...');

            // Save the new token and secret key
            await prefs.setString('token', responseToken);
            await prefs.setString('_tajemnica', decryptionKey);
            AppLogger.info('Token and secret key updated from response');
          } else {
            AppLogger.info(
                'No tajemnica found in response token payload, using stored secret key');
          }
        } catch (e) {
          AppLogger.error('processing response token: $e');
          AppLogger.info('Using stored secret key for decryption');
        }
      } else {
        AppLogger.info(
            'No new token in response (znak is null), using stored secret key');
      }

      AppLogger.info(
          'Attempting to decrypt with key: ${decryptionKey.substring(0, 10)}...');

      String decryptedResponse = aesEncryption.decryptWithBase64Key(
          response['hashedBody'], decryptionKey);

      var jsonResponse = jsonDecode(decryptedResponse);

      if (jsonResponse['responseCode'] == '00' &&
          jsonResponse['entity'] != null) {
        serviceConfig = jsonResponse['entity'];
        var entity = jsonResponse['entity'];
        var serviceJourneyString = entity['serviceJourney'];

        if (serviceJourneyString == null || serviceJourneyString.isEmpty) {
          throw Exception("Service journey is empty");
        }

        var serviceJourneyJson = jsonDecode(serviceJourneyString);

        journeys = (serviceJourneyJson as List)
            .map((journey) =>
                FormJourneyModel.fromJson(journey as Map<String, dynamic>))
            .toList();

        if (journeys.isEmpty) {
          throw Exception("No form journey data found");
        }

        setState(() {
          isLoading = false;
        });

        _initializeFormData();
      } else {
        throw Exception(
            "Failed to load service journey: ${jsonResponse['message']}");
      }
    } catch (e) {
      AppLogger.error('fetching service journey: $e');

      // Check if this is a 403 authentication error
      String errorString = e.toString();
      if (errorString.contains('HTTP 403') || errorString.contains('403')) {
        AppLogger.warning(
            'Authentication error detected (403) - token may be expired');
        setState(() {
          isLoading = false;
          errorMessage =
              'Session expired. Please try logging in again or refresh the page.';
        });
      } else {
        setState(() {
          isLoading = false;
          errorMessage = 'Please try again later.';
        });
      }
    }
  }

  void _initializeFormData() {
    for (var journey in journeys) {
      _processFieldGroupForDefaults(journey.fieldGroup);
    }

    _initializeAccountFields();
  }

  void _initializeAccountFields() {
    for (var journey in journeys) {
      _initializeAccountFieldsInGroup(journey.fieldGroup);
    }
  }

  void _initializeAccountFieldsInGroup(List<FormFieldModel> fields) {
    for (var field in fields) {
      if (field.fieldGroup != null) {
        _initializeAccountFieldsInGroup(field.fieldGroup!);
      } else if (field.fieldArray != null) {
        _initializeAccountFieldsInGroup(field.fieldArray!);
      } else if (field.responseType == 'ACCOUNTS' || field.key == 'ownAccNo') {
        _filterAndPopulateAccountField(field);
      }
    }
  }

  void _filterAndPopulateAccountField(FormFieldModel field) {
    if (allAccounts.isEmpty) {
      return;
    }

    List<Map<String, dynamic>> filteredAccounts;

    if (field.responseType == 'ACCOUNTS') {
      filteredAccounts = AccountFilterService.filterAccountsForField(
        allAccounts,
        {
          'defaultValues': field.defaultValues,
          'responseType': field.responseType,
        },
      );
    } else {
      filteredAccounts = allAccounts.cast<Map<String, dynamic>>();
    }

    fieldAccountOptions[field.key] = filteredAccounts;

    List<Map<String, dynamic>> options =
        AccountFilterService.createAccountOptions(filteredAccounts);
    field.templateOptions['options'] = options;
  }

  void _processFieldGroupForDefaults(List<FormFieldModel> fields) {
    for (var field in fields) {
      if (field.fieldGroup != null) {
        _processFieldGroupForDefaults(field.fieldGroup!);
      } else if (field.fieldArray != null) {
        _processFieldGroupForDefaults(field.fieldArray!);
      } else {
        if (field.defaultValue != null) {
          if (!formData.containsKey(field.key)) {
            formData[field.key] = field.defaultValue;
          }
        }
      }
    }
  }

  void _onFieldChanged(String key, dynamic value) {
    setState(() {
      formData[key] = value;
    });
  }

  String _getAppBarTitle() {
    if (journeys.isEmpty) return 'Transaction';

    if (journeys[0].fieldGroup.isNotEmpty) {
      var firstGroup = journeys[0].fieldGroup[0];
      if (firstGroup.templateOptions.containsKey('label')) {
        return firstGroup.templateOptions['label'];
      }
    }

    return 'Form Journey';
  }

  void _onContinue() async {
    // Prevent multiple submissions
    if (isProcessing) return;

    // Manually add any missing required data for custom UIs
    if (_isWithdrawalTransaction() ||
        _isDepositTransaction() ||
        _isInterAccountTransfer() ||
        _isUtilityBillTransaction()) {
      formData['txnType'] = widget.serviceCode;
    }

    // Add description field for utility bills
    if (_isUtilityBillTransaction()) {
      String utilityName = _getUtilityBillDisplayName();
      String utilityAccount =
          formData[_getUtilityAccountKey()]?.toString() ?? '';
      formData['description'] =
          '$utilityName payment to account $utilityAccount';
    }

    bool isValid = _validateForm();

    if (!isValid) {
      _showErrorSnackBar('Please fill all required fields');
      return;
    }

    setState(() {
      isProcessing = true;
    });

    try {
      // Get phone number and PIN for validation
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? phoneNumber = prefs.getString('msisdn');
      String? pin = formData['cmp'];

      if (pin == null || pin.isEmpty) {
        _showErrorSnackBar('Please enter your PIN.');
        setState(() {
          isProcessing = false;
        });
        return;
      }

      if (phoneNumber == null) {
        _showErrorSnackBar('Could not verify PIN. User data missing.');
        setState(() {
          isProcessing = false;
        });
        return;
      }

      // Verify the PIN before proceeding
      AuthResult pinResult =
          await _authService.verifyPin(phoneNumber, pin, context);
      if (!pinResult.isSuccess) {
        _showErrorSnackBar(pinResult.message);
        setState(() {
          isProcessing = false;
        });
        return; // Stop the process if PIN is invalid
      }

      // If PIN is valid, proceed with the transaction flow
      if (_shouldSkipConfirmation()) {
        _processTransactionDirectly();
      } else if (_isDepositTransaction()) {
        setState(() {
          isProcessing = false;
        });
        _showDepositCompletionDialog({});
      } else if (_isWithdrawalTransaction()) {
        setState(() {
          isProcessing = false;
        });
        _showWithdrawalConfirmationDialog({});
      } else if (_isInterAccountTransfer()) {
        setState(() {
          isProcessing = false;
        });
        _showInterAccountTransferConfirmationDialog({});
      } else if (_isUtilityBillTransaction()) {
        setState(() {
          isProcessing = false;
        });
        _showUtilityBillConfirmationDialog({});
      } else {
        setState(() {
          isProcessing = false;
        });
        _showTransactionConfirmation();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isProcessing = false;
        });
        _showErrorSnackBar('An error occurred. Please try again.');
      }
    }
  }

  bool _shouldSkipConfirmation() {
    if (enquiryServiceCodes.contains(widget.serviceCode)) {
      return true;
    }

    if (widget.balanceEnquiryType != null &&
        enquiryServiceCodes.contains(widget.balanceEnquiryType)) {
      return true;
    }

    return false;
  }

  void _processTransactionDirectly() {
    setState(() {
      isProcessing = true;
    });

    _processTransaction();
  }

  bool _validateForm() {
    List<String> missingFields = [];
    bool isValid = true;

    for (var journey in journeys) {
      if (!_recursiveValidate(journey.fieldGroup, missingFields)) {
        isValid = false;
      }
    }

    if (!isValid) {
      AppLogger.info(
          "Validation failed. Missing fields: ${missingFields.join(', ')}");
    }

    return isValid;
  }

  bool _recursiveValidate(
      List<FormFieldModel> fields, List<String> missingFields) {
    bool isValid = true;
    for (var field in fields) {
      if (_shouldSkipFieldValidation(field)) {
        continue;
      }

      // Recurse into nested structures first
      if (field.fieldGroup != null) {
        if (!_recursiveValidate(field.fieldGroup!, missingFields)) {
          isValid = false;
        }
      } else if (field.fieldArray != null) {
        if (!_recursiveValidate(field.fieldArray!, missingFields)) {
          isValid = false;
        }
      } else {
        // This is a leaf node, validate it.
        bool isRequired = field.templateOptions['required'] == 'true' ||
            field.templateOptions['required'] == true;
        if (isRequired) {
          if (!formData.containsKey(field.key) ||
              formData[field.key] == null ||
              formData[field.key].toString().isEmpty) {
            AppLogger.info("Missing required field: ${field.key}");
            missingFields.add(field.key);
            isValid = false;
          }
        }
      }
    }
    return isValid;
  }

  bool _shouldSkipFieldValidation(FormFieldModel field) {
    if (field.hide == true) return true;

    // Skip validation for hidden fields based on hideExpression
    if (field.hideExpression != null) {
      return _evaluateHideExpression(field.hideExpression!, formData);
    }

    return false;
  }

  bool _evaluateHideExpression(
      String expression, Map<String, dynamic> formData) {
    try {
      String evaluatedExpression = expression;

      // Replace model.fieldName with actual values
      RegExp regex = RegExp(r'model\.(\w+)');
      evaluatedExpression =
          evaluatedExpression.replaceAllMapped(regex, (match) {
        String fieldName = match.group(1)!;
        dynamic value = formData[fieldName];

        if (value == null) {
          return 'null';
        } else if (value is String) {
          return "'$value'";
        } else if (value is bool) {
          return value.toString();
        } else if (value is num) {
          return value.toString();
        } else {
          return "'$value'";
        }
      });

      // Handle != comparison
      if (evaluatedExpression.contains('!=')) {
        List<String> parts =
            evaluatedExpression.split('!=').map((e) => e.trim()).toList();
        if (parts.length == 2) {
          String left = parts[0].replaceAll("'", "");
          String right = parts[1].replaceAll("'", "");
          return left != right;
        }
      }

      // Handle == comparison
      if (evaluatedExpression.contains('==')) {
        evaluatedExpression = evaluatedExpression.replaceAll('===', '==');
        List<String> parts =
            evaluatedExpression.split('==').map((e) => e.trim()).toList();
        if (parts.length == 2) {
          String left = parts[0].replaceAll("'", "");
          String right = parts[1].replaceAll("'", "");
          return left == right;
        }
      }

      // Handle negation
      if (evaluatedExpression.startsWith('!')) {
        String value = evaluatedExpression.substring(1).trim();
        if (value == 'null') {
          return true;
        } else if (value.isEmpty || value == "''") {
          return true;
        } else {
          return false;
        }
      }

      return false;
    } catch (e) {
      AppLogger.error('evaluating hideExpression: $expression, Error: $e');
      return false;
    }
  }

  void _showTransactionConfirmation() {
    Map<String, String> details = _prepareTransactionDetails();
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // Get service title for B2B services
    String dialogTitle = 'Confirm Transaction';
    if (_isB2BService()) {
      dialogTitle = 'Confirm ${_getB2BServiceTitle()}';
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Dialog(
          insetPadding: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with purple background like masterclass design
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: ColorPalette.unselectedNavItemColor,
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(16)),
                ),
                child: Center(
                  child: Text(
                    dialogTitle,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              // Content area with transaction details
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Transaction Details header
                    Text(
                      'Transaction Details',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Transaction details with masterclass styling
                    Container(
                      decoration: BoxDecoration(
                        color: isDark ? Colors.grey[800] : Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: details.entries.map((entry) {
                          final isLast = entry == details.entries.last;
                          return _buildDetailRowWithDivider(
                            entry.key.toUpperCase(),
                            entry.value,
                            isDark,
                            isLast: isLast,
                          );
                        }).toList(),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Action buttons with masterclass styling
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () => Navigator.pop(context),
                            style: TextButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                              _processTransactionDirectly();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: ColorPalette.success,
                              foregroundColor: ColorPalette.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Confirm',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Map<String, String> _prepareTransactionDetails() {
    Map<String, String> details = {};

    // Use custom format for inter-account transfers
    if (_isInterAccountTransfer()) {
      return _prepareInterAccountTransferDetails();
    }

    for (var journey in journeys) {
      for (var field in journey.fieldGroup) {
        if (field.fieldGroup != null && field.fieldGroup!.isNotEmpty) {
          // Handle nested field groups
          for (var nestedField in field.fieldGroup!) {
            if (nestedField.hide == true) continue;

            String key = nestedField.key;
            if (formData.containsKey(key) && formData[key] != null) {
              // Skip PIN field in confirmation dialog (already validated)
              if (key == 'cmp' || key.toLowerCase().contains('pin')) {
                continue;
              }

              String label = nestedField.templateOptions['label'] ?? key;
              String value = _formatFieldValue(nestedField, formData[key]);
              details[label] = value;
            }
          }
        } else {
          // Handle direct fields (not in nested groups)
          if (field.hide == true) continue;

          String key = field.key;
          if (formData.containsKey(key) && formData[key] != null) {
            // Skip PIN field in confirmation dialog (already validated)
            if (key == 'cmp' || key.toLowerCase().contains('pin')) {
              continue;
            }

            String label = field.templateOptions['label'] ?? key;
            String value = _formatFieldValue(field, formData[key]);
            details[label] = value;
          }
        }
      }
    }

    return details;
  }

  Map<String, String> _prepareInterAccountTransferDetails() {
    Map<String, String> details = {};

    // Get transfer details from form data
    String amount = formData['amt']?.toString() ?? '0';
    String fromAccountName = 'Account';
    String accountNumber = '';
    String recipientType =
        (widget.serviceCode == 'IATOW' || widget.serviceCode == 'IAT')
            ? 'To own'
            : 'To other member';

    // Get account details
    if (allAccounts.isNotEmpty) {
      Map<String, dynamic>? fromAccount = allAccounts.firstWhere(
        (acc) => acc['accountNo'] == formData['accNo'],
        orElse: () => allAccounts.first,
      );
      fromAccountName = fromAccount['accountName'] ?? 'Account';
      accountNumber = fromAccount['accountNo'] ?? '';
    }

    details['AMOUNT'] = 'KES $amount';
    details['ACCOUNT TYPE'] = '$fromAccountName - $accountNumber';
    details['TOTAL AMOUNT'] = 'KES $amount';
    details['RECIPIENT TYPE'] = recipientType;

    return details;
  }

  String _formatFieldValue(FormFieldModel field, dynamic value) {
    if (field.templateOptions['type'] == 'date' ||
        field.key == 'startDate' ||
        field.key == 'endDate') {
      if (value is DateTime) {
        return DateFormat('MM-dd-yyyy').format(value);
      } else if (value is String) {
        try {
          DateTime date = DateTime.parse(value);
          return DateFormat('MM-dd-yyyy').format(date);
        } catch (e) {
          return value.toString();
        }
      }
    }

    if (field.type == 'select') {
      if (field.templateOptions.containsKey('options') &&
          field.templateOptions['options'] is List &&
          (field.templateOptions['options'] as List).isNotEmpty) {
        List options = field.templateOptions['options'];
        dynamic selectedOption;
        try {
          selectedOption = options.firstWhere(
            (option) => option['id'] == value || option['value'] == value,
          );
        } catch (e) {
          selectedOption = <String, dynamic>{};
        }
        if (selectedOption.isNotEmpty) {
          return selectedOption['name'] ??
              selectedOption['label'] ??
              value.toString();
        }
      }
    }

    if (field.key == 'amt' ||
        field.templateOptions['label']
                ?.toString()
                .toLowerCase()
                .contains('amount') ==
            true) {
      return 'KES $value';
    }

    if (field.type == 'password') {
      return '●●●●';
    }

    return value.toString();
  }

  void _processTransaction() {
    final transactionService = TransactionService();

    transactionService.processTransaction(
      serviceCode: widget.serviceCode,
      formData: formData,
      context: context,
      balanceEnquiryType: widget.balanceEnquiryType,
      statusCallback: (bool isLoading) {
        setState(() {
          isProcessing = isLoading;
        });
      },
      completedCallback: (Map<String, dynamic> response, bool success) {
        _handleTransactionResponse(response, success);
      },
      otpCallback: () async {
        // For withdrawal transactions, use the integrated OTP verification screen
        if (_isWithdrawalTransaction()) {
          return await _showWithdrawalOtpDialog();
        } else {
          // For other transactions, use the inline OTP dialog
          return await _showOtpDialog();
        }
      },
    );
  }

  Future<String?> _showWithdrawalOtpDialog() async {
    // Get user's phone number from SharedPreferences
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? phoneNumber = prefs.getString('msisdn') ?? 'your phone';

    // Prepare transaction details for OTP screen

    return await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: _buildSimpleOtpDialog(phoneNumber),
        );
      },
    );
  }

  Widget _buildSimpleOtpDialog(String phoneNumber) {
    final List<TextEditingController> otpControllers =
        List.generate(4, (index) => TextEditingController());
    final List<FocusNode> otpFocusNodes =
        List.generate(4, (index) => FocusNode());
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return StatefulBuilder(
      builder: (context, setState) {
        return Dialog(
          insetPadding: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[900] : Colors.grey[100],
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: ColorPalette.unselectedNavItemColor,
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(16)),
                  ),
                  child: Center(
                    child: Text(
                      'Enter OTP',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Text(
                        'Please enter the OTP sent to $phoneNumber',
                        style: TextStyle(
                          fontSize: 14,
                          color: isDark ? Colors.grey[400] : Colors.grey[700],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'If SMS fails, check your email for the OTP',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange[600],
                          fontStyle: FontStyle.italic,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: List.generate(
                          4,
                          (index) => SizedBox(
                            width: 50,
                            child: TextField(
                              controller: otpControllers[index],
                              focusNode: otpFocusNodes[index],
                              keyboardType: TextInputType.number,
                              maxLength: 1,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: isDark ? Colors.white : Colors.black87,
                              ),
                              decoration: InputDecoration(
                                counterText: '',
                                filled: true,
                                fillColor:
                                    isDark ? Colors.grey[800] : Colors.white,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide.none,
                                ),
                              ),
                              onChanged: (value) {
                                if (value.isNotEmpty && index < 3) {
                                  otpFocusNodes[index + 1].requestFocus();
                                } else if (value.isEmpty && index > 0) {
                                  otpFocusNodes[index - 1].requestFocus();
                                }
                              },
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TextButton(
                            onPressed: () {
                              for (var controller in otpControllers) {
                                controller.dispose();
                              }
                              for (var node in otpFocusNodes) {
                                node.dispose();
                              }
                              Navigator.pop(context, null);
                            },
                            style: TextButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text('Cancel'),
                          ),
                          ElevatedButton(
                            onPressed: () {
                              String otp = otpControllers
                                  .map((controller) => controller.text)
                                  .join();
                              if (otp.length != 4) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                        'Please enter a valid 4-digit OTP'),
                                    backgroundColor: Colors.red,
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                                return;
                              }

                              for (var controller in otpControllers) {
                                controller.dispose();
                              }
                              for (var node in otpFocusNodes) {
                                node.dispose();
                              }
                              Navigator.pop(context, otp);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: ColorPalette.primary,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text('Verify'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<String?> _showOtpDialog() async {
    // Get user's phone number from SharedPreferences
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? phoneNumber = prefs.getString('msisdn') ?? 'your phone';

    return await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        final List<TextEditingController> otpControllers =
            List.generate(4, (index) => TextEditingController());
        final List<FocusNode> otpFocusNodes =
            List.generate(4, (index) => FocusNode());
        final isDark = Theme.of(context).brightness == Brightness.dark;
        bool isValidating = false;

        return BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: StatefulBuilder(
              builder: (context, setState) {
                return Dialog(
                  insetPadding: const EdgeInsets.all(16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      color: isDark ? Colors.grey[900] : Colors.grey[100],
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: ColorPalette.unselectedNavItemColor,
                            borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(16)),
                          ),
                          child: Center(
                            child: Text(
                              'Enter OTP',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: [
                              Text(
                                'Please enter the OTP sent to $phoneNumber',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: isDark
                                      ? Colors.grey[400]
                                      : Colors.grey[700],
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 24),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: List.generate(
                                  4,
                                  (index) => SizedBox(
                                    width: 50,
                                    child: TextField(
                                      controller: otpControllers[index],
                                      focusNode: otpFocusNodes[index],
                                      keyboardType: TextInputType.number,
                                      maxLength: 1,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        color: isDark
                                            ? Colors.white
                                            : Colors.black87,
                                      ),
                                      decoration: InputDecoration(
                                        counterText: '',
                                        filled: true,
                                        fillColor: isDark
                                            ? Colors.grey[800]
                                            : Colors.white,
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          borderSide: BorderSide.none,
                                        ),
                                      ),
                                      onChanged: (value) {
                                        if (value.isNotEmpty && index < 3) {
                                          otpFocusNodes[index + 1]
                                              .requestFocus();
                                        } else if (value.isEmpty && index > 0) {
                                          otpFocusNodes[index - 1]
                                              .requestFocus();
                                        }
                                      },
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 24),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  TextButton(
                                    onPressed: () {
                                      for (var controller in otpControllers) {
                                        controller.dispose();
                                      }
                                      for (var node in otpFocusNodes) {
                                        node.dispose();
                                      }
                                      Navigator.pop(context, null);
                                    },
                                    style: TextButton.styleFrom(
                                      backgroundColor: Colors.red,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 24,
                                        vertical: 12,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: Text('Cancel'),
                                  ),
                                  ElevatedButton(
                                    onPressed: isValidating
                                        ? null
                                        : () {
                                            String otp = otpControllers
                                                .map((controller) =>
                                                    controller.text)
                                                .join();
                                            if (otp.length != 4) {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(
                                                SnackBar(
                                                  content: Text(
                                                      'Please enter a valid 4-digit OTP'),
                                                  backgroundColor: Colors.red,
                                                  behavior:
                                                      SnackBarBehavior.floating,
                                                ),
                                              );
                                              return;
                                            }

                                            for (var controller
                                                in otpControllers) {
                                              controller.dispose();
                                            }
                                            for (var node in otpFocusNodes) {
                                              node.dispose();
                                            }
                                            Navigator.pop(context, otp);
                                          },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: ColorPalette.primary,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 24,
                                        vertical: 12,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: isValidating
                                        ? const SizedBox(
                                            height: 16,
                                            width: 16,
                                            child: CircularProgressIndicator(
                                              color: Colors.white,
                                              strokeWidth: 2.0,
                                            ),
                                          )
                                        : Text('Verify'),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ));
      },
    );
  }

  void _handleTransactionResponse(Map<String, dynamic> response, bool success) {
    if (success) {
      if (_shouldSkipConfirmation()) {
        _showEnquiryResults(response);
      } else if (_isDepositTransaction()) {
        _showDepositInitiatedModal(response);
      } else if (_isWithdrawalTransaction()) {
        // For withdrawals, OTP is now handled by the transaction service
        // So if we reach here, the transaction (including OTP) was successful
        _showTransactionSuccess(response);
      } else if (_isUtilityBillTransaction()) {
        _showUtilityBillOtpVerification(response);
      } else {
        _showTransactionSuccess(response);
      }
    } else {
      String errorMessage = _extractErrorMessage(response);
      _showTransactionError(errorMessage);
    }
  }

  void _showEnquiryResults(Map<String, dynamic> response) {
    Map<String, String> details = {};
    String title = _getEnquiryDisplayName();

    // Extract enquiry-specific data
    if (_isBalanceEnquiry()) {
      _extractBalanceEnquiryDetails(response, details);
    } else if (_isStatementEnquiry()) {
      _extractStatementEnquiryDetails(response, details);
    } else {
      _extractGenericEnquiryDetails(response, details);
    }

    if (details.isEmpty) {
      _addFallbackEnquiryDetails(details, response);
    }

    _showResultDialog(title, details, true);
  }

  void _showTransactionSuccess(Map<String, dynamic> response) {
    Map<String, String> details = _prepareTransactionDetails();

    // Add transaction reference if available
    if (response.containsKey('header') &&
        response['header'].containsKey('transactionId')) {
      details['Transaction Reference'] = response['header']['transactionId'];
    }

    if (response.containsKey('responseData') &&
        response['responseData'].containsKey('transactionId')) {
      details['Transaction Reference'] =
          response['responseData']['transactionId'];
    }

    _showResultDialog('Transaction Successful', details, true);
  }

  void _showTransactionError(String errorMessage) {
    showDialog(
        context: context,
        builder: (context) => AlertDialog(
              title: Text(
                'Transaction Failed',
                style: TextStyle(
                  color: ColorPalette.error,
                  fontFamily:
                      ClientThemeManager().currentClientConfig.fontFamily,
                ),
              ),
              content: Text(
                errorMessage,
                style: TextStyle(
                  fontFamily:
                      ClientThemeManager().currentClientConfig.fontFamily,
                ),
              ),
              actions: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    Navigator.pop(context); // Go back to previous screen
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorPalette.primary,
                  ),
                  child: Text(
                    'OK',
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily:
                          ClientThemeManager().currentClientConfig.fontFamily,
                    ),
                  ),
                ),
              ],
            ));
  }

  void _showResultDialog(
      String title, Map<String, String> details, bool isSuccess) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: Dialog(
                insetPadding: const EdgeInsets.all(20),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    color: isDark ? Colors.grey[900] : Colors.white,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with title and icon
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: ColorPalette.unselectedNavItemColor,
                          borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(16)),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              isSuccess ? Icons.check_circle : Icons.error,
                              color: Colors.white,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                title,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Content with details
                      Container(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Transaction Details header
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.only(bottom: 16),
                              child: Text(
                                'Transaction Details',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: isDark ? Colors.white : Colors.black87,
                                ),
                              ),
                            ),

                            // Details list with clean spacing
                            ...details.entries
                                .map((entry) => Container(
                                      margin: const EdgeInsets.only(bottom: 16),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 12),
                                      decoration: BoxDecoration(
                                        color: isDark
                                            ? Colors.grey[800]
                                            : Colors.grey[50],
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            flex: 2,
                                            child: Text(
                                              entry.key.toUpperCase(),
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w500,
                                                color: isDark
                                                    ? Colors.grey[400]
                                                    : Colors.grey[600],
                                                letterSpacing: 0.5,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          Expanded(
                                            flex: 3,
                                            child: Text(
                                              entry.value,
                                              textAlign: TextAlign.right,
                                              style: TextStyle(
                                                fontSize: 14,
                                                fontWeight: entry.key
                                                        .toLowerCase()
                                                        .contains('balance')
                                                    ? FontWeight.bold
                                                    : FontWeight.w500,
                                                color: entry.key
                                                        .toLowerCase()
                                                        .contains('balance')
                                                    ? (isDark
                                                        ? Colors.green[300]
                                                        : Colors.green[700])
                                                    : (isDark
                                                        ? Colors.white
                                                        : Colors.black87),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ))
                                .toList(),

                            const SizedBox(height: 8),

                            // Done button
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                  Navigator.pop(
                                      context); // Go back to dashboard
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: ColorPalette.primary,
                                  foregroundColor: Colors.white,
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: 0,
                                ),
                                child: Text(
                                  'Done',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    fontFamily: ClientThemeManager()
                                        .currentClientConfig
                                        .fontFamily,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ));
  }

  // Check if this is a balance enquiry
  bool _isBalanceEnquiry() {
    Set<String> balanceEnquiryCodes = {'SCBE', 'SBE', 'NWDB', 'LB', 'ASB'};
    return balanceEnquiryCodes.contains(widget.serviceCode) ||
        balanceEnquiryCodes.contains(widget.balanceEnquiryType);
  }

  // Check if this is a statement enquiry
  bool _isStatementEnquiry() {
    Set<String> statementCodes = {'MS', 'AS'};
    return statementCodes.contains(widget.serviceCode) ||
        statementCodes.contains(widget.balanceEnquiryType);
  }

  // Check if this is a B2B service
  bool _isB2BService() {
    return b2bServiceCodes.contains(widget.serviceCode);
  }

  // Check if this is Transfer to Bank

  // Check if this is Pay to Till

  // Check if this is Purchase Mpesa Float

  // Get display name for enquiry type
  String _getEnquiryDisplayName() {
    String serviceCode = widget.balanceEnquiryType ?? widget.serviceCode;

    switch (serviceCode) {
      case 'SCBE':
        return 'Share Balance Enquiry';
      case 'SBE':
        return 'Savings Balance Enquiry';
      case 'NWDB':
        return 'Investment Balance Enquiry';
      case 'LB':
        return 'Loan Balance Enquiry';
      case 'ASB':
        return 'All Balances Enquiry';
      case 'MS':
        return 'Mini Statement';
      case 'AS':
        return 'Full Statement';
      default:
        return 'Balance Enquiry';
    }
  }

  // Extract balance enquiry details
  void _extractBalanceEnquiryDetails(
      Map<String, dynamic> response, Map<String, String> details) {
    if (response.containsKey('header')) {
      if (response['header'].containsKey('customerName')) {
        details['Account Holder'] = response['header']['customerName'];
      }

      // Balance from header 'st' field
      if (response['header'].containsKey('st')) {
        String balanceStr = response['header']['st'].toString();
        if (balanceStr.isNotEmpty && balanceStr != '0') {
          double balance = _parseAmount(balanceStr);
          details['Available Balance'] =
              'KES ${NumberFormat("#,##0.00").format(balance)}';
        }
      }
    }

    if (response.containsKey('responseData')) {
      var responseData = response['responseData'];

      // Account information
      if (responseData.containsKey('accountType')) {
        details['Account Type'] = responseData['accountType'];
      }
      if (responseData.containsKey('accountName')) {
        details['Account Name'] = responseData['accountName'];
      }
      if (responseData.containsKey('accountNo')) {
        details['Account Number'] = responseData['accountNo'];
      }

      // Balance information from 'msm' field (main balance field)
      if (responseData.containsKey('msm')) {
        String balanceStr = responseData['msm'].toString();
        if (balanceStr.isNotEmpty && balanceStr != '0') {
          double balance = _parseAmount(balanceStr);
          details['Available Balance'] =
              'KES ${NumberFormat("#,##0.00").format(balance)}';
        }
      }

      // Alternative balance fields
      if (responseData.containsKey('balance')) {
        double balance = _parseAmount(responseData['balance']);
        details['Available Balance'] =
            'KES ${NumberFormat("#,##0.00").format(balance)}';
      }
      if (responseData.containsKey('ledgerBalance')) {
        double ledgerBalance = _parseAmount(responseData['ledgerBalance']);
        details['Ledger Balance'] =
            'KES ${NumberFormat("#,##0.00").format(ledgerBalance)}';
      }

      // Additional balance details
      if (responseData.containsKey('minBalance')) {
        double minBalance = _parseAmount(responseData['minBalance']);
        details['Minimum Balance'] =
            'KES ${NumberFormat("#,##0.00").format(minBalance)}';
      }
      if (responseData.containsKey('maxWithdrawable')) {
        double maxWithdrawable = _parseAmount(responseData['maxWithdrawable']);
        details['Max Withdrawable'] =
            'KES ${NumberFormat("#,##0.00").format(maxWithdrawable)}';
      }

      // Transaction charges
      if (responseData.containsKey('spotcashCommision') &&
          responseData['spotcashCommision'] != null) {
        double commission = _parseAmount(responseData['spotcashCommision']);
        if (commission > 0) {
          details['Service Charge'] =
              'KES ${NumberFormat("#,##0.00").format(commission)}';
        }
      }

      // Transaction ID
      if (responseData.containsKey('txnId')) {
        details['Transaction ID'] = responseData['txnId'];
      }
    }

    details['Enquiry Date'] =
        DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now());
  }

  // Extract statement enquiry details
  void _extractStatementEnquiryDetails(
      Map<String, dynamic> response, Map<String, String> details) {
    if (response.containsKey('header')) {
      if (response['header'].containsKey('customerName')) {
        details['Account Holder'] = response['header']['customerName'];
      }
    }

    if (response.containsKey('responseData')) {
      var responseData = response['responseData'];

      if (responseData.containsKey('accountName')) {
        details['Account Name'] = responseData['accountName'];
      }
      if (responseData.containsKey('accountNo')) {
        details['Account Number'] = responseData['accountNo'];
      }

      // Statement specific details
      if (responseData.containsKey('statementPeriod')) {
        details['Statement Period'] = responseData['statementPeriod'];
      }
      if (responseData.containsKey('emailAddress')) {
        details['Email Address'] = responseData['emailAddress'];
      }
    }

    details['Request Date'] =
        DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now());
  }

  // Extract generic enquiry details
  void _extractGenericEnquiryDetails(
      Map<String, dynamic> response, Map<String, String> details) {
    if (response.containsKey('responseData')) {
      var responseData = response['responseData'];
      responseData.forEach((key, value) {
        if (value != null && value.toString().isNotEmpty) {
          details[_formatFieldName(key)] = value.toString();
        }
      });
    }
  }

  // Add fallback enquiry details
  void _addFallbackEnquiryDetails(
      Map<String, String> details, Map<String, dynamic> response) {
    if (response.containsKey('header') &&
        response['header'].containsKey('responseMessage')) {
      details['Result'] = response['header']['responseMessage'];
    } else if (response.containsKey('responseMessage')) {
      details['Result'] = response['responseMessage'];
    } else {
      details['Status'] = 'Enquiry completed successfully';
    }
  }

  // Parse amount from string/dynamic
  double _parseAmount(dynamic amount) {
    if (amount is double) return amount;
    if (amount is int) return amount.toDouble();
    if (amount is String) {
      try {
        return double.parse(amount.replaceAll(',', ''));
      } catch (e) {
        return 0.0;
      }
    }
    return 0.0;
  }

  // Format field name for display
  String _formatFieldName(String fieldName) {
    return fieldName
        .replaceAllMapped(RegExp(r'([A-Z])'), (match) => ' ${match.group(1)}')
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) => word.isNotEmpty
            ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
            : '')
        .join(' ')
        .trim();
  }

  // Extract error message from response
  String _extractErrorMessage(Map<String, dynamic> response) {
    AppLogger.info('Extracting error message from response');
    AppLogger.info('Response keys: ${response.keys.toList()}');

    // Log key fields for debugging
    if (response.containsKey('header')) {
      AppLogger.info('Header: ${response['header']}');
    }
    if (response.containsKey('responseData')) {
      AppLogger.info('ResponseData: ${response['responseData']}');
    }

    // Check for direct error field
    if (response.containsKey('error') && response['error'] != null) {
      String errorMsg = response['error'].toString().trim();
      if (errorMsg.isNotEmpty) {
        AppLogger.info('Found error in "error" field: $errorMsg');
        return errorMsg;
      }
    }

    // Check header for error messages - this is the primary source of error info
    if (response.containsKey('header') && response['header'] is Map) {
      var header = response['header'] as Map<String, dynamic>;

      // First check if this is actually an error based on status code
      String? statusCode = header['sc']?.toString();
      bool isError = statusCode != null && statusCode != '00';

      if (isError) {
        AppLogger.info('Transaction failed with status code: $statusCode');

        // Look for error message in order of preference
        // 1. Check sd (status description) - descriptive error message
        if (header.containsKey('sd') && header['sd'] != null) {
          String msg = header['sd'].toString().trim();
          if (msg.isNotEmpty) {
            AppLogger.info('Found error in header.sd: $msg');
            return msg;
          }
        }

        // 2. Check st (status code) - fallback error code
        if (header.containsKey('st') && header['st'] != null) {
          String msg = header['st'].toString().trim();
          if (msg.isNotEmpty) {
            AppLogger.info('Found error in header.st: $msg');
            return msg;
          }
        }

        // 3. Check responseMessage
        if (header.containsKey('responseMessage') &&
            header['responseMessage'] != null) {
          String msg = header['responseMessage'].toString().trim();
          if (msg.isNotEmpty) {
            AppLogger.info('Found error in header.responseMessage: $msg');
            return msg;
          }
        }

        // 4. Check message
        if (header.containsKey('message') && header['message'] != null) {
          String msg = header['message'].toString().trim();
          if (msg.isNotEmpty) {
            AppLogger.info('Found error in header.message: $msg');
            return msg;
          }
        }

        // 5. If no specific message found, return generic error with status code
        AppLogger.info('No specific error message found, using status code');
        return 'Transaction failed (Code: $statusCode)';
      }
    }

    // Check top-level fields as fallback (for non-standard responses)
    if (response.containsKey('message') && response['message'] != null) {
      String msg = response['message'].toString().trim();
      if (msg.isNotEmpty && msg.toLowerCase() != 'success') {
        AppLogger.info('Found error in top-level message: $msg');
        return msg;
      }
    }

    // Check responseData for error information
    if (response.containsKey('responseData') &&
        response['responseData'] is Map) {
      var responseData = response['responseData'] as Map<String, dynamic>;

      // Check sd in responseData
      if (responseData.containsKey('sd') && responseData['sd'] != null) {
        String msg = responseData['sd'].toString().trim();
        if (msg.isNotEmpty &&
            msg.toLowerCase() != 'success' &&
            msg.toLowerCase() != 'transaction success') {
          AppLogger.info('Found error in responseData.sd: $msg');
          return msg;
        }
      }

      // Check st in responseData
      if (responseData.containsKey('st') && responseData['st'] != null) {
        String msg = responseData['st'].toString().trim();
        if (msg.isNotEmpty &&
            msg.toLowerCase() != 'success' &&
            msg.toLowerCase() != 'transaction success' &&
            !msg.contains('successful')) {
          AppLogger.info('Found error in responseData.st: $msg');
          return msg;
        }
      }

      if (responseData.containsKey('errorMessage') &&
          responseData['errorMessage'] != null) {
        String msg = responseData['errorMessage'].toString().trim();
        if (msg.isNotEmpty) {
          AppLogger.info('Found error in responseData.errorMessage: $msg');
          return msg;
        }
      }

      if (responseData.containsKey('message') &&
          responseData['message'] != null) {
        String msg = responseData['message'].toString().trim();
        if (msg.isNotEmpty && msg.toLowerCase() != 'success') {
          AppLogger.info('Found error in responseData.message: $msg');
          return msg;
        }
      }
    }

    // Check responseCode for specific error codes
    if (response.containsKey('responseCode')) {
      String code = response['responseCode'].toString();
      if (code != '00' && code != '0') {
        // Try to get a meaningful message for the error code
        String codeMessage = _getErrorMessageForCode(code);
        if (codeMessage.isNotEmpty) {
          AppLogger.info('Found error code $code with message: $codeMessage');
          return codeMessage;
        }
      }
    }

    AppLogger.warning(
        'No specific error message found in response, using fallback');
    return 'Transaction failed. Please try again.';
  }

  // Get error message for specific response codes
  String _getErrorMessageForCode(String code) {
    switch (code) {
      case '01':
        return 'Insufficient funds. Please check your account balance.';
      case '02':
        return 'Invalid account. Please verify your account details.';
      case '03':
        return 'Transaction limit exceeded. Please try a smaller amount.';
      case '04':
        return 'Invalid PIN. Please check your PIN and try again.';
      case '05':
        return 'Account is blocked. Please contact customer support.';
      case '06':
        return 'Service temporarily unavailable. Please try again later.';
      case '07':
        return 'Network error. Please check your connection and try again.';
      case '08':
        return 'Transaction timeout. Please try again.';
      case '09':
        return 'Invalid transaction amount. Please enter a valid amount.';
      case '10':
        return 'Account not found. Please verify your account number.';
      case '22':
        return 'OTP verification required. Please complete OTP verification.';
      default:
        return 'Transaction failed with error code: $code';
    }
  }

  // Check if this is a deposit transaction
  bool _isDepositTransaction() {
    return widget.serviceCode.contains('STK') ||
        widget.serviceCode == 'DEP' ||
        widget.serviceCode == 'DP' ||
        widget.serviceCode.startsWith('STK') ||
        (widget.tileResponse != null &&
            widget.tileResponse!['name'] == 'deposit');
  }

  // Check if this is a withdrawal transaction
  bool _isWithdrawalTransaction() {
    return widget.serviceCode == 'WD' ||
        (widget.tileResponse != null &&
            widget.tileResponse!['name'] == 'withdraw');
  }

  // Check if this is an inter-account transfer
  bool _isInterAccountTransfer() {
    return widget.serviceCode == 'IATOW' ||
        widget.serviceCode == 'IATOT' ||
        widget.serviceCode == 'IAT';
  }

  // Check if this is a utility bill transaction
  bool _isUtilityBillTransaction() {
    return widget.serviceCode == 'DSTV' ||
        widget.serviceCode == 'ZUKU' ||
        widget.serviceCode == 'STAR' ||
        widget.serviceCode == 'NWTR';
  }

  // Get utility bill display name
  String _getUtilityBillDisplayName() {
    switch (widget.serviceCode) {
      case 'DSTV':
        return 'DSTV';
      case 'ZUKU':
        return 'Zuku';
      case 'STAR':
        return 'Star Times';
      case 'NWTR':
        return 'Nairobi Water';
      default:
        return 'Utility Payment';
    }
  }

  // Get utility bill color
  Color _getUtilityBillColor() {
    switch (widget.serviceCode) {
      case 'DSTV':
        return Color(0xFF1E3A8A); // Blue for DSTV
      case 'ZUKU':
        return Color(0xFFDC2626); // Red for Zuku
      case 'STAR':
        return Color(0xFF059669); // Green for Star Times
      case 'NWTR':
        return Color(0xFF0EA5E9); // Light blue for Nairobi Water
      default:
        return ColorPalette.primary;
    }
  }

  /// Build custom withdrawal UI with your original design
  Widget _buildWithdrawalUI() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    String? selectedAccount;
    Map<String, dynamic>? selectedAccountData;

    // Get account from form data if available
    if (formData.containsKey('accNo') && allAccounts.isNotEmpty) {
      selectedAccountData = allAccounts.firstWhere(
        (account) => account['accountNo'] == formData['accNo'],
        orElse: () => allAccounts.first,
      );
      selectedAccount = selectedAccountData['accountName'];
    } else if (allAccounts.isNotEmpty) {
      selectedAccountData = allAccounts.first;
      selectedAccount = selectedAccountData['accountName'];
    }

    // Ensure the default account number is set in formData if not already present.
    if (selectedAccountData != null && formData['accNo'] == null) {
      formData['accNo'] = selectedAccountData['accountNo'];
    }

    // Get dynamic color based on selected account
    Color accountColor = selectedAccountData != null
        ? _getAccountColor(selectedAccountData)
        : ColorPalette.secondary;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: accountColor, // Use dynamic account color
        title: Text(
          'Withdraw',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
            fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
      ),
      body: Column(
        children: [
          // Account Info Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: accountColor, // Use dynamic account color
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'From Account',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonHideUnderline(
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                      ),
                    ),
                    child: DropdownButton<String>(
                      value: selectedAccount,
                      isExpanded: true,
                      dropdownColor: accountColor, // Use dynamic account color
                      hint: Text(
                        'Select account',
                        style: const TextStyle(color: Colors.white70),
                      ),
                      items: allAccounts
                          .map((account) => DropdownMenuItem<String>(
                                value: account['accountName'],
                                child: Text(
                                  account['accountName'],
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ))
                          .toList(),
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          setState(() {
                            var newSelectedAccountData = allAccounts.firstWhere(
                              (account) => account['accountName'] == newValue,
                            );
                            formData['accNo'] =
                                newSelectedAccountData['accountNo'];
                            // This will trigger a rebuild with new colors
                          });
                        }
                      },
                      icon: Icon(Icons.arrow_drop_down, color: Colors.white),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Available Balance',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  selectedAccount != null
                      ? '${allAccounts.firstWhere((account) => account['accountName'] == selectedAccount)['balance'] ?? '0'} KES'
                      : '0 KES',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Form Content
          Expanded(
            child: Container(
              color: isDark
                  ? Theme.of(context).scaffoldBackgroundColor
                  : ColorPalette.greyBackground,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Transaction Details',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: ColorPalette
                            .primary, // Use green color like original design
                        fontFamily:
                            ClientThemeManager().currentClientConfig.fontFamily,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Recipient Field
                    Text(
                      'Recipient',
                      style: TextStyle(
                        color: ColorPalette
                            .primary, // Use green color like original design
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonHideUnderline(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: isDark ? Colors.grey[800] : Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                              color: isDark
                                  ? Colors.grey[600]!
                                  : Colors.grey.shade300),
                        ),
                        child: DropdownButton<String>(
                          value: formData['recipientType'] ?? 'to own',
                          isExpanded: true,
                          items: [
                            DropdownMenuItem(
                                value: 'to own',
                                child: Text('To own',
                                    style: TextStyle(
                                        color: isDark
                                            ? Colors.white
                                            : Colors.black))),
                            DropdownMenuItem(
                                value: 'to other',
                                child: Text('To other',
                                    style: TextStyle(
                                        color: isDark
                                            ? Colors.white
                                            : Colors.black))),
                          ],
                          onChanged: (value) {
                            setState(() {
                              formData['recipientType'] = value;
                              // Clear phone number when switching back to "to own"
                              if (value == 'to own') {
                                _phoneController.clear();
                                formData['phoneNumber'] = '';
                                _isValidPhone = false;
                                _showPrefixError = false;
                              }
                            });
                          },
                          icon: Icon(Icons.arrow_drop_down,
                              color: isDark ? Colors.grey[400] : Colors.grey),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Phone Number Field - Only show when "To other" is selected
                    if (formData['recipientType'] == 'to other') ...[
                      Text(
                        'Phone Number',
                        style: TextStyle(
                          color: ColorPalette.primary,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _phoneController,
                        keyboardType: TextInputType.phone,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(
                              12), // Max 12 digits for numbers starting with 254
                          TextInputFormatter.withFunction((oldValue, newValue) {
                            final text = newValue.text;
                            // If text starts with 254, limit to 12 digits
                            if (text.startsWith('254')) {
                              if (text.length > 12) {
                                return oldValue;
                              }
                            }
                            // If text starts with 0, limit to 10 digits
                            else if (text.startsWith('0')) {
                              if (text.length > 10) {
                                return oldValue;
                              }
                            }
                            return newValue;
                          }),
                        ],
                        onChanged: (value) {
                          final isValidPrefix = value.startsWith('07') ||
                              value.startsWith('01') ||
                              value.startsWith('254');
                          setState(() {
                            _isValidPhone = RegExp(r'^(07|01)\d{8}$|^254\d{9}$')
                                .hasMatch(value);
                            _showPrefixError =
                                !isValidPrefix && value.isNotEmpty;
                            formData['phoneNumber'] = value;
                          });
                        },
                        decoration: InputDecoration(
                          hintText: 'Enter phone number',
                          filled: true,
                          fillColor: isDark ? Colors.grey[800] : Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                                color: isDark
                                    ? Colors.grey[600]!
                                    : Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                                color: ColorPalette.primary, width: 2),
                          ),
                          suffixIcon: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (_isValidPhone)
                                Icon(
                                  Icons.check_circle,
                                  color: ColorPalette.primary,
                                ),
                              IconButton(
                                icon: Icon(
                                  Icons.contact_phone,
                                  color: Colors.grey.shade600,
                                ),
                                onPressed: () async {
                                  await _pickContact();
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (_showPrefixError && _phoneController.text.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0, top: 4.0),
                          child: Text(
                            'Phone number must start with 07, 01, or 254',
                            style: TextStyle(
                              color: Colors.red,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      const SizedBox(height: 20),
                    ],

                    // Amount Field
                    Text(
                      'Amount',
                      style: TextStyle(
                        color: ColorPalette
                            .primary, // Use green color like original design
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      keyboardType: TextInputType.number,
                      onChanged: (value) => formData['amt'] = value,
                      decoration: InputDecoration(
                        hintText: 'Enter amount',
                        prefixIcon: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 16),
                          child: Text(
                            'KES ',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: ColorPalette
                                  .primary, // Use green color like original design
                              fontFamily: ClientThemeManager()
                                  .currentClientConfig
                                  .fontFamily,
                            ),
                          ),
                        ),
                        filled: true,
                        fillColor: isDark ? Colors.grey[800] : Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: isDark
                                  ? Colors.grey[600]!
                                  : Colors.grey.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: ColorPalette.primary,
                              width: 2), // Use green color like original design
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // PIN Field
                    Text(
                      'PIN',
                      style: TextStyle(
                        color: ColorPalette
                            .primary, // Use green color like original design
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      obscureText: !_isPinVisible,
                      keyboardType: TextInputType.number,
                      maxLength: 4,
                      onChanged: (value) => formData['cmp'] = value,
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: isDark ? Colors.grey[800] : Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: isDark
                                  ? Colors.grey[600]!
                                  : Colors.grey.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: ColorPalette.primary,
                              width: 2), // Use green color like original design
                        ),
                        prefixIcon: Icon(
                          Icons.lock_outline,
                          color: ColorPalette
                              .primary, // Use green color like original design
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _isPinVisible
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: isDark ? Colors.grey[400] : Colors.grey,
                          ),
                          onPressed: () {
                            setState(() {
                              _isPinVisible = !_isPinVisible;
                            });
                          },
                        ),
                      ),
                      style: TextStyle(
                        fontSize: 20,
                        letterSpacing: 8,
                        color: isDark ? Colors.white : Colors.black,
                      ),
                    ),
                    const SizedBox(height: 40),

                    // Action Buttons
                    Padding(
                      padding: const EdgeInsets.only(bottom: 20),
                      child: Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: isProcessing
                                  ? null
                                  : () => Navigator.pop(context),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: isProcessing
                                    ? Colors.grey[200]
                                    : Colors.grey[300],
                                foregroundColor: Colors.black87,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 15),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                elevation: 0,
                              ),
                              child: Text(
                                'Cancel',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: isProcessing ? null : _onContinue,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: isProcessing
                                    ? Colors.grey[400]
                                    : ColorPalette.primary,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 15),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: isProcessing
                                  ? Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          height: 16,
                                          width: 16,
                                          child: CircularProgressIndicator(
                                            color: Colors.white,
                                            strokeWidth: 2.0,
                                          ),
                                        ),
                                        SizedBox(width: 8),
                                        Text(
                                          'Processing...',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    )
                                  : Text(
                                      'Continue',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a custom UI for Inter Account Transfers
  Widget _buildInterAccountTransferUI() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    // This will hold the selected "To Account" number

    // Get the "From Account" based on what was passed to the screen
    Map<String, dynamic>? fromAccountData;
    if (allAccounts.isNotEmpty) {
      fromAccountData = allAccounts.first;
      // Set the from account in form data automatically
      formData['accNo'] = fromAccountData['accountNo'];
    }

    // Filter the list of "To Accounts" to exclude the "From Account"
    final List<Map<String, dynamic>> toAccountOptions = allAccounts
        .where((acc) => acc['accountNo'] != fromAccountData?['accountNo'])
        .toList();

    // Get dynamic color based on the from account
    Color accountColor = fromAccountData != null
        ? _getAccountColor(fromAccountData)
        : ColorPalette.secondary;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: accountColor,
        title: Text(
          'Inter account',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
            fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
      ),
      body: Column(
        children: [
          // "From Account" Info Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: accountColor,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'From Account',
                  style: const TextStyle(color: Colors.white70, fontSize: 14),
                ),
                const SizedBox(height: 8),
                Text(
                  fromAccountData?['accountName'] ?? 'Unknown Account',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Available Balance',
                  style: TextStyle(color: Colors.white70, fontSize: 14),
                ),
                const SizedBox(height: 8),
                Text(
                  '${fromAccountData?['balance'] ?? '0.00'} KES',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Form Content
          Expanded(
            child: Container(
              color: isDark ? Colors.grey[900] : ColorPalette.greyBackground,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Transaction Details',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: ColorPalette.primary,
                        fontFamily:
                            ClientThemeManager().currentClientConfig.fontFamily,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Recipient Field (To own / To other member)
                    // We can simplify this since we have specific service codes
                    _buildFormLabel('Recipient'),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 16),
                      decoration: BoxDecoration(
                        color: isDark ? Colors.grey[800] : Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                            color: isDark
                                ? Colors.grey[600]!
                                : Colors.grey.shade300),
                      ),
                      child: Text(
                        widget.serviceCode == 'IATOW'
                            ? 'To own'
                            : 'To other member',
                        style: TextStyle(
                          fontSize: 16,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Conditionally show "To Account" dropdown or Phone Number field
                    if (widget.serviceCode == 'IATOW') ...[
                      // "To Account" Dropdown for transfers to own account
                      _buildFormLabel('To Account'),
                      DropdownButtonFormField<String>(
                        decoration: _getInputDecoration(
                            hint: 'Select destination account'),
                        items: toAccountOptions
                            .map((account) => DropdownMenuItem<String>(
                                  value: account['accountNo'],
                                  child: Text(account['accountName']),
                                ))
                            .toList(),
                        onChanged: (String? newValue) {
                          formData['otherAccNo'] =
                              newValue; // Use the correct key for validation
                        },
                        validator: (value) =>
                            value == null ? 'Please select an account' : null,
                      ),
                    ] else ...[
                      // Phone Number & Account for transfers to another member
                      _buildFormLabel('Beneficiary Phone Number'),
                      TextFormField(
                        controller: _phoneController,
                        keyboardType: TextInputType.phone,
                        onChanged: (value) {
                          final isValidPrefix = value.startsWith('07') ||
                              value.startsWith('01') ||
                              value.startsWith('254');
                          setState(() {
                            _isValidPhone = RegExp(r'^(07|01)\d{8}$|^254\d{9}$')
                                .hasMatch(value);
                            _showPrefixError =
                                !isValidPrefix && value.isNotEmpty;
                            formData['beneficiaryPhone'] = value;
                          });
                        },
                        decoration:
                            _getInputDecoration(hint: 'Enter phone number')
                                .copyWith(
                          suffixIcon: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (_isValidPhone)
                                Icon(Icons.check_circle,
                                    color: ColorPalette.primary),
                              IconButton(
                                icon: Icon(Icons.contact_phone,
                                    color: Colors.grey.shade600),
                                onPressed: _pickContact,
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (_showPrefixError && _phoneController.text.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0, top: 4.0),
                          child: Text(
                            'Phone number must start with 07, 01, or 254',
                            style: TextStyle(color: Colors.red, fontSize: 12),
                          ),
                        ),
                      const SizedBox(height: 20),

                      _buildFormLabel('Beneficiary Account Number'),
                      TextFormField(
                        keyboardType: TextInputType.text,
                        onChanged: (value) => formData['otherAccNo'] =
                            value, // Use the correct key for validation
                        decoration:
                            _getInputDecoration(hint: 'Enter account number'),
                      ),
                    ],
                    const SizedBox(height: 20),

                    // Amount Field
                    _buildFormLabel('Amount'),
                    TextFormField(
                      keyboardType: TextInputType.number,
                      onChanged: (value) => formData['amt'] = value,
                      decoration: _getInputDecoration(
                          hint: 'Enter amount', prefix: 'KES '),
                    ),
                    const SizedBox(height: 20),

                    // PIN Field
                    _buildFormLabel('PIN'),
                    TextFormField(
                      obscureText: !_isPinVisible,
                      keyboardType: TextInputType.number,
                      maxLength: 4,
                      onChanged: (value) => formData['cmp'] = value,
                      decoration: _getInputDecoration(
                        hint: '',
                        isPin: true,
                      ),
                      style: TextStyle(fontSize: 20, letterSpacing: 8),
                    ),
                    const SizedBox(height: 40),

                    // Action Buttons
                    Padding(
                      padding: const EdgeInsets.only(bottom: 20),
                      child: Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: isProcessing
                                  ? null
                                  : () => Navigator.pop(context),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: isProcessing
                                    ? Colors.grey[200]
                                    : Colors.grey[300],
                                foregroundColor: Colors.black87,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 15),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8)),
                                elevation: 0,
                              ),
                              child: Text('Cancel',
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold)),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: isProcessing ? null : _onContinue,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: isProcessing
                                    ? Colors.grey[400]
                                    : ColorPalette.primary,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 15),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8)),
                              ),
                              child: isProcessing
                                  ? Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          height: 16,
                                          width: 16,
                                          child: CircularProgressIndicator(
                                            color: Colors.white,
                                            strokeWidth: 2.0,
                                          ),
                                        ),
                                        SizedBox(width: 8),
                                        Text(
                                          'Processing...',
                                          style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    )
                                  : Text('Continue',
                                      style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold)),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper to build form labels
  Widget _buildFormLabel(String label) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        label,
        style: TextStyle(
          color: ColorPalette.primary,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  // Helper for input decoration
  InputDecoration _getInputDecoration(
      {required String hint, String? prefix, bool isPin = false}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return InputDecoration(
      hintText: hint,
      prefixIcon: prefix != null
          ? Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              child: Text(prefix,
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: ColorPalette.primary)),
            )
          : (isPin
              ? Icon(Icons.lock_outline, color: ColorPalette.primary)
              : null),
      suffixIcon: isPin
          ? IconButton(
              icon: Icon(
                _isPinVisible ? Icons.visibility : Icons.visibility_off,
                color: isDark ? Colors.grey[400] : Colors.grey,
              ),
              onPressed: () {
                setState(() {
                  _isPinVisible = !_isPinVisible;
                });
              },
            )
          : null,
      filled: true,
      fillColor: isDark ? Colors.grey[800] : Colors.white,
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(
            color: isDark ? Colors.grey[600]! : Colors.grey.shade300),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: ColorPalette.primary, width: 2),
      ),
    );
  }

  // Build custom deposit UI with your original design
  Widget _buildDepositUI() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    String? selectedAccount;
// Default to M-Pesa, remove Airtel
    Map<String, dynamic>? selectedAccountData;

    // Get account from form data if available
    if (formData.containsKey('accNo') && allAccounts.isNotEmpty) {
      selectedAccountData = allAccounts.firstWhere(
        (account) => account['accountNo'] == formData['accNo'],
        orElse: () => allAccounts.first,
      );
      selectedAccount = selectedAccountData['accountName'];
    } else if (allAccounts.isNotEmpty) {
      selectedAccountData = allAccounts.first;
      selectedAccount = selectedAccountData['accountName'];
    }

    // Ensure the default account number is set in formData if not already present.
    if (selectedAccountData != null && formData['accNo'] == null) {
      formData['accNo'] = selectedAccountData['accountNo'];
    }

    // Get dynamic color based on selected account
    Color accountColor = selectedAccountData != null
        ? _getAccountColor(selectedAccountData)
        : ColorPalette.secondary;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: accountColor, // Use dynamic account color
        title: Text(
          'Deposit',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
            fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
      ),
      body: Column(
        children: [
          // Account Info Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: accountColor, // Use dynamic account color
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'To Account',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                if (widget.lockAccountSelection && allAccounts.length == 1)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 16),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.account_balance, color: Colors.white),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            allAccounts[0]['accountName'] ?? 'Account',
                            style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          allAccounts[0]['accountNo'] ?? '',
                          style: const TextStyle(
                              color: Colors.white70, fontSize: 14),
                        ),
                      ],
                    ),
                  )
                else
                  DropdownButtonHideUnderline(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                        ),
                      ),
                      child: DropdownButton<String>(
                        value: selectedAccount,
                        isExpanded: true,
                        dropdownColor:
                            accountColor, // Use dynamic account color
                        hint: Text(
                          'Select account',
                          style: const TextStyle(color: Colors.white70),
                        ),
                        items: allAccounts
                            .map((account) => DropdownMenuItem<String>(
                                  value: account['accountName'],
                                  child: Text(
                                    account['accountName'],
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ))
                            .toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            setState(() {
                              var newSelectedAccountData =
                                  allAccounts.firstWhere(
                                (account) => account['accountName'] == newValue,
                              );
                              formData['accNo'] =
                                  newSelectedAccountData['accountNo'];
                              // This will trigger a rebuild with new colors
                            });
                          }
                        },
                        icon: Icon(Icons.arrow_drop_down, color: Colors.white),
                      ),
                    ),
                  ),
                const SizedBox(height: 16),
                Text(
                  'Available Balance',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  selectedAccount != null
                      ? '${allAccounts.firstWhere((account) => account['accountName'] == selectedAccount)['balance'] ?? '0'} KES'
                      : '0 KES',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Form Content
          Expanded(
            child: Container(
              color: isDark
                  ? Colors.grey[900]
                  : ColorPalette
                      .greyBackground, // Use same grey background throughout
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Transaction Details',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: ColorPalette
                            .primary, // Use green color like original design
                        fontFamily:
                            ClientThemeManager().currentClientConfig.fontFamily,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Amount Field
                    Text(
                      'Amount',
                      style: TextStyle(
                        color: ColorPalette
                            .primary, // Use green color like original design
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      keyboardType: TextInputType.number,
                      onChanged: (value) => formData['amt'] = value,
                      style: TextStyle(
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Enter amount',
                        hintStyle: TextStyle(
                          color: isDark ? Colors.grey[400] : Colors.grey[600],
                        ),
                        prefixIcon: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 16),
                          child: Text(
                            'KES ',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: ColorPalette
                                  .primary, // Use green color like original design
                              fontFamily: ClientThemeManager()
                                  .currentClientConfig
                                  .fontFamily,
                            ),
                          ),
                        ),
                        filled: true,
                        fillColor: isDark ? Colors.grey[800] : Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: isDark
                                  ? Colors.grey[600]!
                                  : Colors.grey.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: ColorPalette.primary,
                              width: 2), // Use green color like original design
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Payment Method Selection (M-Pesa only)
                    Text(
                      'Deposit From',
                      style: TextStyle(
                        color: ColorPalette
                            .primary, // Use green color like original design
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Container(
                      decoration: BoxDecoration(
                        color: isDark ? Colors.grey[800] : Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                            color: isDark
                                ? Colors.grey[600]!
                                : Colors.grey.shade300),
                      ),
                      child: ListTile(
                        leading: Image.asset(
                          'assets/icons/mpesa.png',
                          width: 32,
                          height: 32,
                        ),
                        title: Text(
                          'M-Pesa',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: isDark ? Colors.white : Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // PIN Field
                    Text(
                      'PIN',
                      style: TextStyle(
                        color: ColorPalette
                            .primary, // Use green color like original design
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      obscureText: !_isPinVisible,
                      keyboardType: TextInputType.number,
                      maxLength: 4,
                      onChanged: (value) => formData['cmp'] = value,
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: isDark ? Colors.grey[800] : Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: isDark
                                  ? Colors.grey[600]!
                                  : Colors.grey.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: ColorPalette.primary,
                              width: 2), // Use green color like original design
                        ),
                        prefixIcon: Icon(
                          Icons.lock_outline,
                          color: ColorPalette
                              .primary, // Use green color like original design
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _isPinVisible
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: isDark ? Colors.grey[400] : Colors.grey,
                          ),
                          onPressed: () {
                            setState(() {
                              _isPinVisible = !_isPinVisible;
                            });
                          },
                        ),
                      ),
                      style: TextStyle(
                        fontSize: 20,
                        letterSpacing: 8,
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 40),

                    // Action Buttons
                    Padding(
                      padding: const EdgeInsets.only(bottom: 20),
                      child: Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: isProcessing
                                  ? null
                                  : () => Navigator.pop(context),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: isProcessing
                                    ? Colors.grey[200]
                                    : Colors.grey[
                                        300], // Grey background like original
                                foregroundColor: Colors.black87,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 15),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                elevation: 0,
                              ),
                              child: Text(
                                'Cancel',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: isProcessing ? null : _onContinue,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: isProcessing
                                    ? Colors.grey[400]
                                    : ColorPalette
                                        .primary, // Green background like original
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 15),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: isProcessing
                                  ? Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          height: 16,
                                          width: 16,
                                          child: CircularProgressIndicator(
                                            color: Colors.white,
                                            strokeWidth: 2.0,
                                          ),
                                        ),
                                        SizedBox(width: 8),
                                        Text(
                                          'Processing...',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    )
                                  : Text(
                                      'Continue',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Get account color based on account type (same logic as dashboard)
  Color _getAccountColor(Map<String, dynamic> account) {
    String accountType = account['accountType']?.toString() ?? '';
    String accountName = account['accountName']?.toString().toLowerCase() ?? '';

    if (accountName.contains('savings') ||
        account['isSavingsAccount'] == 'Yes') {
      return ColorPalette.secondary; // Pink
    } else if (accountName.contains('share') ||
        account['isShareCapital'] == 'Yes') {
      return ColorPalette.primary; // Green
    } else if (accountName.contains('loan') ||
        account['isLoanAccount'] == 'Yes') {
      return Colors.teal;
    } else if (accountName.contains('nwd') ||
        accountName.contains('deposit') ||
        account['isNWD'] == 'Yes') {
      return Colors.purple; // Purple for deposit accounts
    }

    // Default colors based on account type
    switch (accountType) {
      case '1':
        return Colors.teal; // Loan
      case '2':
        return Colors.purple; // NWD/Deposit
      case '3':
        return ColorPalette.secondary; // Savings
      case '4':
        return ColorPalette.primary; // Shares
      default:
        return ColorPalette.primary; // Default to GREEN (same as dashboard)
    }
  }

  /// Show deposit completion dialog
  void _showDepositCompletionDialog(Map<String, dynamic> response) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // Get deposit details from form data
    String amount = formData['amt']?.toString() ?? '0';
// Get from selected account

    // Try to get account name and number from form data
    if (formData.containsKey('accNo') && allAccounts.isNotEmpty) {
      allAccounts.firstWhere(
        (account) => account['accountNo'] == formData['accNo'],
        orElse: () => allAccounts.first,
      );
    } else if (allAccounts.isNotEmpty) {}

    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: Dialog(
                insetPadding: const EdgeInsets.all(16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    color: isDark ? Colors.grey[900] : Colors.white,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with purple background like OTP screen
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: ColorPalette
                              .unselectedNavItemColor, // Purple like OTP screen
                          borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(16)),
                        ),
                        child: Center(
                          child: Text(
                            'Confirm Transaction',
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      // Content area with transaction details
                      Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Transaction Details header
                            Text(
                              'Transaction Details',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: isDark ? Colors.white : Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Amount
                            _buildDetailRow(
                              'AMOUNT',
                              amount,
                              isDark,
                            ),

                            // Account Type
                            _buildDetailRow(
                              'ACCOUNT TYPE',
                              'Savings Account',
                              isDark,
                            ),

                            // Total Amount
                            _buildDetailRow(
                              'TOTAL AMOUNT',
                              amount,
                              isDark,
                            ),

                            // Recipient Type
                            _buildDetailRow(
                              'RECIPIENT TYPE',
                              'To own',
                              isDark,
                            ),

                            const SizedBox(height: 24),

                            // Buttons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    style: TextButton.styleFrom(
                                      backgroundColor: Colors.red,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: Text(
                                      'Cancel',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: ElevatedButton(
                                    onPressed: () {
                                      Navigator.pop(
                                          context); // Close confirmation dialog
                                      // Now actually process the transaction
                                      setState(() {
                                        isProcessing = true;
                                      });
                                      _processTransaction();
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: ColorPalette.primary,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: Text(
                                      'Confirm',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ));
  }

  /// Show deposit initiated modal after confirmation
  void _showDepositInitiatedModal(Map<String, dynamic> response) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // Get deposit details from form data
    String amount = formData['amt']?.toString() ?? '0';
    String toAccountName = 'Account';

    // Try to get account name from form data
    if (formData.containsKey('accNo') && allAccounts.isNotEmpty) {
      var selectedAccount = allAccounts.firstWhere(
        (account) => account['accountNo'] == formData['accNo'],
        orElse: () => allAccounts.first,
      );
      toAccountName = selectedAccount['accountName'] ?? 'Account';
    } else if (allAccounts.isNotEmpty) {
      toAccountName = allAccounts.first['accountName'] ?? 'Account';
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        insetPadding: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          decoration: BoxDecoration(
            color: isDark ? Colors.grey[900] : Colors.grey[100],
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: ColorPalette.primary,
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(16)),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.schedule,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Deposit Initiated',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: ColorPalette.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: ColorPalette.primary.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Column(
                        children: [
                          Text(
                            'KES $amount',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: ColorPalette.primary,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Deposit via M-Pesa',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  isDark ? Colors.grey[400] : Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'To: $toAccountName',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  isDark ? Colors.grey[400] : Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Your deposit request has been sent to M-Pesa. Please check your phone for a payment prompt and complete the transaction.',
                      style: TextStyle(
                        fontSize: 14,
                        color: isDark ? Colors.grey[300] : Colors.grey[700],
                        height: 1.4,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isDark ? Colors.grey[800] : Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: ColorPalette.primary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'You will receive an SMS confirmation once the deposit is processed.',
                              style: TextStyle(
                                fontSize: 12,
                                color: isDark
                                    ? Colors.grey[400]
                                    : Colors.grey[600],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context); // Close dialog
                          Navigator.pop(context); // Return to previous screen
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorPalette.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'OK',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show OTP verification for utility bills
  void _showUtilityBillOtpVerification(Map<String, dynamic> response) async {
    // Get phone number for OTP
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String phoneNumber = prefs.getString('msisdn') ?? '';

    // Prepare transaction details for OTP screen
    Map<String, String> transactionDetails = {
      'account_type': _getSelectedAccountName(),
      'utility_account': formData[_getUtilityAccountKey()]?.toString() ?? '',
      'utility_type': _getUtilityBillDisplayName(),
      'amount': formData['amt']?.toString() ?? '0',
    };

    // Show OTP verification dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: OtpVerificationScreen(
          phoneNumber: phoneNumber,
          transactionType: '${_getUtilityBillDisplayName()} Payment',
          amount: formData['amt']?.toString() ?? '0',
          transactionDetails: transactionDetails,
        ),
      ),
    ).then((result) {
      if (result == true) {
        // OTP verified successfully, show receipt
        _showUtilityBillTransactionReceipt();
      }
      // If result is null or false, user cancelled or OTP failed
      // Just close the dialog and return to form
    });
  }

  /// Get selected account name for transaction details
  String _getSelectedAccountName() {
    if (formData.containsKey('accNo') && allAccounts.isNotEmpty) {
      var selectedAccount = allAccounts.firstWhere(
        (account) => account['accountNo'] == formData['accNo'],
        orElse: () => allAccounts.first,
      );
      return selectedAccount['accountName'] ?? 'Account';
    } else if (allAccounts.isNotEmpty) {
      return allAccounts.first['accountName'] ?? 'Account';
    }
    return 'Account';
  }

  /// Show utility bill transaction receipt after successful OTP verification
  void _showUtilityBillTransactionReceipt() {
    String transactionType = '${_getUtilityBillDisplayName()} Payment';
    String amount = formData['amt']?.toString() ?? '0';
    String accountName = _getSelectedAccountName();
    String utilityAccount = formData[_getUtilityAccountKey()]?.toString() ?? '';

    Map<String, dynamic> receiptData = {
      'Transaction ID': 'TXN${DateTime.now().millisecondsSinceEpoch}',
      'Transaction Type': transactionType,
      'Date & Time': DateTime.now().toString().substring(0, 19),
      'Amount': amount,
      'Description': _getUtilityBillTransactionDescription(
          transactionType, accountName, utilityAccount),
      'From Account': accountName,
      'Account Number': _getSelectedAccountNumber(),
      'Paying to': utilityAccount,
      'Utility': _getUtilityBillDisplayName(),
      'Status': 'Successful',
    };

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => TransactionReceiptScreen(
          receiptData: receiptData,
          serviceCode: widget.serviceCode,
        ),
      ),
    );
  }

  /// Get selected account number
  String _getSelectedAccountNumber() {
    if (formData.containsKey('accNo') && allAccounts.isNotEmpty) {
      var selectedAccount = allAccounts.firstWhere(
        (account) => account['accountNo'] == formData['accNo'],
        orElse: () => allAccounts.first,
      );
      return selectedAccount['accountNo'] ?? '';
    } else if (allAccounts.isNotEmpty) {
      return allAccounts.first['accountNo'] ?? '';
    }
    return 'ACC-${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';
  }

  /// Get utility bill transaction description
  String _getUtilityBillTransactionDescription(
      String type, String accountName, String utilityAccount) {
    return '$type from $accountName to account $utilityAccount';
  }

  /// Pick contact from device contacts
  Future<void> _pickContact() async {
    try {
      // Request permission first
      if (await FlutterContacts.requestPermission()) {
        // Open contact picker
        final contact = await FlutterContacts.openExternalPick();
        if (contact != null && contact.phones.isNotEmpty) {
          // Get the first phone number and clean it
          String phoneNumber =
              contact.phones.first.number.replaceAll(RegExp(r'[^0-9]'), '');

          // Update the controller and validate
          _phoneController.text = phoneNumber;
          final isValidPrefix = phoneNumber.startsWith('07') ||
              phoneNumber.startsWith('01') ||
              phoneNumber.startsWith('254');

          setState(() {
            _isValidPhone =
                RegExp(r'^(07|01)\d{8}$|^254\d{9}$').hasMatch(phoneNumber);
            _showPrefixError = !isValidPrefix && phoneNumber.isNotEmpty;
            formData['phoneNumber'] = phoneNumber;
          });
        }
      } else {
        // Permission denied
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Contact permission is required to select contacts',
              style: TextStyle(
                fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
              ),
            ),
            backgroundColor: ColorPalette.error,
          ),
        );
      }
    } catch (e) {
      AppLogger.error('picking contact: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Failed to access contacts. Please try again.',
            style: TextStyle(
              fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
            ),
          ),
          backgroundColor: ColorPalette.error,
        ),
      );
    }
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    String appBarTitle = isLoading
        ? "Loading..."
        : isProcessing
            ? "Processing..."
            : _getAppBarTitle();

    if (widget.serviceCode == 'STKLOANS' &&
        !isLoading &&
        errorMessage == null &&
        journeys.isNotEmpty) {
      return _buildLoanRepaymentUI();
    }

    // Use custom balance enquiry UI for balance enquiry transactions
    if (_isBalanceEnquiry() &&
        !isLoading &&
        errorMessage == null &&
        journeys.isNotEmpty) {
      return _buildBalanceEnquiryUI();
    }

    // Use custom deposit UI for deposit transactions
    if (_isDepositTransaction() &&
        !isLoading &&
        errorMessage == null &&
        journeys.isNotEmpty) {
      return _buildDepositUI();
    }

    // Use custom withdrawal UI for withdrawal transactions
    if (_isWithdrawalTransaction() &&
        !isLoading &&
        errorMessage == null &&
        journeys.isNotEmpty) {
      return _buildWithdrawalUI();
    }

    // Use custom Inter-Account Transfer UI
    if (_isInterAccountTransfer() && !isLoading && errorMessage == null) {
      return _buildInterAccountTransferUI();
    }

    // Use custom utility bill UI for utility bill transactions
    if (_isUtilityBillTransaction() &&
        !isLoading &&
        errorMessage == null &&
        journeys.isNotEmpty) {
      return _buildUtilityBillUI();
    }

    // Use custom B2B UI for B2B services (Transfer to Bank, Pay to Till, Purchase Mpesa Float)
    if (_isB2BService() &&
        !isLoading &&
        errorMessage == null &&
        journeys.isNotEmpty) {
      return _buildB2BServiceUI();
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorPalette.primary,
        centerTitle: true,
        title: Text(
          appBarTitle,
          style: theme.textTheme.titleLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
            fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: isProcessing ? null : () => Navigator.pop(context),
        ),
        toolbarHeight: 110.0,
      ),
      body: Container(
        color: ColorPalette.primary,
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(25.0),
            topRight: Radius.circular(25.0),
          ),
          child: Container(
            color: theme.colorScheme.background,
            child: SafeArea(
              child: Stack(
                children: [
                  if (isLoading)
                    Center(
                      child: CircularProgressIndicator(
                        valueColor:
                            AlwaysStoppedAnimation<Color>(ColorPalette.primary),
                      ),
                    )
                  else if (errorMessage != null)
                    Center(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error_outline,
                                color: ColorPalette.error, size: 50),
                            const SizedBox(height: 16),
                            Text(
                              "Service is Currently Unavailable",
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: ColorPalette.error,
                                fontFamily: ClientThemeManager()
                                    .currentClientConfig
                                    .fontFamily,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              errorMessage!,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: theme.colorScheme.onSurface
                                    .withValues(alpha: 0.7),
                                fontFamily: ClientThemeManager()
                                    .currentClientConfig
                                    .fontFamily,
                              ),
                            ),
                            const SizedBox(height: 24),
                            ElevatedButton(
                              onPressed: () async {
                                // If the error was a 403, try refreshing token first
                                if (errorMessage != null &&
                                    errorMessage!.contains('Session expired')) {
                                  AppLogger.info(
                                      'Attempting token refresh before retry due to session expiry');
                                  try {
                                    bool refreshSuccess =
                                        await TokenRefreshService.instance
                                            .forceRefreshToken();
                                    if (refreshSuccess) {
                                      AppLogger.info(
                                          'Token refreshed successfully before retry');
                                    } else {
                                      AppLogger.warning(
                                          'Token refresh failed before retry');
                                    }
                                  } catch (e) {
                                    AppLogger.error(
                                        'Error refreshing token before retry: $e');
                                  }
                                }
                                _fetchServiceJourney();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: ColorPalette.primary,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 30, vertical: 12),
                              ),
                              child: Text(
                                "Try Again",
                                style: TextStyle(
                                  fontFamily: ClientThemeManager()
                                      .currentClientConfig
                                      .fontFamily,
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    )
                  else if (isProcessing)
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                                ColorPalette.primary),
                          ),
                          const SizedBox(height: 20),
                          Text(
                            'Processing Transaction...',
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: ClientThemeManager()
                                  .currentClientConfig
                                  .fontFamily,
                            ),
                          ),
                        ],
                      ),
                    )
                  else
                    Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Expanded(
                            child: SingleChildScrollView(
                              child: _buildFormContent(),
                            ),
                          ),
                          const SizedBox(height: 20),
                          ElevatedButton(
                            onPressed: isProcessing ? null : _onContinue,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: isProcessing
                                  ? Colors.grey[400]
                                  : ColorPalette.secondary,
                              padding: const EdgeInsets.symmetric(vertical: 15),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                            child: isProcessing
                                ? Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        height: 16,
                                        width: 16,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2.0,
                                        ),
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Processing...',
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontFamily: ClientThemeManager()
                                              .currentClientConfig
                                              .fontFamily,
                                        ),
                                      ),
                                    ],
                                  )
                                : Text(
                                    'Continue',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontFamily: ClientThemeManager()
                                          .currentClientConfig
                                          .fontFamily,
                                    ),
                                  ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFormContent() {
    if (journeys.isEmpty) {
      return Center(
        child: Text(
          'No form journey data available',
          style: TextStyle(
            fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _getAllFormFields(),
    );
  }

  List<Widget> _getAllFormFields() {
    List<Widget> allFields = [];

    for (var journey in journeys) {
      if (journey.type != 'stepper') {
        allFields.add(
          DynamicForm(
            fields: journey.fieldGroup,
            onChanged: _onFieldChanged,
            formData: formData,
          ),
        );
        continue;
      }

      for (var step in journey.fieldGroup) {
        if (step.fieldGroup != null && step.fieldGroup!.isNotEmpty) {
          if (step.templateOptions.containsKey('label') &&
              step.templateOptions['label'] != null &&
              step.templateOptions['label'].toString().isNotEmpty) {
            allFields.add(
              Padding(
                padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
                child: Text(
                  step.templateOptions['label'],
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: ColorPalette.primary,
                    fontFamily:
                        ClientThemeManager().currentClientConfig.fontFamily,
                  ),
                ),
              ),
            );
          }

          allFields.add(
            DynamicForm(
              fields: step.fieldGroup!,
              onChanged: _onFieldChanged,
              formData: formData,
            ),
          );
        }
      }
    }

    return allFields;
  }

  /// Build detail row for transaction confirmation
  Widget _buildDetailRow(String label, String value, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: isDark ? Colors.white : Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build detail row with divider for transaction confirmation (matching Figma design)
  Widget _buildDetailRowWithDivider(String label, String value, bool isDark,
      {bool isLast = false}) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 3,
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black,
                  ),
                  textAlign: TextAlign.right,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ),
            ],
          ),
        ),
        if (!isLast)
          Container(
            height: 1,
            color: isDark ? Colors.grey[800] : Colors.grey[200],
            margin: const EdgeInsets.symmetric(horizontal: 16),
          ),
      ],
    );
  }

  /// Show inter-account transfer confirmation dialog
  void _showInterAccountTransferConfirmationDialog(
      Map<String, dynamic> response) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // Get transfer details from form data
    String amount = formData['amt']?.toString() ?? '0';
    String fromAccountName = 'Account';
    String accountNumber = '';
    String recipientType =
        (widget.serviceCode == 'IATOW' || widget.serviceCode == 'IAT')
            ? 'To own'
            : 'To other member';

    // Get account details
    if (allAccounts.isNotEmpty) {
      Map<String, dynamic>? fromAccount = allAccounts.firstWhere(
        (acc) => acc['accountNo'] == formData['accNo'],
        orElse: () => allAccounts.first,
      );
      fromAccountName = fromAccount['accountName'] ?? 'Account';
      accountNumber = fromAccount['accountNo'] ?? '';
    }

    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: Dialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: ColorPalette
                            .unselectedNavItemColor, // Purple like OTP screen
                        borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(16)),
                      ),
                      child: Center(
                        child: Text(
                          'Confirm Transaction',
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    // Content area with transaction details
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Transaction Details Card
                          Container(
                            decoration: BoxDecoration(
                              color: isDark ? Colors.grey[800] : Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.05),
                                  blurRadius: 10,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Transaction Details header
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 16, right: 16, top: 16, bottom: 8),
                                  child: Text(
                                    'Transaction Details',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ),

                                // Amount
                                _buildDetailRowWithDivider(
                                  'AMOUNT',
                                  'KES $amount',
                                  isDark,
                                ),

                                // Account Type
                                _buildDetailRowWithDivider(
                                  'ACCOUNT TYPE',
                                  '$fromAccountName - $accountNumber',
                                  isDark,
                                ),

                                // Total Amount
                                _buildDetailRowWithDivider(
                                  'TOTAL AMOUNT',
                                  'KES $amount',
                                  isDark,
                                ),

                                // Recipient Type
                                _buildDetailRowWithDivider(
                                  'RECIPIENT TYPE',
                                  recipientType,
                                  isDark,
                                  isLast: true,
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Action buttons
                          Row(
                            children: [
                              Expanded(
                                child: TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  style: TextButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child: Text(
                                    'Cancel',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: () {
                                    Navigator.pop(
                                        context); // Close confirmation dialog
                                    // Now actually process the transaction
                                    setState(() {
                                      isProcessing = true;
                                    });
                                    _processTransaction();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: ColorPalette.success,
                                    foregroundColor: ColorPalette.white,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child: Text(
                                    'Confirm',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ));
  }

  /// Show withdrawal confirmation dialog
  void _showWithdrawalConfirmationDialog(Map<String, dynamic> response) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // Get withdrawal details from form data
    String amount = formData['amt']?.toString() ?? '0';
    String recipientType = formData['recipientType'] ?? 'To own';

    // Try to get account name and number from form data
    if (formData.containsKey('accNo') && allAccounts.isNotEmpty) {
      allAccounts.firstWhere(
        (account) => account['accountNo'] == formData['accNo'],
        orElse: () => allAccounts.first,
      );
    } else if (allAccounts.isNotEmpty) {}

    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: Dialog(
                insetPadding: const EdgeInsets.all(16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with purple background like OTP screen
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: ColorPalette
                              .unselectedNavItemColor, // Purple like OTP screen
                          borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(16)),
                        ),
                        child: Center(
                          child: Text(
                            'Confirm Withdraw',
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      // Content area with transaction details
                      Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Transaction Details Card
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.05),
                                    blurRadius: 10,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Transaction Details header
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 16,
                                        right: 16,
                                        top: 16,
                                        bottom: 8),
                                    child: Text(
                                      'Transaction Details',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                  ),

                                  // Amount
                                  _buildDetailRowWithDivider(
                                    'AMOUNT',
                                    amount,
                                    isDark,
                                  ),

                                  // Account Type
                                  _buildDetailRowWithDivider(
                                    'ACCOUNT TYPE',
                                    'Savings Account',
                                    isDark,
                                  ),

                                  // Total Amount
                                  _buildDetailRowWithDivider(
                                    'TOTAL AMOUNT',
                                    amount,
                                    isDark,
                                  ),

                                  // Recipient Type
                                  _buildDetailRowWithDivider(
                                    'RECIPIENT TYPE',
                                    recipientType,
                                    isDark,
                                    isLast: true,
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 24),

                            // Buttons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    style: TextButton.styleFrom(
                                      backgroundColor: Colors.red,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: Text(
                                      'Cancel',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: ElevatedButton(
                                    onPressed: () {
                                      Navigator.pop(
                                          context); // Close confirmation dialog
                                      // Now actually process the transaction
                                      setState(() {
                                        isProcessing = true;
                                      });
                                      _processTransaction();
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: ColorPalette.success,
                                      foregroundColor: ColorPalette.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: Text(
                                      'Confirm',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ));
  }

  /// Show error message via SnackBar
  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // Build custom utility bill UI with your original design
  Widget _buildUtilityBillUI() {
    Theme.of(context);
    String? selectedAccount;
    Map<String, dynamic>? selectedAccountData;

    // Get account from form data if available
    if (formData.containsKey('accNo') && allAccounts.isNotEmpty) {
      selectedAccountData = allAccounts.firstWhere(
        (account) => account['accountNo'] == formData['accNo'],
        orElse: () => allAccounts.first,
      );
      selectedAccount = selectedAccountData['accountName'];
    } else if (allAccounts.isNotEmpty) {
      selectedAccountData = allAccounts.first;
      selectedAccount = selectedAccountData['accountName'];
    }

    // Ensure the default account number is set in formData if not already present.
    if (selectedAccountData != null && formData['accNo'] == null) {
      formData['accNo'] = selectedAccountData['accountNo'];
    }

    // Get utility bill specific color
    Color utilityColor = _getUtilityBillColor();
    String utilityName = _getUtilityBillDisplayName();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: utilityColor,
        title: Text(
          utilityName,
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
            fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
      ),
      body: Column(
        children: [
          // Account Info Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: utilityColor,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Select Source Account',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonHideUnderline(
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                      ),
                    ),
                    child: DropdownButton<String>(
                      value: selectedAccount,
                      isExpanded: true,
                      dropdownColor: utilityColor,
                      hint: Text(
                        'Select account',
                        style: const TextStyle(color: Colors.white70),
                      ),
                      items: allAccounts
                          .map((account) => DropdownMenuItem<String>(
                                value: account['accountName'],
                                child: Text(
                                  account['accountName'],
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ))
                          .toList(),
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          setState(() {
                            var newSelectedAccountData = allAccounts.firstWhere(
                              (account) => account['accountName'] == newValue,
                            );
                            formData['accNo'] =
                                newSelectedAccountData['accountNo'];
                            // This will trigger a rebuild with new colors
                          });
                        }
                      },
                      icon: Icon(Icons.arrow_drop_down, color: Colors.white),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Available Balance',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  selectedAccount != null
                      ? '${allAccounts.firstWhere((account) => account['accountName'] == selectedAccount)['balance'] ?? '0'} KES'
                      : '0 KES',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Form Content
          Expanded(
            child: Container(
              color: ColorPalette.greyBackground,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Transaction Details',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: ColorPalette.primary,
                        fontFamily:
                            ClientThemeManager().currentClientConfig.fontFamily,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Utility Account Number Field
                    _buildFormLabel(_getUtilityAccountLabel()),
                    TextFormField(
                      keyboardType: TextInputType.text,
                      onChanged: (value) =>
                          formData[_getUtilityAccountKey()] = value,
                      decoration: _getInputDecoration(
                          hint: _getUtilityAccountPlaceholder()),
                    ),
                    const SizedBox(height: 20),

                    // Amount Field
                    _buildFormLabel('Amount'),
                    TextFormField(
                      keyboardType: TextInputType.number,
                      onChanged: (value) => formData['amt'] = value,
                      decoration: _getInputDecoration(
                          hint: 'Enter amount', prefix: 'KES '),
                    ),
                    const SizedBox(height: 20),

                    // PIN Field
                    _buildFormLabel('PIN'),
                    TextFormField(
                      obscureText: !_isPinVisible,
                      keyboardType: TextInputType.number,
                      maxLength: 4,
                      onChanged: (value) => formData['cmp'] = value,
                      decoration: _getInputDecoration(
                        hint: '',
                        isPin: true,
                      ),
                      style: TextStyle(fontSize: 20, letterSpacing: 8),
                    ),
                    const SizedBox(height: 30),
                  ],
                ),
              ),
            ),
          ),

          // Action Buttons
          Padding(
            padding: const EdgeInsets.only(bottom: 20),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed:
                        isProcessing ? null : () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          isProcessing ? Colors.grey[200] : Colors.grey[300],
                      foregroundColor: Colors.black87,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                      elevation: 0,
                    ),
                    child: Text('Cancel',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.bold)),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: isProcessing ? null : _onContinue,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isProcessing
                          ? Colors.grey[400]
                          : ColorPalette.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                    ),
                    child: isProcessing
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                height: 16,
                                width: 16,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2.0,
                                ),
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Processing...',
                                style: TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                            ],
                          )
                        : Text('Continue',
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold)),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Get utility-specific account field label
  String _getUtilityAccountLabel() {
    switch (widget.serviceCode) {
      case 'DSTV':
        return 'Enter Utility Account Number';
      case 'ZUKU':
        return 'Enter Account Number';
      case 'STAR':
        return 'Enter Smartcard Number';
      case 'NWTR':
        return 'Enter Account Number';
      default:
        return 'Enter Account Number';
    }
  }

  // Get utility-specific account field key
  String _getUtilityAccountKey() {
    switch (widget.serviceCode) {
      case 'DSTV':
        return 'utilityAccountNumber';
      case 'ZUKU':
        return 'accountNumber';
      case 'STAR':
        return 'smartcardNumber';
      case 'NWTR':
        return 'accountNumber';
      default:
        return 'accountNumber';
    }
  }

  // Get utility-specific account field placeholder
  String _getUtilityAccountPlaceholder() {
    switch (widget.serviceCode) {
      case 'DSTV':
        return 'Enter enter utility account number';
      case 'ZUKU':
        return 'Enter account number';
      case 'STAR':
        return 'Enter smartcard number';
      case 'NWTR':
        return 'Enter account number';
      default:
        return 'Enter account number';
    }
  }

  /// Show utility bill confirmation dialog
  void _showUtilityBillConfirmationDialog(Map<String, dynamic> response) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // Get utility bill details from form data
    String amount = formData['amt']?.toString() ?? '0';
    String fromAccountName = 'Account';
    String accountNumber = '';
    String utilityAccountNumber =
        formData[_getUtilityAccountKey()]?.toString() ?? '';
    String utilityName = _getUtilityBillDisplayName();

    // Try to get account name and number from form data
    if (formData.containsKey('accNo') && allAccounts.isNotEmpty) {
      var selectedAccount = allAccounts.firstWhere(
        (account) => account['accountNo'] == formData['accNo'],
        orElse: () => allAccounts.first,
      );
      fromAccountName = selectedAccount['accountName'] ?? 'Account';
      accountNumber = selectedAccount['accountNo'] ?? '';
    } else if (allAccounts.isNotEmpty) {
      fromAccountName = allAccounts.first['accountName'] ?? 'Account';
      accountNumber = allAccounts.first['accountNo'] ?? '';
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Dialog(
          insetPadding: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[900] : Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with purple background like OTP screen
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: ColorPalette
                        .unselectedNavItemColor, // Purple like OTP screen
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(16)),
                  ),
                  child: Center(
                    child: Text(
                      'Confirm $utilityName Payment',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                // Content area with transaction details
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Transaction Details header
                      Text(
                        'Transaction Details',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Transaction details with masterclass styling
                      Container(
                        decoration: BoxDecoration(
                          color: isDark ? Colors.grey[800] : Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 10,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            // Source Account
                            _buildDetailRowWithDivider(
                              'SOURCE ACCOUNT',
                              '$fromAccountName - $accountNumber',
                              isDark,
                            ),

                            // Utility Account Number
                            _buildDetailRowWithDivider(
                              _getUtilityAccountLabel().toUpperCase(),
                              utilityAccountNumber,
                              isDark,
                            ),

                            // Amount
                            _buildDetailRowWithDivider(
                              'AMOUNT',
                              'KES $amount',
                              isDark,
                              isLast: true,
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: TextButton(
                              onPressed: () => Navigator.pop(context),
                              style: TextButton.styleFrom(
                                backgroundColor: Colors.red,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                'Cancel',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(
                                    context); // Close confirmation dialog
                                // Now actually process the transaction
                                setState(() {
                                  isProcessing = true;
                                });
                                _processTransaction();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: ColorPalette.success,
                                foregroundColor: ColorPalette.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                'Confirm',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build beautiful balance enquiry UI that matches BalanceEnquiryScreen design
  Widget _buildBalanceEnquiryUI() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    String enquiryTitle = _getEnquiryDisplayName();

    // Get the appropriate account based on service code
    Map<String, dynamic>? selectedAccount = _getAccountForServiceCode();
    String selectedAccountNo = selectedAccount?['accountNo'] ?? '';

    // Pre-populate form data with selected account
    if (selectedAccount != null && formData['accNo'] == null) {
      formData['accNo'] = selectedAccountNo;
    }

    return Scaffold(
      backgroundColor:
          isDark ? Theme.of(context).scaffoldBackgroundColor : Colors.grey[200],
      appBar: AppBar(
        title: Text(
          enquiryTitle,
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          // Header section with curved bottom
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: ColorPalette.secondary,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(30),
                bottomRight: Radius.circular(30),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Check Your Balance',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  'Enter your PIN to view your current balance.',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),

          // Form content
          Expanded(
            child: Container(
              color: isDark
                  ? Theme.of(context).scaffoldBackgroundColor
                  : Colors.grey[200],
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Selected Account Display (non-editable)
                    Text(
                      'Selected Account',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isDark ? Colors.white : ColorPalette.primary,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: isDark ? Colors.grey[850] : Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: ColorPalette.primary.withValues(alpha: 0.3),
                          width: 2,
                        ),
                        boxShadow: [
                          if (!isDark)
                            BoxShadow(
                              color: Colors.grey.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                        ],
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color:
                                  ColorPalette.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.account_balance_wallet,
                              color: ColorPalette.primary,
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  selectedAccount?['accountName'] ?? 'Account',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: isDark ? Colors.white : Colors.black,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  selectedAccountNo,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: isDark
                                        ? Colors.grey[400]
                                        : Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Icon(
                            Icons.check_circle,
                            color: ColorPalette.primary,
                            size: 24,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // PIN field
                    Text(
                      'Enter PIN',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isDark ? Colors.white : ColorPalette.primary,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Container(
                      decoration: BoxDecoration(
                        color: isDark ? Colors.grey[850] : Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                        ),
                        boxShadow: [
                          if (!isDark)
                            BoxShadow(
                              color: Colors.grey.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                        ],
                      ),
                      child: TextFormField(
                        obscureText: true,
                        keyboardType: TextInputType.number,
                        maxLength: 4,
                        onChanged: (value) => formData['cmp'] = value,
                        style: TextStyle(
                          color: isDark ? Colors.white : Colors.black,
                          fontSize: 16,
                        ),
                        decoration: InputDecoration(
                          prefixIcon: Icon(
                            Icons.lock_outline,
                            color: isDark ? Colors.grey[400] : Colors.grey[600],
                          ),
                          suffixIcon: Icon(
                            Icons.visibility_off,
                            color: isDark ? Colors.grey[400] : Colors.grey[600],
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(16),
                          counterText: '',
                        ),
                      ),
                    ),

                    const SizedBox(height: 30),

                    // Check Balance button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: isProcessing ? null : _onContinue,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorPalette.secondary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                          elevation: 2,
                        ),
                        child: isProcessing
                            ? CircularProgressIndicator(color: Colors.white)
                            : Text(
                                'Check Balance',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Security notice
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: isDark
                            ? Colors.blue.withValues(alpha: 0.1)
                            : Colors.blue.withValues(alpha: 0.05),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.blue.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.security,
                            color: Colors.blue,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Security Notice',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Your PIN is encrypted and secure. Never share your PIN with anyone.',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: isDark
                                        ? Colors.grey[400]
                                        : Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(
                        height: MediaQuery.of(context).padding.bottom + 20),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Get the appropriate account based on the service code
  Map<String, dynamic>? _getAccountForServiceCode() {
    if (allAccounts.isEmpty) return null;

    // Map service codes to account types
    switch (widget.serviceCode) {
      case 'SBE': // Savings Balance Enquiry
        return allAccounts.firstWhere(
          (account) =>
              account['isSavingsAccount'] == 'Yes' ||
              account['accountName']
                  .toString()
                  .toLowerCase()
                  .contains('savings'),
          orElse: () => allAccounts.first,
        );
      case 'SCBE': // Share Capital Balance Enquiry
        return allAccounts.firstWhere(
          (account) =>
              account['isShareCapital'] == 'Yes' ||
              account['accountName'].toString().toLowerCase().contains('share'),
          orElse: () => allAccounts.first,
        );
      case 'NWDB': // NWD/Deposit Balance Enquiry
        return allAccounts.firstWhere(
          (account) =>
              account['isNWD'] == 'Yes' ||
              account['accountName']
                  .toString()
                  .toLowerCase()
                  .contains('deposit') ||
              account['accountName'].toString().toLowerCase().contains('nwd'),
          orElse: () => allAccounts.first,
        );
      case 'LB': // Loan Balance Enquiry
        return allAccounts.firstWhere(
          (account) =>
              account['isLoanAccount'] == 'Yes' ||
              account['accountName'].toString().toLowerCase().contains('loan'),
          orElse: () => allAccounts.first,
        );
      default:
        return allAccounts.first;
    }
  }

  /// Build B2B Service UI matching Figma design
  Widget _buildB2BServiceUI() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Get service title and available balance
    String serviceTitle = _getB2BServiceTitle();

    List<Map<String, dynamic>> displayAccounts = allAccounts;
    // For TTB, only show savings accounts
    if (widget.serviceCode == 'TTB') {
      displayAccounts =
          allAccounts.where((acc) => acc['isSavingsAccount'] == 'Yes').toList();
    }

    // Ensure the from account is set in the form data, and is valid for the current context.
    if (displayAccounts.isNotEmpty) {
      final validAccountNos =
          displayAccounts.map((a) => a['accountNo']?.toString()).toSet();
      if (formData['accNo'] == null ||
          !validAccountNos.contains(formData['accNo']?.toString())) {
        // formData['accNo'] is either null or invalid, so reset to the first valid account.
        formData['accNo'] = displayAccounts.first['accountNo'];
      }
    }

    // Ensure the from account is set in the form data if not already present
    if (allAccounts.isNotEmpty && formData['accNo'] == null) {
      formData['accNo'] = allAccounts.first['accountNo'];
    }

    String availableBalance = _getAvailableBalance();
    _getSelectedAccountName();

    return Scaffold(
      body: Column(
        children: [
          // Header section with account info (using client-aware colors)
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  ColorPalette.primary,
                  ColorPalette.primary.withValues(alpha: 0.8)
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(25.0),
                bottomRight: Radius.circular(25.0),
              ),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  // App bar
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      children: [
                        IconButton(
                          icon:
                              const Icon(Icons.arrow_back, color: Colors.white),
                          onPressed: isProcessing
                              ? null
                              : () => Navigator.pop(context),
                        ),
                        Expanded(
                          child: Text(
                            serviceTitle,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(width: 48), // Balance the back button
                      ],
                    ),
                  ),

                  // Account info section
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 10, 20, 10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'From Account',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Account dropdown
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                                color: Colors.white.withValues(alpha: 0.3)),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<String>(
                              value: formData['accNo']?.toString(),
                              isExpanded: true,
                              dropdownColor: ColorPalette.primary,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                              icon: Icon(Icons.keyboard_arrow_down,
                                  color: Colors.white.withValues(alpha: 0.7)),
                              items: displayAccounts
                                  .map<DropdownMenuItem<String>>((account) {
                                return DropdownMenuItem<String>(
                                  value: account['accountNo']?.toString(),
                                  child: Text(account['accountName'] ??
                                      'Unnamed Account'),
                                );
                              }).toList(),
                              onChanged: (String? newValue) {
                                if (newValue != null) {
                                  setState(() {
                                    formData['accNo'] = newValue;
                                  });
                                }
                              },
                            ),
                          ),
                        ),

                        const SizedBox(height: 12),

                        // Available balance
                        const Text(
                          'Available Balance',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          availableBalance,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // White form section
          Expanded(
            child: Container(
              color: isDark ? theme.colorScheme.background : Colors.grey[50],
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(20, 12, 20, 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Dynamic form fields based on service journey (excluding account fields)
                    ..._buildB2BFormFields(),

                    const SizedBox(height: 8),

                    // Continue button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: isProcessing ? null : _onContinue,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isProcessing
                              ? Colors.grey[400]
                              : ColorPalette.secondary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                          elevation: 2,
                        ),
                        child: isProcessing
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2.0,
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Processing...',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              )
                            : const Text(
                                'Continue',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),

                    // Minimal bottom padding
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Get B2B service title
  String _getB2BServiceTitle() {
    switch (widget.serviceCode) {
      case 'TTB':
        return 'Transfer to Bank';
      case 'PTT':
        return 'Pay to Till';
      case 'MFL':
        return 'Purchase Mpesa Float';
      default:
        return 'B2B Service';
    }
  }

  /// Get available balance for display
  String _getAvailableBalance() {
    if (allAccounts.isNotEmpty) {
      var selectedAccount = allAccounts.firstWhere(
        (acc) => acc['accountNo'] == formData['accNo'],
        orElse: () => allAccounts.first,
      );

      if (selectedAccount.containsKey('balance')) {
        double balance = _parseAmount(selectedAccount['balance']);
        return '${NumberFormat("#,##0").format(balance)} KES';
      }
      if (selectedAccount.containsKey('availableBalance')) {
        double balance = _parseAmount(selectedAccount['availableBalance']);
        return '${NumberFormat("#,##0").format(balance)} KES';
      }
    }
    return '250,000 KES'; // Fallback
  }

  /// Build form fields for B2B services
  List<Widget> _buildB2BFormFields() {
    List<Widget> formFields = [];

    // Process all journeys and their fields
    for (var journey in journeys) {
      formFields.addAll(_buildFieldsFromGroup(journey.fieldGroup));
    }

    return formFields;
  }

  /// Build widgets from field group
  List<Widget> _buildFieldsFromGroup(List<FormFieldModel> fields) {
    List<Widget> widgets = [];

    for (int i = 0; i < fields.length; i++) {
      var field = fields[i];
      if (field.fieldGroup != null && field.fieldGroup!.isNotEmpty) {
        // Recursive for nested field groups
        widgets.addAll(_buildFieldsFromGroup(field.fieldGroup!));
      } else if (field.fieldArray != null && field.fieldArray!.isNotEmpty) {
        // Handle field arrays
        widgets.addAll(_buildFieldsFromGroup(field.fieldArray!));
      } else {
        // Build individual field
        Widget? fieldWidget = _buildB2BFormField(field);
        if (fieldWidget != null) {
          widgets.add(fieldWidget);
          // Add minimal spacing between fields
          if (i < fields.length - 1) {
            widgets.add(const SizedBox(height: 8));
          }
        }
      }
    }

    return widgets;
  }

  /// Build individual form field for B2B services
  Widget? _buildB2BFormField(FormFieldModel field) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Skip hidden fields
    if (field.hide == true) return null;

    // Skip account selection, it's handled in the header.
    if (field.key == 'accNo' || field.key == 'ownAccNo') return null;

    // Get field label
    String label = field.templateOptions['label'] ?? field.key;
    bool isRequired = field.templateOptions['required'] == true;

    // Add red asterisk for required fields
    if (isRequired && !label.contains('*')) {
      label = '$label *';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Field label
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ColorPalette.secondary,
          ),
        ),
        const SizedBox(height: 6),

        // Field input
        _buildFieldInput(field, isDark),
      ],
    );
  }

  /// Build field input widget
  Widget _buildFieldInput(FormFieldModel field, bool isDark) {
    // Handle different field types
    switch (field.type) {
      case 'select':
        return _buildSelectField(field, isDark);
      case 'input':
        return _buildInputField(field, isDark);
      default:
        return _buildInputField(field, isDark);
    }
  }

  /// Build select/dropdown field
  Widget _buildSelectField(FormFieldModel field, bool isDark) {
    List<dynamic> options = field.templateOptions['options'] ?? [];

    // De-duplicate options to prevent assertion error from duplicate values
    final uniqueOptions = <dynamic>[];
    final seenValues = <String>{};
    for (final option in options) {
      final value =
          (option is Map ? (option['value'] ?? option['name'] ?? '') : option)
              .toString();
      if (seenValues.add(value)) {
        uniqueOptions.add(option);
      }
    }

    return Container(
      decoration: BoxDecoration(
        color: isDark ? Theme.of(context).colorScheme.surface : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark
              ? Theme.of(context).colorScheme.outline
              : Colors.grey[300]!,
        ),
        boxShadow: [
          if (!isDark)
            BoxShadow(
              color: Colors.grey.withValues(alpha: .1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
        ],
      ),
      child: DropdownButtonFormField<String>(
          value: formData[field.key]?.toString(),
          isExpanded: true, // Fix overflow issue
          decoration: InputDecoration(
            border: InputBorder.none,
            contentPadding: const EdgeInsets.all(16),
            hintText:
                field.templateOptions['placeholder'] ?? 'Select an option',
            hintStyle: TextStyle(
              color: isDark ? Colors.grey[500] : Colors.grey[400],
            ),
          ),
          dropdownColor: isDark ? Colors.grey[800] : Colors.white,
          style: TextStyle(
            color: isDark ? Colors.white : Colors.black,
            fontSize: 16,
          ),
          items: uniqueOptions.map<DropdownMenuItem<String>>((option) {
            String value = (option is Map
                    ? (option['value'] ?? option['name'] ?? '')
                    : option)
                .toString();
            String label = (option is Map
                    ? (option['name'] ?? option['label'] ?? value)
                    : option)
                .toString();

            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                label,
                overflow: TextOverflow.ellipsis, // Handle long text
              ),
            );
          }).toList(),
          onChanged: isProcessing
              ? null
              : (String? value) {
                  if (value != null) {
                    _onFieldChanged(field.key, value);
                  }
                }),
    );
  }

  /// Build input field
  Widget _buildInputField(FormFieldModel field, bool isDark) {
    // Determine input type
    TextInputType keyboardType = TextInputType.text;
    bool obscureText = false;
    Widget? prefixIcon;

    if (field.templateOptions.containsKey('type')) {
      String inputType = field.templateOptions['type'];
      switch (inputType) {
        case 'number':
          keyboardType = TextInputType.number;
          prefixIcon = Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            child: Text(
              'KES',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: ColorPalette.primary,
              ),
            ),
          );
          break;
        case 'tel':
        case 'phone':
          keyboardType = TextInputType.phone;
          prefixIcon = Icon(Icons.phone,
              color: isDark ? Colors.grey[400] : Colors.grey[600]);
          break;
        case 'email':
          keyboardType = TextInputType.emailAddress;
          prefixIcon = Icon(Icons.email,
              color: isDark ? Colors.grey[400] : Colors.grey[600]);
          break;
        case 'password':
          obscureText = true;
          prefixIcon = Icon(Icons.lock_outline,
              color: isDark ? Colors.grey[400] : Colors.grey[600]);
          break;
      }
    }

    // Special handling for specific fields
    if (field.templateOptions['label']
            ?.toString()
            .toLowerCase()
            .contains('destination account') ==
        true) {
      keyboardType = TextInputType.number;
    } else if (field.key.toLowerCase().contains('account')) {
      prefixIcon = Icon(Icons.account_balance,
          color: isDark ? Colors.grey[400] : Colors.grey[600]);
    } else if (field.key.toLowerCase().contains('amount')) {
      keyboardType = TextInputType.number;
      prefixIcon = Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        child: Text(
          'KES',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: ColorPalette.primary,
          ),
        ),
      );
    } else if (field.key.toLowerCase().contains('pin') || field.key == 'cmp') {
      obscureText = true;
      prefixIcon = Icon(Icons.lock_outline,
          color: isDark ? Colors.grey[400] : Colors.grey[600]);
      keyboardType = TextInputType.number;
    } else if (field.key.toLowerCase().contains('phone')) {
      keyboardType = TextInputType.phone;
      prefixIcon = Icon(Icons.phone,
          color: isDark ? Colors.grey[400] : Colors.grey[600]);
    }

    List<TextInputFormatter> formatters = [];
    if (keyboardType == TextInputType.number) {
      formatters.add(FilteringTextInputFormatter.digitsOnly);
    }

    return Container(
      decoration: BoxDecoration(
        color: isDark ? Theme.of(context).colorScheme.surface : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark
              ? Theme.of(context).colorScheme.outline
              : Colors.grey[300]!,
        ),
        boxShadow: [
          if (!isDark)
            BoxShadow(
              color: Colors.grey.withValues(alpha: .1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
        ],
      ),
      child: AbsorbPointer(
        absorbing: isProcessing,
        child: TextFormField(
          initialValue: formData[field.key]?.toString(),
          keyboardType: keyboardType,
          obscureText: obscureText,
          enabled: !isProcessing,
          maxLength:
              field.key.toLowerCase().contains('pin') || field.key == 'cmp'
                  ? 4
                  : null,
          inputFormatters: formatters,
          onChanged: isProcessing
              ? null
              : (value) => _onFieldChanged(field.key, value),
          style: TextStyle(
            color: isDark ? Colors.white : Colors.black,
            fontSize: 16,
          ),
          decoration: InputDecoration(
            prefixIcon: prefixIcon,
            suffixIcon: obscureText
                ? Icon(
                    Icons.visibility_off,
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                  )
                : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.all(16),
            hintText: field.templateOptions['placeholder'] ??
                'Enter ${field.templateOptions['label'] ?? field.key}',
            hintStyle: TextStyle(
              color: isDark ? Colors.grey[500] : Colors.grey[400],
            ),
            counterText: '',
          ),
        ),
      ),
    );
  }

  Widget _buildLoanRepaymentUI() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Find the source account (first non-loan account) and loan accounts
    Map<String, dynamic>? fromAccount = allAccounts
        .firstWhere((acc) => acc['isLoanAccount'] != 'Yes', orElse: () => {});
    List<Map<String, dynamic>> toAccounts =
        allAccounts.where((acc) => acc['isLoanAccount'] == 'Yes').toList();

    // Set the from account in form data automatically
    if (fromAccount.isNotEmpty) {
      formData['accNo'] = fromAccount['accountNo'];
    }

    String? selectedToAccount = formData['otherAccNo'];

    return Scaffold(
      backgroundColor:
          isDark ? theme.scaffoldBackgroundColor : Colors.grey[100],
      appBar: AppBar(
        title: Text('Repay Loan',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          // Header section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(20, 10, 20, 20),
            decoration: BoxDecoration(
              color: ColorPalette.secondary,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(25),
                bottomRight: Radius.circular(25),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Make Loan Payment',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Pay your loan installments quickly and securely.',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),

          // Form section
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // From Account Display
                  _buildFormLabel('From Account'),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                        color: isDark ? Colors.grey[800] : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          fromAccount['accountName'] ??
                              'No source account found',
                          style: TextStyle(
                              fontSize: 16, fontWeight: FontWeight.w600),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Available Balance: KES ${fromAccount['balance'] ?? '0.00'}',
                          style:
                              TextStyle(fontSize: 14, color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 20),

                  // Loan Account to Repay
                  _buildFormLabel('Loan Account'),
                  DropdownButtonFormField<String>(
                    value: selectedToAccount,
                    items: toAccounts.map<DropdownMenuItem<String>>((account) {
                      return DropdownMenuItem<String>(
                        value: account['accountNo'],
                        child: Text(
                            '${account['accountName']} - ${account['accountNo']}'),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedToAccount = value;
                        formData['otherAccNo'] = value;
                      });
                    },
                    decoration:
                        _getInputDecoration(hint: 'Select loan account'),
                  ),
                  SizedBox(height: 20),

                  // Amount
                  _buildFormLabel('Amount (KES)'),
                  TextFormField(
                    keyboardType: TextInputType.number,
                    onChanged: (value) => formData['amt'] = value,
                    decoration: _getInputDecoration(
                        hint: 'Enter amount to repay', prefix: 'KES '),
                  ),
                  SizedBox(height: 20),

                  // PIN
                  _buildFormLabel('Enter PIN'),
                  TextFormField(
                    obscureText: true,
                    keyboardType: TextInputType.number,
                    maxLength: 4,
                    onChanged: (value) => formData['cmp'] = value,
                    decoration: _getInputDecoration(hint: '••••', isPin: true),
                    style: TextStyle(fontSize: 20, letterSpacing: 8),
                  ),
                  SizedBox(height: 30),

                  // Repay Button
                  ElevatedButton(
                    onPressed: isProcessing ? null : _onContinue,
                    style: ElevatedButton.styleFrom(
                        backgroundColor: ColorPalette.secondary,
                        foregroundColor: Colors.white,
                        minimumSize: Size(double.infinity, 56),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16))),
                    child: isProcessing
                        ? CircularProgressIndicator(color: Colors.white)
                        : Text('Repay Loan',
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold)),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
