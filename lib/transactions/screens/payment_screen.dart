import 'package:flutter/material.dart';
import '../../utils/color_palette.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'otp_verification_screen.dart';

class PaymentScreen extends StatefulWidget {
  final String? loanTitle;
  final String? loanAmount;
  final String? interestRate;
  final String? duration;
  final Map<String, dynamic>? loanDetails;
  
  const PaymentScreen({
    super.key,
    this.loanTitle,
    this.loanAmount,
    this.interestRate,
    this.duration,
    this.loanDetails,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _pinController = TextEditingController();
  bool _showPin = false;
  String? _selectedMethod; // Track selected payment method

  void _selectMethod(String method) {
    setState(() {
      _selectedMethod = method;
    });
  }

  void _processPayment(BuildContext context) {
    if (_amountController.text.isEmpty || _pinController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Please enter amount and PIN')),
      );
      return;
    }

    if (_selectedMethod == 'Savings Account') {
      // Show OTP as dialog for savings account payments
      showDialog(
        context: context,
        builder: (context) => OtpVerificationScreen(
          phoneNumber: '+254*****1234', // Replace with actual user phone
          transactionType: 'Loan Payment',
          amount: _amountController.text,
          transactionDetails: {
            'amount': _amountController.text,
            'payment_method': _selectedMethod!,
            'transaction_type': 'Loan Payment',
          },
        ),
      );
    } else {
      // Direct payment for mobile money
      _completePayment(context);
    }
  }

  void _completePayment(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    // Payment processing logic here
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Dialog(
          insetPadding: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[900] : Colors.grey[100],
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: ColorPalette.primary,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.schedule,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'Transaction Initiated',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: ColorPalette.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: ColorPalette.primary.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Column(
                          children: [
                            Text(
                              'KES ${_amountController.text}',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: ColorPalette.primary,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Payment via $_selectedMethod',
                              style: TextStyle(
                                fontSize: 14,
                                color: isDark ? Colors.grey[400] : Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Your payment request has been sent to $_selectedMethod. Please check your phone for a payment prompt and complete the transaction.',
                        style: TextStyle(
                          fontSize: 14,
                          color: isDark ? Colors.grey[300] : Colors.grey[700],
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: isDark ? Colors.grey[800] : Colors.grey[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: ColorPalette.primary,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'You will receive an SMS confirmation once the payment is processed.',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context); // Close dialog
                            Navigator.pop(context); // Return to previous screen
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorPalette.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Text(
                            'OK',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: isDark ? Theme.of(context).scaffoldBackgroundColor : ColorPalette.greyBackground,
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Loan Payment',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        centerTitle: true,
      ),
      body: Container(
        color: isDark ? Theme.of(context).scaffoldBackgroundColor : ColorPalette.greyBackground,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Loan Details Card (if loan details are provided) - Compact with payable amount
                    if (widget.loanTitle != null) ...[
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: isDark ? Colors.grey[850] : Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              widget.loanTitle!.contains('School') ? Icons.school :
                              widget.loanTitle!.contains('Home') ? Icons.home :
                              widget.loanTitle!.contains('Car') ? Icons.directions_car :
                              widget.loanTitle!.contains('Biashara') ? Icons.business :
                              widget.loanTitle!.contains('Kilimo') ? Icons.agriculture :
                              Icons.monetization_on,
                              color: ColorPalette.secondary,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.loanTitle!,
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: isDark ? Colors.white : ColorPalette.textDark,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  if (widget.loanDetails != null && widget.loanDetails!.containsKey('Payable Amount'))
                                    Text(
                                      'Loan Balance: ${widget.loanDetails!['Payable Amount']}',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                        color: ColorPalette.primary,
                                      ),
                                    )
                                  else if (widget.loanAmount != null)
                                    Text(
                                      'Amount: ${widget.loanAmount}',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                        color: ColorPalette.primary,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            if (widget.loanDetails != null && widget.loanDetails!.containsKey('Interest Rate'))
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: ColorPalette.primary.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Text(
                                  widget.loanDetails!['Interest Rate'].toString(),
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: ColorPalette.primary,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 12),
                    ],
                    
                    Text(
                      'Select Payment Method',
                      style: TextStyle(
                        color: ColorPalette.primary,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Payment Methods - Reduced padding
                    _buildPaymentMethod(
                      icon: 'assets/icons/mpesa.png',
                      label: 'M-Pesa',
                      onTap: () => _selectMethod('M-Pesa'),
                      isSelected: _selectedMethod == 'M-Pesa',
                    ),
                    _buildPaymentMethod(
                      icon: 'assets/icons/airtel_money.png',
                      label: 'Airtel Money',
                      onTap: () => _selectMethod('Airtel Money'),
                      isSelected: _selectedMethod == 'Airtel Money',
                    ),
                    _buildPaymentMethod(
                      icon: Icons.account_balance,
                      label: 'Pay from Account (Savings)',
                      onTap: () => _selectMethod('Savings Account'),
                      isSelected: _selectedMethod == 'Savings Account',
                      isIcon: true,
                    ),

                    // Show amount and PIN only when method is selected
                    if (_selectedMethod != null) ...[
                      const SizedBox(height: 16),
                      
                      // Payment Details Section - Reduced padding
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: isDark ? Colors.grey[850] : Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                          ),
                          boxShadow: [
                            if (!isDark)
                              BoxShadow(
                                color: Colors.grey.withValues(alpha: 0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Payment Details',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: isDark ? Colors.white : ColorPalette.primary,
                              ),
                            ),
                            const SizedBox(height: 12),
                            
                            // Amount Field - Compact
                            TextFormField(
                              controller: _amountController,
                              keyboardType: TextInputType.number,
                              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                              style: TextStyle(
                                color: isDark ? Colors.white : Colors.black,
                                fontSize: 14,
                              ),
                              decoration: InputDecoration(
                                labelText: 'Amount (KES)',
                                labelStyle: TextStyle(
                                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                                  fontSize: 12,
                                ),
                                prefixIcon: Icon(
                                  Icons.attach_money,
                                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                                  size: 20,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: isDark ? Colors.grey[600]! : Colors.grey[300]!,
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: isDark ? Colors.grey[600]! : Colors.grey[300]!,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: ColorPalette.primary,
                                    width: 2,
                                  ),
                                ),
                                filled: true,
                                fillColor: isDark ? Colors.grey[800] : Colors.grey[50],
                                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                                isDense: true,
                              ),
                            ),
                            const SizedBox(height: 12),
                            
                            // PIN Field - Compact
                            TextFormField(
                              controller: _pinController,
                              obscureText: !_showPin,
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                LengthLimitingTextInputFormatter(4),
                              ],
                              style: TextStyle(
                                color: isDark ? Colors.white : Colors.black,
                                fontSize: 14,
                                letterSpacing: _showPin ? 0 : 6,
                              ),
                              decoration: InputDecoration(
                                labelText: 'PIN',
                                labelStyle: TextStyle(
                                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                                  fontSize: 12,
                                ),
                                prefixIcon: Icon(
                                  Icons.lock_outline,
                                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                                  size: 20,
                                ),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _showPin ? Icons.visibility_off : Icons.visibility,
                                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                                    size: 20,
                                  ),
                                  onPressed: () => setState(() => _showPin = !_showPin),
                                  padding: EdgeInsets.zero,
                                  constraints: const BoxConstraints(),
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: isDark ? Colors.grey[600]! : Colors.grey[300]!,
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: isDark ? Colors.grey[600]! : Colors.grey[300]!,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: ColorPalette.primary,
                                    width: 2,
                                  ),
                                ),
                                filled: true,
                                fillColor: isDark ? Colors.grey[800] : Colors.grey[50],
                                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                                isDense: true,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Reduced bottom space
                      const SizedBox(height: 80),
                    ],
                  ],
                ),
              ),
            ),
            // Fixed button at bottom
            if (_selectedMethod != null)
              Container(
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: isDark ? Theme.of(context).scaffoldBackgroundColor : ColorPalette.greyBackground,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: SafeArea(
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => _processPayment(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorPalette.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        'Complete Payment',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethod({
    required dynamic icon,
    required String label,
    required VoidCallback onTap,
    bool isIcon = false,
    bool isSelected = false,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isSelected 
            ? (isDark ? ColorPalette.primary.withValues(alpha: 0.2) : ColorPalette.primary.withValues(alpha: 0.1))
            : (isDark ? Colors.grey[800] : Colors.white),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected 
              ? ColorPalette.primary
              : (isDark ? Colors.grey[600]! : Colors.grey[300]!),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            isIcon 
                ? Icon(
                    icon as IconData, 
                    size: 32, 
                    color: isSelected ? ColorPalette.primary : Colors.blue
                  )
                : Image.asset(
                    icon as String,
                    width: 32,
                    height: 32,
                  ),
            if (isSelected) ...[
              const SizedBox(width: 6),
              Container(
                padding: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  color: ColorPalette.primary,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 12,
                ),
              ),
            ],
          ],
        ),
        title: Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected 
                ? ColorPalette.primary 
                : (isDark ? Colors.white : Colors.black87),
          ),
        ),
        trailing: Icon(
          isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
          color: isSelected ? ColorPalette.primary : Colors.grey,
          size: 20,
        ),
        onTap: onTap,
      ),
    );
  }
} 