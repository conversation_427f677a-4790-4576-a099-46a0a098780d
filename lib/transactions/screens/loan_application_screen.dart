import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../utils/color_palette.dart';
import 'otp_verification_screen.dart';

class LoanApplicationScreen extends StatefulWidget {
  final String loanType;
  final String maxAmount;
  final int maxAmountValue;
  final String interestRate;

  const LoanApplicationScreen({
    super.key,
    required this.loanType,
    required this.maxAmount,
    required this.maxAmountValue,
    required this.interestRate,
  });

  @override
  State<LoanApplicationScreen> createState() => _LoanApplicationScreenState();
}

class _LoanApplicationScreenState extends State<LoanApplicationScreen> {
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _pinController = TextEditingController();
  bool _showPin = false;
  String? _amountError; // Track validation error for the amount

  // Validate the loan amount
  void _validateAmount(String value) {
    final amount = int.tryParse(value);
    if (amount == null) {
      setState(() => _amountError = 'Please enter a valid amount');
    } else if (amount > widget.maxAmountValue) {
      setState(() => _amountError = 'Amount cannot exceed ${widget.maxAmount}');
    } else {
      setState(() => _amountError = null);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Apply for ${widget.loanType}',
          style: const TextStyle(
            color: Colors.white,
          ),
        ),
        backgroundColor: ColorPalette.secondary,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        color: isDark ? Theme.of(context).scaffoldBackgroundColor : Colors.grey[200],
        height: double.infinity,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Loan Details Card
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isDark ? Colors.grey[800] : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Loan Details',
                      style: TextStyle(
                        color: ColorPalette.primary,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildDetailRow('Loan Type', widget.loanType),
                    _buildDetailRow('Maximum Amount', widget.maxAmount),
                    _buildDetailRow('Interest Rate', widget.interestRate),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Amount Input
              Text(
                'Loan Amount (KES)',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: isDark ? Colors.white : ColorPalette.primary,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _amountController,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                decoration: InputDecoration(
                  hintText: 'Enter amount',
                  filled: true,
                  fillColor: isDark ? Colors.grey[800] : Colors.white,
                  prefixIcon: Icon(Icons.attach_money, color: ColorPalette.primary),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: ColorPalette.secondary,
                      width: 2,
                    ),
                  ),
                  errorText: _amountError, // Show validation error
                ),
                style: TextStyle(
                  color: isDark ? Colors.white : Colors.black87,
                ),
                onChanged: (value) {
                  if (value.isNotEmpty) {
                    _validateAmount(value);
                  } else {
                    setState(() => _amountError = null);
                  }
                },
                onFieldSubmitted: (_) => _validateAmount(_amountController.text),
              ),
              const SizedBox(height: 20),

              // PIN Input
              Text(
                'Enter PIN',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: isDark ? Colors.white : ColorPalette.primary,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _pinController,
                obscureText: !_showPin,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                maxLength: 4,
                decoration: InputDecoration(
                  hintText: 'Enter 4-digit PIN',
                  filled: true,
                  fillColor: isDark ? Colors.grey[800] : Colors.white,
                  prefixIcon: Icon(Icons.lock_outline, color: ColorPalette.primary),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _showPin ? Icons.visibility : Icons.visibility_off,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                    onPressed: () => setState(() => _showPin = !_showPin),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: ColorPalette.secondary,
                      width: 2,
                    ),
                  ),
                ),
                style: TextStyle(
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
              const SizedBox(height: 40),

              // Action Buttons
              Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: Row(
                  children: [
                    // Cancel Button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[300],
                          foregroundColor: Colors.black87,
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Continue Button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          // Validate fields are not empty
                          if (_amountController.text.isEmpty || _pinController.text.isEmpty) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Please fill all required fields'),
                                backgroundColor: Colors.red,
                              ),
                            );
                            return;
                          }

                          // Validate amount is a number
                          final amount = int.tryParse(_amountController.text);
                          if (amount == null) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Please enter a valid amount'),
                                backgroundColor: Colors.red,
                              ),
                            );
                            return;
                          }

                          // Validate amount doesn't exceed maximum
                          if (amount > widget.maxAmountValue) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Amount cannot exceed ${widget.maxAmount}'),
                                backgroundColor: Colors.red,
                              ),
                            );
                            return;
                          }

                          // Proceed if validation passes
                          showDialog(
                            context: context,
                            builder: (context) => OtpVerificationScreen(
                              phoneNumber: '+254*****5673',
                              transactionType: '${widget.loanType} Loan',
                              amount: _amountController.text,
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorPalette.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Continue',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: isDark ? Colors.grey[400] : Colors.grey[700],
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: isDark ? Colors.white : Colors.black87,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}