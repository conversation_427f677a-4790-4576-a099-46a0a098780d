import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../utils/color_palette.dart';
import 'loan_application_screen.dart';

class ApplyLoanScreen extends StatelessWidget {
  const ApplyLoanScreen({super.key});

  Widget _buildPieChart() {
    return SizedBox(
      height: 150,
      child: Stack(
        children: [
          <PERSON><PERSON><PERSON>(
            PieChartData(
              sectionsSpace: 2,
              centerSpaceRadius: 40,
              sections: [
                PieChartSectionData(
                  color: ColorPalette.secondary,
                  value: 20000,
                  title: '',
                  radius: 20,
                ),
                PieChartSectionData(
                  color: ColorPalette.primary,
                  value: 15000,
                  title: '',
                  radius: 20,
                ),
                PieChartSectionData(
                  color: Colors.pink[100],
                  value: 18000,
                  title: '',
                  radius: 20,
                ),
              ],
            ),
          ),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${(53000).toStringAsFixed(0)}KES',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'Total',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoanOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String interestRate,
    required String duration,
    required String maxAmount,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.only(bottom: 15),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: isDark ? ColorPalette.white.withValues(alpha: 0.05) : ColorPalette.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          if (!isDark)
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(icon, color: isDark ? Colors.white70 : ColorPalette.secondary),
              const SizedBox(width: 10),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : ColorPalette.textDark,
                ),
              ),
              const Spacer(),
              ElevatedButton(
                onPressed: () => _handleApplyLoan(context, title),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorPalette.primary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: Text(
                  'Apply loan',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    interestRate,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: isDark ? Colors.white : ColorPalette.textDark,
                    ),
                  ),
                  Text(
                    'Interest rate',
                    style: TextStyle(
                      fontSize: 12,
                      color: isDark ? Colors.white70 : Colors.grey,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    duration,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: isDark ? Colors.white : ColorPalette.textDark,
                    ),
                  ),
                  Text(
                    'Duration',
                    style: TextStyle(
                      fontSize: 12,
                      color: isDark ? Colors.white70 : Colors.grey,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    maxAmount,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: isDark ? Colors.white : ColorPalette.textDark,
                    ),
                  ),
                  Text(
                    'Maximum amount',
                    style: TextStyle(
                      fontSize: 12,
                      color: isDark ? Colors.white70 : Colors.grey,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _handleApplyLoan(BuildContext context, String loanType) {
    final loanDetails = _getLoanDetails(loanType);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LoanApplicationScreen(
          loanType: loanType,
          maxAmount: loanDetails['maxAmount']!,
          maxAmountValue: int.parse(loanDetails['maxAmountValue']!),
          interestRate: loanDetails['interestRate']!,
        ),
      ),
    );
  }

  Map<String, String> _getLoanDetails(String loanType) {
    switch (loanType) {
      case 'Home loan':
        return {
          'maxAmount': '100,000 KES',
          'maxAmountValue': '100000',
          'interestRate': '2.5% p.a',
        };
      case 'Car loan':
        return {
          'maxAmount': '500,000 KES',
          'maxAmountValue': '500000',
          'interestRate': '10% p.a',
        };
      case 'School fees loan':
        return {
          'maxAmount': '40,000 KES',
          'maxAmountValue': '40000',
          'interestRate': '5% p.a',
        };
      default:
        return {
          'maxAmount': '0 KES',
          'maxAmountValue': '0',
          'interestRate': '0%',
        };
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? theme.scaffoldBackgroundColor : Colors.grey[200],
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Apply loan',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              margin: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFF6B4E71),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Your top eligible loans',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 0.1),
                  _buildPieChart(),
                  const SizedBox(height: 0.1),
                  Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: ColorPalette.secondary,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Home loan',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(width: 5),
                      Text(
                        '20,000KES',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: Colors.pink[100],
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Car loan',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(width: 5),
                      Text(
                        '18,000 KES',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: ColorPalette.primary,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'School fees loan',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(width: 5),
                      Text(
                        '15,000 KES',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  _buildLoanOption(
                    context: context,
                    icon: Icons.home,
                    title: 'Home loan',
                    interestRate: '2.5% p.a',
                    duration: '2years',
                    maxAmount: '100,000KES',
                  ),
                  _buildLoanOption(
                    context: context,
                    icon: Icons.directions_car,
                    title: 'Car loan',
                    interestRate: '10% p.a',
                    duration: '5years',
                    maxAmount: '500,000KES',
                  ),
                  _buildLoanOption(
                    context: context,
                    icon: Icons.school,
                    title: 'School fees loan',
                    interestRate: '5% p.a',
                    duration: '3months',
                    maxAmount: '40,000KES',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}