import 'package:flutter/material.dart';
import '../../models/account_model.dart';
import '../../utils/services/account_manager.dart';
import '../../utils/services/transaction_service.dart';
import '../../utils/color_palette.dart';
import 'transaction_receipt_screen.dart';

class MiniStatementScreen extends StatefulWidget {
  const MiniStatementScreen({super.key});

  @override
  State<MiniStatementScreen> createState() => _MiniStatementScreenState();
}

class _MiniStatementScreenState extends State<MiniStatementScreen> {
  final TransactionService _transactionService = TransactionService();
  final TextEditingController _pinController = TextEditingController();

  AccountModel? _selectedAccount;
  bool _obscurePin = true;
  bool _isLoading = false;

  List<dynamic>? _statementTransactions;
  Map<String, dynamic>? _statementDetails;

  List<AccountModel> _accounts = [];

  @override
  void initState() {
    super.initState();
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    try {
      final accountList = await AccountManager.fetchFreshAccounts();
      setState(() {
        _accounts = accountList.map((acc) {
          final List<String> actions = [];
          if (acc['canDeposit'] == 'Yes') actions.add('deposit');
          if (acc['canWithdraw'] == 'Yes') actions.add('withdraw');

          return AccountModel(
            name: acc['accountName'],
            balance: acc['balance'] ?? '0.00',
            accountNo: acc['accountNo'],
            allowedActions: actions,
            icon: Icons.account_balance_wallet,
            backgroundColor: ColorPalette.primary,
            rawAccount: acc,
          );
        }).where((acc) => (acc.rawAccount?['isLoanAccount'] ?? 'No') == 'No').toList();
      });
    } catch (e) {
      // Handle error fetching accounts
    }
  }

  @override
  void dispose() {
    _pinController.dispose();
    super.dispose();
  }

  Future<void> _requestMiniStatement() async {
    if (_pinController.text.isNotEmpty && _selectedAccount != null) {
      setState(() {
        _isLoading = true;
      });

      final pin = _pinController.text;

      await _transactionService.processTransaction(
        serviceCode: 'MS',
        formData: {
          'accNo': _selectedAccount!.accountNo,
          'cmp': pin,
        },
        context: context,
        statusCallback: (isLoading) {
          setState(() {
            _isLoading = isLoading;
          });
        },
        completedCallback: (response, success) {
          setState(() {
            _isLoading = false;
          });
          if (success) {
            final msm = response['responseData']['msm'] as String? ?? '';
            final transactions = msm.split('|').where((s) => s.trim().isNotEmpty).toList();
            
            Map<String, dynamic> receiptData = {
              'Transaction Type': 'Mini Statement',
              'Account': _selectedAccount!.name,
              'Account Number': _selectedAccount!.accountNo,
              'Status': 'Successful',
            };

            for (var i = 0; i < transactions.length; i++) {
              receiptData['Transaction ${i + 1}'] = transactions[i].trim();
            }

            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TransactionReceiptScreen(
                  receiptData: receiptData,
                  serviceCode: 'MS',
                ),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    response['header']['sd'] ?? 'Failed to get statement'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        otpCallback: () async {
          // Handle OTP if needed, returning the OTP string
          return null;
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? Colors.black : ColorPalette.greyBackground,
      appBar: AppBar(
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: ColorPalette.textLight),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Mini Statement',
          style: TextStyle(
            color: ColorPalette.textLight,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : _statementTransactions != null
              ? _buildStatementView()
              : _buildPinEntryForm(),
    );
  }

  Widget _buildPinEntryForm() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[900] : ColorPalette.greyBackground,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'From Account',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: ColorPalette.primary,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: isDark ? Colors.grey[800] : ColorPalette.greyBackground,
                border: Border.all(
                  color: isDark ? Colors.grey[700]! : Colors.grey.shade300,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Theme(
                data: Theme.of(context).copyWith(
                  canvasColor:
                      isDark ? Colors.grey[800] : ColorPalette.greyBackground,
                ),
                child: DropdownButtonFormField<AccountModel>(
                  value: _selectedAccount,
                  icon: Icon(Icons.keyboard_arrow_down),
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                    border: InputBorder.none,
                    hintStyle: TextStyle(
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                  style: TextStyle(
                    color: isDark ? Colors.white : Colors.black,
                  ),
                  hint: Text('Select account'),
                  isExpanded: true,
                  items: _accounts.map((AccountModel account) {
                    return DropdownMenuItem<AccountModel>(
                      value: account,
                      child: Text(
                          "${account.name} - ${account.accountNo}",
                          overflow: TextOverflow.ellipsis),
                    );
                  }).toList(),
                  onChanged: (AccountModel? newValue) {
                    setState(() {
                      _selectedAccount = newValue;
                    });
                  },
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Enter Pin',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: ColorPalette.primary,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: isDark ? Colors.grey[800] : ColorPalette.greyBackground,
                border: Border.all(
                  color: isDark ? Colors.grey[700]! : Colors.grey.shade300,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextField(
                controller: _pinController,
                obscureText: _obscurePin,
                keyboardType: TextInputType.number,
                maxLength: 4,
                style: TextStyle(
                  color: isDark ? Colors.white : Colors.black87,
                ),
                decoration: InputDecoration(
                  hintText: 'Enter PIN',
                  hintStyle: TextStyle(
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                  ),
                  counterText: '',
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  border: InputBorder.none,
                  prefixIcon: Icon(
                    Icons.lock_outline,
                    color: isDark ? Colors.grey[400] : Colors.grey,
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePin ? Icons.visibility_off : Icons.visibility,
                      color: isDark ? Colors.grey[400] : Colors.grey,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePin = !_obscurePin;
                      });
                    },
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _requestMiniStatement,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorPalette.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Request Statement',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatementView() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[900] : ColorPalette.greyBackground,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              color: isDark ? Colors.grey[900] : ColorPalette.greyBackground,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _selectedAccount?.name ?? 'Account',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: ColorPalette.primary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _selectedAccount?.accountNo ?? '',
                    style: TextStyle(
                      fontSize: 14,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Opening Balance:',
                        style: TextStyle(
                            fontSize: 14,
                            color: isDark ? Colors.grey[400] : Colors.grey[700]),
                      ),
                      Text(
                        _statementDetails?['openingBalance'] ?? '0.00',
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: isDark ? Colors.white : Colors.black),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Closing Balance:',
                        style: TextStyle(
                            fontSize: 14,
                            color: isDark ? Colors.grey[400] : Colors.grey[700]),
                      ),
                      Text(
                        _statementDetails?['closingBalance'] ?? '0.00',
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: isDark ? Colors.white : Colors.black),
                      ),
                    ],
                  )
                ],
              ),
            ),
            const Divider(height: 1),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _statementTransactions?.length ?? 0,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final transaction = _statementTransactions![index];
                final bool isCredit =
                    transaction['type']?.toLowerCase() == 'credit';

                return ListTile(
                  title: Text(
                    transaction['description'] ?? 'N/A',
                    style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: isDark ? Colors.white : Colors.black87),
                  ),
                  subtitle: Text(
                    transaction['date'] ?? 'N/A',
                    style: TextStyle(
                        color: isDark ? Colors.grey[400] : Colors.grey[600]),
                  ),
                  trailing: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${isCredit ? '+' : '-'} ${transaction['amount'] ?? '0.00'}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: isCredit ? Colors.green : Colors.red,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        'Bal: ${transaction['balance'] ?? '0.00'}',
                        style: TextStyle(
                          color: isDark ? Colors.grey[400] : Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
