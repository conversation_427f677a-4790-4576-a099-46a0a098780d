import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';

import '../../providers/theme_provider.dart';
import '../../utils/color_palette.dart';
import 'dashboard_screen.dart';
import '../../utils/services/shared_preferences_helper.dart';

class TransactionReceiptScreen extends StatelessWidget {
  final Map<String, dynamic> receiptData;
  final String serviceCode;

  TransactionReceiptScreen({
    super.key,
    required this.receiptData,
    required this.serviceCode,
  });

  final ScreenshotController screenshotController = ScreenshotController();

  String _formatLabel(String key) {
    // Add spaces before capital letters
    String formatted = key.replaceAllMapped(RegExp(r'(?<=[a-z])[A-Z]'), (Match m) => ' ${m.group(0)}');
    // Capitalize the first letter and return
    return formatted[0].toUpperCase() + formatted.substring(1);
  }
  
  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    List<Map<String, dynamic>> details = [];

    receiptData.forEach((key, value) {
      if (value != null && value.toString().isNotEmpty) {
        details.add({
          'label': _formatLabel(key),
          'value': value.toString(),
        });
      }
    });

    return Scaffold(
      backgroundColor: themeProvider.isDarkMode ? Theme.of(context).scaffoldBackgroundColor : Colors.grey[200],
      appBar: AppBar(
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.close, color: ColorPalette.textLight),
          onPressed: () async {
            final userName = await SharedPreferencesHelper.getCustomerName() ?? 'User';
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => DashboardScreen(userName: userName)),
              (Route<dynamic> route) => false,
            );
          },
        ),
        title: Text(
          'Transaction Receipt',
          style: TextStyle(
            color: ColorPalette.textLight,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          // IconButton(
          //   icon: Icon(Icons.share, color: ColorPalette.textLight),
          //   onPressed: () => _shareReceiptScreenshot(context),
          // ),
        ],
      ),
      body: Screenshot(
        controller: screenshotController,
        child: Container(
          color: themeProvider.isDarkMode ? Theme.of(context).scaffoldBackgroundColor : Colors.grey[200],
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 48,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Transaction Successful',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(height: 24),
                Container(
                  decoration: BoxDecoration(
                    color: themeProvider.isDarkMode ? Colors.grey[800] : Colors.white,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: details.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              details[index]['label'],
                              style: TextStyle(
                                fontSize: 16,
                                color: themeProvider.isDarkMode ? Colors.white70 : Colors.black54,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Text(
                                details[index]['value'],
                                textAlign: TextAlign.end,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                    separatorBuilder: (context, index) => Divider(
                      height: 1,
                      color: themeProvider.isDarkMode ? Colors.grey[700] : Colors.grey[300],
                      indent: 16,
                      endIndent: 16,
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _shareReceiptScreenshot(BuildContext context) async {
    final image = await screenshotController.capture();
    if (image != null) {
      final directory = await getTemporaryDirectory();
      final imagePath = await File('${directory.path}/receipt.png').create();
      await imagePath.writeAsBytes(image);
      await Share.shareFiles([imagePath.path], text: 'Here is my transaction receipt.');
    }
  }
}