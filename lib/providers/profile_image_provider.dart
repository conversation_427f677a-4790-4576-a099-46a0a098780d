import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import '../utils/services/profile_picture_service.dart';
import '../utils/image_helper.dart';

import '../utils/app_logger.dart';
class ProfileImageProvider with ChangeNotifier {
  File? _profileImage;
  final ImagePicker _picker = ImagePicker();
  final ProfilePictureService _profilePictureService = ProfilePictureService();
  bool _isLoading = false;
  bool _syncWithBackend = true; // Flag to enable/disable backend sync - enabled with workaround for current backend implementation

  File? get profileImage => _profileImage;
  bool get isLoading => _isLoading;
  bool get syncWithBackend => _syncWithBackend;

  /// Sets whether to sync with backend
  void setSyncWithBackend(bool sync) {
    _syncWithBackend = sync;
    notifyListeners();
  }

  // Load saved profile image path on app start and fetch from backend if enabled
  Future<void> loadProfileImage() async {
    _isLoading = true;
    notifyListeners();

    try {
      // First try to load from local storage
      final prefs = await SharedPreferences.getInstance();
      final imagePath = prefs.getString('profile_image_path');
      if (imagePath != null && File(imagePath).existsSync()) {
        _profileImage = File(imagePath);
        notifyListeners();
      }

      // If backend sync is enabled, try to fetch from server
      if (_syncWithBackend) {
        await _fetchFromBackend();
      }
    } catch (e) {
      AppLogger.error('loading profile image: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Fetches profile picture from backend and saves locally
  Future<void> _fetchFromBackend() async {
    try {
      final imageBytes = await _profilePictureService.fetchProfilePicture();
      if (imageBytes != null && isValidImage(imageBytes)) {
        // Save to local file with proper error handling
        final directory = await getApplicationDocumentsDirectory();
        
        // Try different file extensions based on the actual image format
        String fileExtension = _detectImageExtension(imageBytes);
        final imagePath = '${directory.path}/profile_image_server$fileExtension';
        final imageFile = File(imagePath);
        
        try {
          // Delete existing file if it exists
          if (await imageFile.exists()) {
            await imageFile.delete();
          }
          
          // Write the image bytes to file
          await imageFile.writeAsBytes(imageBytes, flush: true);
          
          // Verify the file was written correctly by reading it back
          final writtenBytes = await imageFile.readAsBytes();
          if (writtenBytes.length != imageBytes.length) {
            AppLogger.error('File write verification failed: expected ${imageBytes.length} bytes, got ${writtenBytes.length}');
            return;
          }
          
          // Additional validation: try to decode the saved image using Flutter's image package
          if (!isValidImage(writtenBytes)) {
            AppLogger.error('Saved image file failed validation check');
            // Delete the corrupted file
            if (await imageFile.exists()) {
              await imageFile.delete();
            }
            return;
          }
          
          // Update the current image only if everything is successful
          _profileImage = imageFile;
          await _saveImagePath(imagePath);

          AppLogger.info('Profile picture fetched from backend successfully with extension: $fileExtension');
        } catch (e) {
          AppLogger.error('Error saving profile image to file: $e');
          // Clean up any partially written file
          if (await imageFile.exists()) {
            try {
              await imageFile.delete();
            } catch (deleteError) {
              AppLogger.error('Error deleting corrupted image file: $deleteError');
            }
          }
        }
      } else {
        AppLogger.info('Fetched data is not a valid image or is null. Skipping save.');
      }
    } catch (e) {
      AppLogger.error('fetching profile picture from backend: $e');
    }
  }
  
  /// Detects the proper file extension based on image bytes
  String _detectImageExtension(List<int> bytes) {
    if (bytes.length < 4) return '.jpg'; // Default fallback
    
    // JPEG: FF D8 FF
    if (bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) {
      return '.jpg';
    }
    
    // PNG: 89 50 4E 47
    if (bytes.length >= 8 && 
        bytes[0] == 0x89 && bytes[1] == 0x50 && 
        bytes[2] == 0x4E && bytes[3] == 0x47) {
      return '.png';
    }
    
    // GIF: 47 49 46 38
    if (bytes.length >= 6 && 
        bytes[0] == 0x47 && bytes[1] == 0x49 && 
        bytes[2] == 0x46 && bytes[3] == 0x38) {
      return '.gif';
    }
    
    // WebP: 52 49 46 46 (RIFF) + WebP signature
    if (bytes.length >= 12 && 
        bytes[0] == 0x52 && bytes[1] == 0x49 && 
        bytes[2] == 0x46 && bytes[3] == 0x46 &&
        bytes[8] == 0x57 && bytes[9] == 0x45 && 
        bytes[10] == 0x42 && bytes[11] == 0x50) {
      return '.webp';
    }
    
    // BMP: 42 4D
    if (bytes[0] == 0x42 && bytes[1] == 0x4D) {
      return '.bmp';
    }
    
    return '.jpg'; // Default fallback
  }

  // Save profile image path
  Future<void> _saveImagePath(String path) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('profile_image_path', path);
  }

  // Pick image from gallery
  Future<void> pickImageFromGallery() async {
    _isLoading = true;
    notifyListeners();

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        _profileImage = File(image.path);
        await _saveImagePath(image.path);
        notifyListeners();

        // Save to backend if enabled
        if (_syncWithBackend) {
          await _saveToBackend(File(image.path));
        }
      }
    } catch (e) {
      AppLogger.error('picking image from gallery: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Take photo with camera
  Future<void> takePhoto() async {
    _isLoading = true;
    notifyListeners();

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        _profileImage = File(image.path);
        await _saveImagePath(image.path);
        notifyListeners();

        // Save to backend if enabled
        if (_syncWithBackend) {
          await _saveToBackend(File(image.path));
        }
      }
    } catch (e) {
      AppLogger.error('taking photo: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Saves profile picture to backend
  Future<void> _saveToBackend(File imageFile) async {
    try {
      final success = await _profilePictureService.saveProfilePicture(imageFile);
      if (success) {
        AppLogger.info('Profile picture saved to backend successfully');
      } else {
        AppLogger.info('Failed to save profile picture to backend');
      }
    } catch (e) {
      AppLogger.error('saving profile picture to backend: $e');
    }
  }

  // Remove profile image
  Future<void> removeProfileImage() async {
    _isLoading = true;
    notifyListeners();

    try {
      _profileImage = null;
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('profile_image_path');

      // Delete from backend if enabled
      if (_syncWithBackend) {
        await _deleteFromBackend();
      }
    } catch (e) {
      AppLogger.error('removing profile image: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Deletes profile picture from backend
  Future<void> _deleteFromBackend() async {
    try {
      final success = await _profilePictureService.deleteProfilePicture();
      if (success) {
        AppLogger.info('Profile picture deleted from backend successfully');
      } else {
        AppLogger.info('Failed to delete profile picture from backend');
      }
    } catch (e) {
      AppLogger.error('deleting profile picture from backend: $e');
    }
  }

  // Show image source selection dialog
  Future<void> showImageSourceDialog(BuildContext context) async {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        final theme = Theme.of(context);
        final isDark = theme.brightness == Brightness.dark;
        
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          backgroundColor: isDark ? theme.dialogBackgroundColor : Colors.white,
          title: Text(
            'Select Profile Photo',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.textTheme.bodyLarge?.color,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.camera_alt,
                    color: Colors.blue,
                  ),
                ),
                title: Text('Take Photo'),
                subtitle: Text('Use camera to take a new photo'),
                onTap: () {
                  Navigator.pop(context);
                  takePhoto();
                },
              ),
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.photo_library,
                    color: Colors.green,
                  ),
                ),
                title: Text('Choose from Gallery'),
                subtitle: Text('Select from your photo library'),
                onTap: () {
                  Navigator.pop(context);
                  pickImageFromGallery();
                },
              ),
              if (_profileImage != null)
                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.delete,
                      color: Colors.red,
                    ),
                  ),
                  title: Text('Remove Photo'),
                  subtitle: Text('Use default avatar'),
                  onTap: () {
                    Navigator.pop(context);
                    removeProfileImage();
                  },
                ),
            ],
          ),
        );
      },
    );
  }
} 