import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum ThemePreference { system, light, dark }

class ThemeProvider with ChangeNotifier {
  static const String _themePreferenceKey = 'theme_preference';
  final SharedPreferences? _prefs;
  ThemePreference _themePreference;

  // Normal constructor with SharedPreferences
  ThemeProvider(this._prefs) : _themePreference = ThemePreference.system {
    try {
      _loadPreferences();
    } catch (e) {
      // If loading preferences fails, use system default
      _themePreference = ThemePreference.system;
      // Try to save the default preference
      _savePreference().catchError((error) {
        // Silent error handling
      });
    }
  }

  // Fallback constructor without SharedPreferences
  ThemeProvider.fallback() : _prefs = null, _themePreference = ThemePreference.system;

  ThemePreference get themePreference => _themePreference;

  ThemeMode get themeMode {
    switch (_themePreference) {
      case ThemePreference.system:
        return ThemeMode.system;
      case ThemePreference.light:
        return ThemeMode.light;
      case ThemePreference.dark:
        return ThemeMode.dark;
    }
  }

  // For backward compatibility and UI checks
  bool get isDarkMode => _themePreference == ThemePreference.dark;
  bool get isSystemMode => _themePreference == ThemePreference.system;
  bool get isLightMode => _themePreference == ThemePreference.light;

  void _loadPreferences() {
    if (_prefs == null) return;
    
    try {
      final savedTheme = _prefs!.getString(_themePreferenceKey);
      if (savedTheme != null) {
        switch (savedTheme) {
          case 'system':
            _themePreference = ThemePreference.system;
            break;
          case 'light':
            _themePreference = ThemePreference.light;
            break;
          case 'dark':
            _themePreference = ThemePreference.dark;
            break;
          default:
            _themePreference = ThemePreference.system;
        }
      } else {
        // Check for old boolean preference for backward compatibility
        final oldDarkMode = _prefs!.getBool('theme_preference');
        if (oldDarkMode != null) {
          _themePreference = oldDarkMode ? ThemePreference.dark : ThemePreference.light;
          // Migrate to new format
          _savePreference().catchError((error) {
            // Silent error handling
          });
        } else {
          _themePreference = ThemePreference.system;
        }
      }
      notifyListeners();
    } catch (e) {
      _themePreference = ThemePreference.system;
      notifyListeners();
    }
  }

  Future<void> _savePreference() async {
    if (_prefs == null) return;
    
    try {
      String themeString;
      switch (_themePreference) {
        case ThemePreference.system:
          themeString = 'system';
          break;
        case ThemePreference.light:
          themeString = 'light';
          break;
        case ThemePreference.dark:
          themeString = 'dark';
          break;
      }
      await _prefs!.setString(_themePreferenceKey, themeString);
    } catch (e) {
      // Silent error handling - don't rethrow
    }
  }

  Future<void> setThemePreference(ThemePreference preference) async {
    _themePreference = preference;
    await _savePreference();
    notifyListeners();
  }

  // Cycle through themes: System -> Light -> Dark -> System
  void toggleDarkMode() {
    try {
      switch (_themePreference) {
        case ThemePreference.system:
          setThemePreference(ThemePreference.light);
          break;
        case ThemePreference.light:
          setThemePreference(ThemePreference.dark);
          break;
        case ThemePreference.dark:
          setThemePreference(ThemePreference.system);
          break;
      }
    } catch (e) {
      // Fallback to system theme if toggle fails
      _themePreference = ThemePreference.system;
      notifyListeners();
    }
  }

  String get currentThemeDescription {
    try {
      switch (_themePreference) {
        case ThemePreference.system:
          return 'Follow system theme';
        case ThemePreference.light:
          return 'Always light theme';
        case ThemePreference.dark:
          return 'Always dark theme';
      }
    } catch (e) {
      return 'Follow system theme';
    }
  }

  IconData get currentThemeIcon {
    try {
      switch (_themePreference) {
        case ThemePreference.system:
          return Icons.brightness_auto;
        case ThemePreference.light:
          return Icons.light_mode_outlined;
        case ThemePreference.dark:
          return Icons.dark_mode_outlined;
      }
    } catch (e) {
      return Icons.brightness_auto;
    }
  }
}