import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/color_palette.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/contact_details_model.dart';
import '../utils/contact_details_helper.dart';
import '../configuration/client_config.dart';

import '../utils/app_logger.dart';
class SupportScreen extends StatefulWidget {
  const SupportScreen({super.key});

  @override
  State<SupportScreen> createState() => _SupportScreenState();
}

class _SupportScreenState extends State<SupportScreen> {
  ContactDetails? _contactDetails;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadContactDetails();
  }

  Future<void> _loadContactDetails() async {
    try {
      final contactDetails = await ContactDetailsHelper.getContactDetails();
      setState(() {
        _contactDetails = contactDetails;
        _isLoading = false;
      });
    } catch (e) {
      AppLogger.error('loading contact details: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _launchUrl(String url) async {
    if (!await launchUrl(Uri.parse(url))) {
      throw Exception('Could not launch $url');
    }
  }

  Widget _buildSocialMediaButton({
    required IconData icon,
    required String label,
    required String url,
    required Color color,
  }) {
    return Builder(
      builder: (context) {
        final isDark = Theme.of(context).brightness == Brightness.dark;
        return InkWell(
          onTap: () => _launchUrl(url),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isDark ? Theme.of(context).cardColor : Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                FaIcon(icon, size: 24, color: color),
                const SizedBox(height: 6),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: isDark ? Colors.white : Colors.grey[800],
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }
    );
  }

  Widget _buildContactInfo({
    required IconData icon,
    required String label,
    required String value,
    Color? color,
    VoidCallback? onTap,
  }) {
    return Builder(
      builder: (context) {
        final isDark = Theme.of(context).brightness == Brightness.dark;
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: InkWell(
            onTap: onTap,
            child: Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: isDark ? Theme.of(context).cardColor : Colors.white,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
              ),
              child: Row(
                children: [
                  Icon(icon, color: color ?? ColorPalette.primary, size: 24),
                  const SizedBox(width: 15),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          label,
                          style: TextStyle(
                            fontSize: 14,
                            color: isDark ? Colors.grey[300] : Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          value,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: isDark ? Colors.white : Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark 
          ? Theme.of(context).scaffoldBackgroundColor 
          : Colors.grey[200],
      appBar: AppBar(
        title: Text(
          'Support',
          style: TextStyle(
              color: ColorPalette.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        automaticallyImplyLeading: false,
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: ColorPalette.secondary,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Need Help?',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 10),
                  Text(
                    'Connect with us on our social media platforms or reach out through our contact information below.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Contact Information',
                    style: TextStyle(
                      color: ColorPalette.primary,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 15),
                  _buildContactInfo(
                    icon: Icons.location_on,
                    label: 'Address',
                    value: _contactDetails!.postalAddress,
                    color: ColorPalette.unselectedNavItemColor,
                  ),
                  _buildContactInfo(
                    icon: Icons.phone,
                    label: 'Phone',
                    value: _contactDetails!.phoneNumber,
                    onTap: () => _launchUrl('tel:${_contactDetails!.phoneNumber}'),
                    color: ColorPalette.unselectedNavItemColor,
                  ),
                  _buildContactInfo(
                    icon: Icons.email,
                    label: 'Email',
                    value: _contactDetails!.email,
                    onTap: () => _launchUrl('mailto:${_contactDetails!.email}'),
                    color: ColorPalette.unselectedNavItemColor,
                  ),
                  const SizedBox(height: 30),
                  Text(
                    'Social Media',
                    style: TextStyle(
                      color: ColorPalette.primary,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 15),
                  GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 2,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    childAspectRatio: 1.8,
                    children: [
                      // Check if the current client is Magadi Sacco
                      if (ClientThemeManager().currentClientId == '120') ...[
                        _buildSocialMediaButton(
                          icon: FontAwesomeIcons.linkedin,
                          label: 'LinkedIn',
                          url: 'https://www.linkedin.com/company/magadi-sacco-society-ltd',
                          color: const Color(0xFF0A66C2),
                        ),
                        _buildSocialMediaButton(
                          icon: FontAwesomeIcons.xTwitter,
                          label: 'X (Twitter)',
                          url: 'https://x.com/magadi_sacco',
                          color: Colors.black,
                        ),
                        _buildSocialMediaButton(
                          icon: FontAwesomeIcons.facebook,
                          label: 'Facebook',
                          url: 'https://www.facebook.com/magadisacco',
                          color: const Color(0xFF1877F2),
                        ),
                        _buildSocialMediaButton(
                          icon: FontAwesomeIcons.youtube,
                          label: 'YouTube',
                          url: 'https://www.youtube.com/@magadisacco5277',
                          color: const Color(0xFFFF0000),
                        ),
                      ] else ...[
                        _buildSocialMediaButton(
                          icon: FontAwesomeIcons.facebook,
                          label: 'Facebook',
                          url: _contactDetails!.facebook,
                          color: const Color(0xFF1877F2),
                        ),
                        _buildSocialMediaButton(
                          icon: FontAwesomeIcons.xTwitter,
                          label: 'X (Twitter)',
                          url: _contactDetails!.x,
                          color: Colors.black,
                        ),
                        _buildSocialMediaButton(
                          icon: FontAwesomeIcons.instagram,
                          label: 'Instagram',
                          url: _contactDetails!.instagram,
                          color: const Color(0xFFE4405F),
                        ),
                        _buildSocialMediaButton(
                          icon: FontAwesomeIcons.youtube,
                          label: 'YouTube',
                          url: _contactDetails!.youtube,
                          color: const Color(0xFFFF0000),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}