import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../utils/color_palette.dart';
import '../utils/services/api_service.dart';
import '../utils/services/api_endpoints.dart';
import '../utils/services/cryptographer.dart';
import '../utils/services/shared_preferences_helper.dart';
import '../utils/services/auth_service.dart';

class ChangePinScreen extends StatefulWidget {
  const ChangePinScreen({super.key});

  @override
  State<ChangePinScreen> createState() => _ChangePinScreenState();
}

class _ChangePinScreenState extends State<ChangePinScreen> {
  final TextEditingController _oldPinController = TextEditingController();
  final TextEditingController _newPinController = TextEditingController();
  final TextEditingController _confirmPinController = TextEditingController();
  
  bool _showOldPin = false;
  bool _showNewPin = false;
  bool _showConfirmPin = false;
  bool _isLoading = false;
  
  String? _oldPinError;
  String? _newPinError;
  String? _confirmPinError;
  
  final AuthService _authService = AuthService();
  final ApiService _apiService = ApiService();
  final AesEncryption _aes = AesEncryption();

  @override
  void dispose() {
    _oldPinController.dispose();
    _newPinController.dispose();
    _confirmPinController.dispose();
    super.dispose();
  }

  void _validateOldPin(String value) {
    setState(() {
      if (value.isEmpty) {
        _oldPinError = 'Please enter your current PIN';
      } else if (value.length != 4) {
        _oldPinError = 'PIN must be 4 digits';
      } else {
        _oldPinError = null;
      }
    });
  }

  void _validateNewPin(String value) {
    setState(() {
      if (value.isEmpty) {
        _newPinError = 'Please enter a new PIN';
      } else if (value.length != 4) {
        _newPinError = 'PIN must be 4 digits';
      } else if (value == _oldPinController.text) {
        _newPinError = 'New PIN must be different from current PIN';
      } else {
        _newPinError = null;
      }
    });
    
    // Re-validate confirm PIN if it has been entered
    if (_confirmPinController.text.isNotEmpty) {
      _validateConfirmPin(_confirmPinController.text);
    }
  }

  void _validateConfirmPin(String value) {
    setState(() {
      if (value.isEmpty) {
        _confirmPinError = 'Please confirm your new PIN';
      } else if (value.length != 4) {
        _confirmPinError = 'PIN must be 4 digits';
      } else if (value != _newPinController.text) {
        _confirmPinError = 'PINs do not match';
      } else {
        _confirmPinError = null;
      }
    });
  }

  bool _isFormValid() {
    return _oldPinController.text.length == 4 &&
           _newPinController.text.length == 4 &&
           _confirmPinController.text.length == 4 &&
           _oldPinError == null &&
           _newPinError == null &&
           _confirmPinError == null;
  }

  Future<void> _changePin() async {
    if (!_isFormValid()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // First verify the old PIN
      bool oldPinValid = await _verifyOldPin();
      if (!oldPinValid) {
        _showErrorSnackBar('Current PIN is incorrect');
        return;
      }
      
      // Proceed with PIN change
      String result = await _performPinChange();
      
      if (mounted) {
        if (result.toLowerCase().contains('success') || result.contains('PIN changed')) {
          _showSuccessDialog();
        } else {
          _showErrorSnackBar(result);
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to change PIN: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  Future<bool> _verifyOldPin() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? phoneNumber = prefs.getString('msisdn');
      
      if (phoneNumber == null) {
        throw Exception('Session expired. Please login again.');
      }
      
      // Verify old PIN using AuthService
      if (!mounted) return false;
      AuthResult result = await _authService.verifyPin(phoneNumber, _oldPinController.text, context);
      return result.isSuccess;
    } catch (e) {
      debugPrint('Error verifying old PIN: $e');
      return false;
    }
  }
  
  Future<String> _performPinChange() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? secretKey = prefs.getString('_tajemnica');
      String? phoneNumber = prefs.getString('msisdn');
      String? deviceId = await _apiService.getDeviceId();
      String? clientId = await SharedPreferencesHelper.getClientId();
      String? spCustId = await SharedPreferencesHelper.getCustId();
      
      if (secretKey == null || phoneNumber == null || spCustId == null) {
        throw Exception('Session expired. Please login again.');
      }
      
      // Prepare request body for PIN change
      var requestBody = {
        "header": {
          "categoryName": "Spotcash_App",
          "clientId": clientId,
          "msisdn": _aes.encryptWithBase64Key(phoneNumber, secretKey),
          "imei": deviceId,
        },
        "requestData": {
          "cmp": _aes.encryptWithBase64Key(_oldPinController.text, secretKey),
          "custid": _aes.encryptWithBase64Key(spCustId, secretKey),
          "customerType": 'CUSTOMER',
          "newPin": _aes.encryptWithBase64Key(_newPinController.text, secretKey),
          "confirmPin": _aes.encryptWithBase64Key(_confirmPinController.text, secretKey),
          "txnType": 'CP'
        }
      };
      
      // Send API request
      if (!mounted) throw Exception('Context not available');
      var response = await _apiService.postRequest(
        ApiEndpoints.transaction,
        requestBody,
        context: context,
        isAuth: true,
        navigateToLoginCallback: () {
          if (mounted) {
            Navigator.pushReplacementNamed(context, '/login');
          }
        }
      );
      
      if (response != null && response['hashedBody'] != null) {
        String decryptedResponse = _aes.decryptWithBase64Key(response['hashedBody'], secretKey);
        Map<String, dynamic> decodedResponse = jsonDecode(decryptedResponse);
        
        // Check response status
        if (decodedResponse['header'] != null && decodedResponse['header']['sd'] != null) {
          return decodedResponse['header']['sd'];
        } else if (decodedResponse['responseCode'] == '00') {
          return 'PIN changed successfully!';
        } else {
          return decodedResponse['responseDescription'] ?? 'Failed to change PIN';
        }
      } else {
        return 'No valid response received from server';
      }
    } catch (e) {
      debugPrint('Error changing PIN: $e');
      throw Exception('Failed to change PIN. Please try again.');
    }
  }
  
  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
    }
  }

  void _showSuccessDialog() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with gradient
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.green.shade400,
                      Colors.green.shade600,
                    ],
                  ),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(24),
                  ),
                ),
                child: Column(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Icon(
                        Icons.check_circle_rounded,
                        size: 30,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'PIN Changed Successfully',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Content
              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    Text(
                      'Your PIN has been changed successfully!',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: isDarkMode ? Colors.white : Colors.grey[800],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Please use your new PIN for future login and transactions.',
                      style: TextStyle(
                        fontSize: 14,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                        height: 1.4,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 32),
                    
                    // OK button
                    SizedBox(
                      width: double.infinity,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.green.shade400,
                              Colors.green.shade600,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.green.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: TextButton(
                          onPressed: () {
                            Navigator.pop(context); // Close dialog
                            Navigator.pop(context); // Go back to profile
                          },
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: Text(
                            'OK',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPinField({
    required String label,
    required TextEditingController controller,
    required bool obscureText,
    required VoidCallback onToggleVisibility,
    required Function(String) onChanged,
    String? errorText,
    IconData? prefixIcon,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: isDark ? Colors.white : ColorPalette.primary,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          obscureText: obscureText,
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(4),
          ],
          maxLength: 4,
          onChanged: onChanged,
          style: TextStyle(
            color: isDark ? Colors.white : Colors.black87,
            fontSize: 16,
            letterSpacing: obscureText ? 8 : 2,
          ),
          decoration: InputDecoration(
            hintText: '****',
            filled: true,
            fillColor: isDark ? Colors.grey[800] : Colors.white,
            counterText: '',
            errorText: errorText,
            prefixIcon: Icon(
              prefixIcon ?? Icons.lock_outline,
              color: ColorPalette.primary,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                obscureText ? Icons.visibility_off : Icons.visibility,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
              onPressed: onToggleVisibility,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: errorText != null 
                    ? Colors.red 
                    : (isDark ? Colors.grey[700]! : Colors.grey[300]!),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: errorText != null 
                    ? Colors.red 
                    : (isDark ? Colors.grey[700]! : Colors.grey[300]!),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: errorText != null 
                    ? Colors.red 
                    : ColorPalette.secondary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Colors.red,
                width: 1,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Colors.red,
                width: 2,
              ),
            ),
            hintStyle: TextStyle(
              color: isDark ? Colors.grey[400] : Colors.grey[600],
              letterSpacing: 8,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark 
          ? theme.scaffoldBackgroundColor 
          : Colors.grey[50],
      body: SafeArea(
        child: Column(
          children: [
            // Modern Header with Gradient
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    ColorPalette.secondary,
                    ColorPalette.secondary,
                  ],
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.arrow_back,
                          color: Colors.white,
                          size: 20,
                        ),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      'Change PIN',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Form Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Info Card
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: isDark ? Colors.grey[850] : Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: isDark 
                                ? Colors.black.withValues(alpha: 0.3)
                                : Colors.grey.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: ColorPalette.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.info_outline,
                              color: ColorPalette.primary,
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Security Information',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: theme.textTheme.bodyLarge?.color,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Enter your current PIN, then choose a new 4-digit PIN that\'s different from your current one.',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // PIN Fields
                    _buildPinField(
                      label: 'Current PIN',
                      controller: _oldPinController,
                      obscureText: !_showOldPin,
                      onToggleVisibility: () => setState(() => _showOldPin = !_showOldPin),
                      onChanged: _validateOldPin,
                      errorText: _oldPinError,
                      prefixIcon: Icons.lock_outline,
                    ),
                    
                    const SizedBox(height: 24),
                    
                    _buildPinField(
                      label: 'New PIN',
                      controller: _newPinController,
                      obscureText: !_showNewPin,
                      onToggleVisibility: () => setState(() => _showNewPin = !_showNewPin),
                      onChanged: _validateNewPin,
                      errorText: _newPinError,
                      prefixIcon: Icons.lock_reset,
                    ),
                    
                    const SizedBox(height: 24),
                    
                    _buildPinField(
                      label: 'Confirm New PIN',
                      controller: _confirmPinController,
                      obscureText: !_showConfirmPin,
                      onToggleVisibility: () => setState(() => _showConfirmPin = !_showConfirmPin),
                      onChanged: _validateConfirmPin,
                      errorText: _confirmPinError,
                      prefixIcon: Icons.lock_clock,
                    ),
                    
                    const SizedBox(height: 40),
                    
                    // Change PIN Button
                    SizedBox(
                      width: double.infinity,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: _isFormValid() && !_isLoading
                              ? LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    ColorPalette.secondary,
                                    ColorPalette.secondary,
                                  ],
                                )
                              : null,
                          color: !_isFormValid() || _isLoading
                              ? (isDark ? Colors.grey[700] : Colors.grey[300])
                              : null,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: _isFormValid() && !_isLoading
                              ? [
                                  BoxShadow(
                                    color: ColorPalette.secondary.withValues(alpha: 0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ]
                              : null,
                        ),
                        child: TextButton(
                          onPressed: _isFormValid() && !_isLoading ? _changePin : null,
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : Text(
                                  'Change PIN',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}