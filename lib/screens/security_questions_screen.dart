import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../utils/services/api_endpoints.dart';
import '../utils/services/api_service.dart';
import '../utils/services/cryptographer.dart';
import '../utils/services/shared_preferences_helper.dart';
import '../utils/color_palette.dart';
import 'email_confirmation_screen.dart';
import 'login_screen.dart';

class SecurityQuestionsScreen extends StatefulWidget {
  const SecurityQuestionsScreen({super.key});

  @override
  State<SecurityQuestionsScreen> createState() => _SecurityQuestionsScreenState();
}

class _SecurityQuestionsScreenState extends State<SecurityQuestionsScreen> {
  final Map<int, TextEditingController> answerControllers = {};
  List<Map<String, dynamic>> questions = [];
  bool isLoading = false;
  bool isFetchingQuestions = true;

  @override
  void initState() {
    super.initState();
    _fetchCustomerSecurityQuestions();
  }

  @override
  void dispose() {
    // Dispose all controllers
    for (var controller in answerControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  // Fetch customer security questions dynamically
  Future<void> _fetchCustomerSecurityQuestions() async {
    setState(() {
      isFetchingQuestions = true;
    });

    ApiService apiService = ApiService();
    AesEncryption aes = AesEncryption();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? secretKey = prefs.getString('_tajemnica');
    String? phoneNumber = prefs.getString('msisdn');
    var clientId = await SharedPreferencesHelper.getClientId();

    if (phoneNumber == null || secretKey == null) {
      _showError('Session expired. Please login again.');
      _navigateToLogin();
      return;
    }

    var requestBody = {
      'msisdn': phoneNumber,
      'clientId': clientId,
    };

    String plainTextRequestBody = jsonEncode(requestBody);
    String encryptedString = aes.encryptWithBase64Key(plainTextRequestBody, secretKey);
    var encryptedRequest = {'hashedBody': encryptedString};

    try {
      var response = await apiService.postRequest(
        ApiEndpoints.fetchCustomerSecurityQuestions,
        encryptedRequest,
      );

      if (response != null && response.containsKey('hashedBody')) {
        String hashedBody = response['hashedBody'];
        String decrypted = aes.decryptWithBase64Key(hashedBody, secretKey);
        Map<String, dynamic> decryptedResponse = jsonDecode(decrypted);

        if (decryptedResponse['responseCode'] == '00') {
          setState(() {
            questions = List<Map<String, dynamic>>.from(
                decryptedResponse['responseBody']);
            for (var question in questions) {
              answerControllers[question['id']] = TextEditingController();
            }
          });
        } else {
          _showError(
              'Error fetching questions: ${decryptedResponse['responseDescription']}');
        }
      } else {
        _showError('No valid response received from the server.');
      }
    } catch (e) {
      _showError('Error fetching questions: $e');
    } finally {
      setState(() {
        isFetchingQuestions = false;
      });
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.fromLTRB(16, 0, 16, 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          backgroundColor: ColorPalette.error,
        ),
      );
    }
  }

  void _navigateToLogin() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => LoginScreen()),
    );
  }

  Future<void> _submitAnswers() async {
    // Check if all questions have been answered
    bool allAnswered = true;
    for (var question in questions) {
      if (answerControllers[question['id']]!.text.trim().isEmpty) {
        allAnswered = false;
        break;
      }
    }

    if (!allAnswered) {
      _showError('Please answer all security questions');
      return;
    }

    setState(() {
      isLoading = true;
    });

    ApiService apiService = ApiService();
    AesEncryption aes = AesEncryption();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? secretKey = prefs.getString('_tajemnica');
    var clientId = await SharedPreferencesHelper.getClientId();
    String? phoneNumber = prefs.getString('msisdn');

    if (phoneNumber == null || secretKey == null) {
      _showError('Session expired. Please login again.');
      _navigateToLogin();
      return;
    }

    // Trim answers and prepare them for submission
    List<Map<String, String>> answers = questions.map((question) {
      String trimmedAnswer = answerControllers[question['id']]!.text.trim();
      return {
        'questionId': question['id'].toString(),
        'answer': trimmedAnswer,
      };
    }).toList();

    var requestBody = {
      'clientId': clientId,
      'msisdn': phoneNumber,
      'answers': answers,
    };

    try {
      String plainTextRequestBody = jsonEncode(requestBody);
      String encryptedString = aes.encryptWithBase64Key(plainTextRequestBody, secretKey);

      var encryptedRequestBody = {'hashedBody': encryptedString};

      var response = await apiService.postRequest(
        ApiEndpoints.verifySecurityQuestion,
        encryptedRequestBody,
        context: context,
        isAuth: true,
        navigateToLoginCallback: () {
          Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => LoginScreen())
          );
        }
      );

      setState(() {
        isLoading = false;
      });

      if (response != null && response.containsKey('hashedBody')) {
        String hashedBody = response['hashedBody'];
        String decryptedResponseString = aes.decryptWithBase64Key(hashedBody, secretKey);
        Map<String, dynamic> decryptedResponse = jsonDecode(decryptedResponseString);

        if (decryptedResponse['responseCode'] == '00') {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => EmailConfirmationScreen()),
          );
        } else {
          _showError(decryptedResponse['responseDescription'] ?? 'Verification failed');
        }
      } else {
        _showError('No valid response received from the server.');
      }
    } catch (e) {
      _showError('Error verifying answers: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  Widget _buildQuestionCard(Map<String, dynamic> question) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: isDark ? ColorPalette.surface : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark ? ColorPalette.surface : ColorPalette.primary.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: ColorPalette.primary.withValues(alpha: 0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Question header with icon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: ColorPalette.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.help_outline,
                    color: ColorPalette.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Security Question',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: ColorPalette.primary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Question text
            Text(
              question['question'] ?? '',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: ColorPalette.textPrimary,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 16),
            
            // Answer input field
            TextField(
              controller: answerControllers[question['id']],
              style: TextStyle(
                color: ColorPalette.textPrimary,
                fontSize: 16,
              ),
              decoration: InputDecoration(
                hintText: 'Enter your answer',
                hintStyle: TextStyle(
                  color: ColorPalette.textSecondary,
                ),
                filled: true,
                fillColor: isDark ? ColorPalette.background : ColorPalette.surface,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: ColorPalette.primary, width: 2),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? ColorPalette.background : Colors.grey[50],
      appBar: AppBar(
        backgroundColor: isDark ? ColorPalette.surface : Colors.white,
        elevation: 0,
        centerTitle: true,
        title: Text(
          'Security Questions',
          style: TextStyle(
            color: ColorPalette.textPrimary,
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ColorPalette.textPrimary,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: isFetchingQuestions
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(ColorPalette.primary),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Loading security questions...',
                    style: TextStyle(
                      color: ColorPalette.textSecondary,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            )
          : questions.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.warning_amber_rounded,
                        size: 64,
                        color: ColorPalette.warning,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No security questions found',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: ColorPalette.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Please contact support for assistance',
                        style: TextStyle(
                          fontSize: 14,
                          color: ColorPalette.textSecondary,
                        ),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    // Header section
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(20),
                      margin: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            ColorPalette.primary,
                            ColorPalette.primary.withValues(alpha: 0.8),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: ColorPalette.primary.withValues(alpha: 0.3),
                            blurRadius: 12,
                            offset: const Offset(0, 6),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.security,
                            color: Colors.white,
                            size: 32,
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'Answer Security Questions',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Please answer all questions to proceed with PIN reset',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.9),
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    // Questions list
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: questions.length,
                        itemBuilder: (context, index) {
                          return _buildQuestionCard(questions[index]);
                        },
                      ),
                    ),

                    // Submit button
                    Container(
                      padding: const EdgeInsets.all(16),
                      child: SizedBox(
                        width: double.infinity,
                        height: 52,
                        child: ElevatedButton(
                          onPressed: isLoading ? null : _submitAnswers,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorPalette.primary,
                            disabledBackgroundColor: ColorPalette.primary.withValues(alpha: 0.5),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            elevation: 0,
                          ),
                          child: isLoading
                              ? SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    strokeWidth: 2,
                                  ),
                                )
                              : Text(
                                  'Verify Answers',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                      ),
                    ),
                  ],
                ),
    );
  }
} 