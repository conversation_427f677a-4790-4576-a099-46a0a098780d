import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../utils/services/api_endpoints.dart';
import '../utils/services/api_service.dart';
import '../utils/services/cryptographer.dart';
import '../utils/services/shared_preferences_helper.dart';
import '../utils/color_palette.dart';
import '../transactions/screens/dashboard_screen.dart';
import 'login_screen.dart';

class SecurityQuestionsSetupScreen extends StatefulWidget {
  const SecurityQuestionsSetupScreen({super.key});

  @override
  State<SecurityQuestionsSetupScreen> createState() => _SecurityQuestionsSetupScreenState();
}

class _SecurityQuestionsSetupScreenState extends State<SecurityQuestionsSetupScreen> {
  List<Map<String, dynamic>> questions = [];
  List<Map<String, dynamic>> selectedQuestions = [];
  bool isLoading = false;
  int requiredQuestions = 3; // Default value
  int _currentStep = 0; // 0 = selection, 1 = answering

  @override
  void initState() {
    super.initState();
    _fetchSecurityQuestions();
  }

  @override
  void dispose() {
    // Dispose all controllers
    for (var question in selectedQuestions) {
      if (question['answer'] is TextEditingController) {
        (question['answer'] as TextEditingController).dispose();
      }
    }
    super.dispose();
  }

  Future<void> _fetchSecurityQuestions() async {
    setState(() {
      isLoading = true;
    });

    ApiService apiService = ApiService();
    AesEncryption aes = AesEncryption();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? secretKey = prefs.getString('_tajemnica');
    String? phoneNumber = prefs.getString('msisdn');
    var clientId = await SharedPreferencesHelper.getClientId();

    if (phoneNumber == null || secretKey == null) {
      _showError('Session expired. Please login again.');
      _navigateToLogin();
      return;
    }

    var requestBody = {
      'msisdn': phoneNumber,
      'clientId': clientId,
    };

    String plainTextRequestBody = jsonEncode(requestBody);
    String encryptedString = aes.encryptWithBase64Key(plainTextRequestBody, secretKey);
    var encryptedRequest = {'hashedBody': encryptedString};

    try {
      var response = await apiService.postRequest(
        ApiEndpoints.fetchSecurityQuestions,
        encryptedRequest,
      );

      if (response != null && response.containsKey('hashedBody')) {
        String hashedBody = response['hashedBody'];
        String decrypted = aes.decryptWithBase64Key(hashedBody, secretKey);
        Map<String, dynamic> decryptedResponse = jsonDecode(decrypted);

        if (decryptedResponse['responseCode'] == '00') {
          setState(() {
            questions = List<Map<String, dynamic>>.from(
                decryptedResponse['responseBody']);
          });
        } else {
          _showError('Error fetching questions: ${decryptedResponse['responseDescription']}');
        }
      } else {
        _showError('No valid response received from the server.');
      }
    } catch (e) {
      _showError('Error fetching security questions: $e');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.fromLTRB(16, 0, 16, 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          backgroundColor: ColorPalette.error,
        ),
      );
    }
  }

  void _navigateToLogin() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => LoginScreen()),
    );
  }

  void _toggleQuestionSelection(Map<String, dynamic> question) {
    final int questionIndex = selectedQuestions.indexWhere((q) => q['id'] == question['id']);

    setState(() {
      if (questionIndex >= 0) {
        // Question is already selected, remove it and dispose controller
        if (selectedQuestions[questionIndex]['answer'] is TextEditingController) {
          (selectedQuestions[questionIndex]['answer'] as TextEditingController).dispose();
        }
        selectedQuestions.removeAt(questionIndex);
      } else if (selectedQuestions.length < requiredQuestions) {
        // Question not selected and we haven't reached max, add it
        selectedQuestions.add({
          'id': question['id'],
          'question': question['question'],
          'answer': TextEditingController(),
        });
      }
    });
  }

  bool _isQuestionSelected(int questionId) {
    return selectedQuestions.any((q) => q['id'] == questionId);
  }

  bool _isQuestionAnswered(int index) {
    return selectedQuestions[index]['answer'].text.trim().isNotEmpty;
  }

  bool _validateAnswers() {
    return selectedQuestions.every((q) => q['answer'].text.trim().isNotEmpty);
  }

  int _answeredQuestionsCount() {
    return selectedQuestions
        .where((q) => q['answer'].text.trim().isNotEmpty)
        .length;
  }

  void _goToAnswerStep() {
    if (selectedQuestions.length == requiredQuestions) {
      setState(() {
        _currentStep = 1;
      });
    } else {
      _showError('Please select $requiredQuestions security questions');
    }
  }

  void _goBackToSelection() {
    setState(() {
      _currentStep = 0;
    });
  }

  Future<void> _submitSecurityQuestions() async {
    if (!_validateAnswers()) {
      _showError('Please answer all selected questions');
      return;
    }

    setState(() {
      isLoading = true;
    });

    ApiService apiService = ApiService();
    AesEncryption aes = AesEncryption();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? secretKey = prefs.getString('_tajemnica');
    String? phoneNumber = prefs.getString('msisdn');
    var clientId = await SharedPreferencesHelper.getClientId();

    if (phoneNumber == null || secretKey == null) {
      _showError('Session expired. Please login again.');
      _navigateToLogin();
      return;
    }

    List<Map<String, dynamic>> payload = selectedQuestions.map((q) {
      return {
        'questionId': q['id'],
        'answer': q['answer'].text.trim(),
      };
    }).toList();

    var requestBody = {
      'msisdn': phoneNumber,
      'clientId': clientId,
      'securityQuestions': payload,
    };

    String plainTextRequestBody = jsonEncode(requestBody);
    String encryptedString = aes.encryptWithBase64Key(plainTextRequestBody, secretKey);
    var encryptedRequest = {'hashedBody': encryptedString};

    try {
      var response = await apiService.postRequest(
        ApiEndpoints.addSecurityQuestions,
        encryptedRequest,
      );

      if (response != null && response.containsKey('hashedBody')) {
        String hashedBody = response['hashedBody'];
        String decrypted = aes.decryptWithBase64Key(hashedBody, secretKey);
        Map<String, dynamic> decryptedResponse = jsonDecode(decrypted);

        if (decryptedResponse['responseCode'] == '00') {
          if (mounted) {
            // Show success message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Security questions set up successfully!'),
                backgroundColor: ColorPalette.success,
                behavior: SnackBarBehavior.floating,
              ),
            );
            
            // Navigate to dashboard after successful setup
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (context) => const DashboardScreen(
                  userName: 'User', // You might want to get the actual user name
                ),
              ),
            );
          }
        } else {
          _showError('Error: ${decryptedResponse['responseDescription']}');
        }
      } else {
        _showError('No valid response received from the server.');
      }
    } catch (e) {
      _showError('Error submitting security questions: $e');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Widget _buildProgressIndicator() {
    final int progress = _currentStep == 0
        ? selectedQuestions.length
        : requiredQuestions + _answeredQuestionsCount();
    final int total = requiredQuestions * 2; // Selection + Answering

    return Container(
      color: ColorPalette.primary,
      padding: const EdgeInsets.fromLTRB(20, 12, 20, 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _currentStep == 0
                ? 'Select Security Questions'
                : 'Answer Your Questions',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: LinearProgressIndicator(
              value: progress / total,
              minHeight: 6,
              backgroundColor: Colors.white24,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectionStep() {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Card(
              elevation: 0,
              color: Colors.blue.withValues(alpha: 0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[700], size: 24),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'These questions help secure your account. Choose $requiredQuestions questions that only you would know the answer to.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue[800],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final question = questions[index];
              final isSelected = _isQuestionSelected(question['id']);
              
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                child: Card(
                  elevation: 0,
                  color: isSelected ? ColorPalette.primary.withValues(alpha: 0.1) : Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(
                      color: isSelected ? ColorPalette.primary : Colors.grey[300]!,
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: InkWell(
                    onTap: () => _toggleQuestionSelection(question),
                    borderRadius: BorderRadius.circular(12),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: isSelected ? ColorPalette.primary : Colors.transparent,
                              border: Border.all(
                                color: isSelected ? ColorPalette.primary : Colors.grey[400]!,
                                width: 2,
                              ),
                            ),
                            child: isSelected
                                ? Icon(Icons.check, color: Colors.white, size: 16)
                                : null,
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Text(
                              question['question'] ?? '',
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                                color: isSelected ? ColorPalette.primary : ColorPalette.textPrimary,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
            childCount: questions.length,
          ),
        ),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: selectedQuestions.length == requiredQuestions ? _goToAnswerStep : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorPalette.primary,
                      disabledBackgroundColor: Colors.grey[400],
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Continue (${selectedQuestions.length}/$requiredQuestions)',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAnswerStep() {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Card(
              elevation: 0,
              color: Colors.green.withValues(alpha: 0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.security, color: Colors.green[700], size: 24),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'Now provide answers to your selected questions. Make sure to remember your answers!',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.green[800],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final question = selectedQuestions[index];
              final isAnswered = _isQuestionAnswered(index);
              
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Card(
                  elevation: 0,
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(
                      color: isAnswered ? Colors.green : Colors.grey[300]!,
                      width: isAnswered ? 2 : 1,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: ColorPalette.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Text(
                                '${index + 1}',
                                style: TextStyle(
                                  color: ColorPalette.primary,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            if (isAnswered)
                              Icon(Icons.check_circle, color: Colors.green, size: 20),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          question['question'] ?? '',
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                            color: ColorPalette.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 12),
                        TextField(
                          controller: question['answer'],
                          style: TextStyle(
                            color: ColorPalette.textPrimary,
                            fontSize: 16,
                          ),
                          decoration: InputDecoration(
                            hintText: 'Enter your answer',
                            hintStyle: TextStyle(
                              color: ColorPalette.textSecondary,
                            ),
                            filled: true,
                            fillColor: Colors.grey[50],
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide.none,
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: ColorPalette.primary, width: 2),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          ),
                          onChanged: (value) => setState(() {}),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
            childCount: selectedQuestions.length,
          ),
        ),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: OutlinedButton(
                        onPressed: _goBackToSelection,
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: ColorPalette.primary),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'Back',
                          style: TextStyle(
                            color: ColorPalette.primary,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      flex: 2,
                      child: ElevatedButton(
                        onPressed: (_validateAnswers() && !isLoading) ? _submitSecurityQuestions : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorPalette.primary,
                          disabledBackgroundColor: Colors.grey[400],
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: isLoading
                            ? SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                'Complete Setup',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? ColorPalette.background : Colors.grey[50],
      appBar: AppBar(
        backgroundColor: ColorPalette.primary,
        elevation: 0,
        centerTitle: true,
        title: Text(
          'Security Questions Setup',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            _currentStep == 0 ? Icons.close : Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: _currentStep == 0
              ? () => Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(builder: (context) => LoginScreen()),
                  )
              : _goBackToSelection,
        ),
      ),
      body: isLoading && questions.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(ColorPalette.primary),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Loading security questions...',
                    style: TextStyle(
                      color: ColorPalette.textSecondary,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            )
          : questions.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.warning_amber_rounded,
                        size: 64,
                        color: ColorPalette.warning,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No security questions available',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: ColorPalette.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Please contact support for assistance',
                        style: TextStyle(
                          fontSize: 14,
                          color: ColorPalette.textSecondary,
                        ),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    _buildProgressIndicator(),
                    Expanded(
                      child: _currentStep == 0
                          ? _buildSelectionStep()
                          : _buildAnswerStep(),
                    ),
                  ],
                ),
    );
  }
} 