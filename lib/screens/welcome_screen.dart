import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'login_screen.dart';
import 'registration_screens.dart';
import '../utils/color_palette.dart';
import '../configuration/client_config.dart';
import '../utils/theme_helper.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> with TickerProviderStateMixin {
  late AnimationController _coinController;
  late AnimationController _pulseController;
  late Animation<double> _coinAnimation;
  late Animation<double> _pulseAnimation;
  
  int _currentCarouselIndex = 0;

  List<CarouselItem> get carouselItems {
    final clientName = ClientThemeManager().currentClientConfig.displayName;
    
    return [
      CarouselItem(
        title: "Ready to change\nthe way you\nMONEY?",
        subtitle: "Experience seamless banking with $clientName",
        iconPath: "assets/images/carousel_money.svg",
        color: ColorPalette.primary,
        backgroundColor: ColorPalette.secondary,
      ),
      CarouselItem(
        title: "Send money\ninstantly with\nM-PESA",
        subtitle: "Fast, secure, and convenient mobile money transfers with $clientName",
        iconPath: "assets/images/carousel_mpesa.svg",
        color: ColorPalette.secondary,
        backgroundColor: ColorPalette.primary,
      ),
      CarouselItem(
        title: "Loans made\neasy for\nKENYANS",
        subtitle: "Quick loan approvals with competitive rates from $clientName",
        iconPath: "assets/images/carousel_loans.svg",
        color: ColorPalette.primary,
        backgroundColor: ColorPalette.unselectedNavItemColor,
      ),
      CarouselItem(
        title: "Save smartly\nfor your\nFUTURE",
        subtitle: "Build wealth with $clientName's tailored savings products",
        iconPath: "assets/images/carousel_savings.svg",
        color: ColorPalette.secondary,
        backgroundColor: ColorPalette.primary,
      ),
    ];
  }

  @override
  void initState() {
    super.initState();
    
    // Floating coin animation
    _coinController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);
    
    _coinAnimation = Tween<double>(
      begin: -20.0,
      end: 20.0,
    ).animate(CurvedAnimation(
      parent: _coinController,
      curve: Curves.easeInOut,
    ));

    // Pulse animation for buttons
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _coinController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Theme.of(context);

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          color: carouselItems[_currentCarouselIndex].backgroundColor,
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // Main content
              Column(
                children: [
                  // Progress bar at the top
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),
                    child: Column(
                      children: [
                        // Progress bar container
                        Container(
                          height: 4,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(2),
                          ),
                          child: Stack(
                            children: [
                              // Progress fill
                              AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                width: MediaQuery.of(context).size.width * 
                                       ((_currentCarouselIndex + 1) / carouselItems.length) * 
                                       ((MediaQuery.of(context).size.width - 48) / MediaQuery.of(context).size.width),
                                height: 4,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                            ],
                          ),
                        ).animate().fadeIn(duration: 800.ms, delay: 200.ms),
                      ],
                    ),
                  ),
                  
                  // Carousel section
                  Expanded(
                    child: CarouselSlider.builder(
                      itemCount: carouselItems.length,
                      itemBuilder: (context, index, realIndex) {
                        return _buildCarouselItem(carouselItems[index], index);
                      },
                      options: CarouselOptions(
                        height: double.infinity,
                        viewportFraction: 1.0,
                        enableInfiniteScroll: true,
                        autoPlay: true,
                        autoPlayInterval: const Duration(seconds: 4),
                        autoPlayAnimationDuration: const Duration(milliseconds: 800),
                        onPageChanged: (index, reason) {
                          setState(() {
                            _currentCarouselIndex = index;
                          });
                        },
                      ),
                    ),
                  ),
                  
                  // Action buttons
                  Padding(
                    padding: const EdgeInsets.fromLTRB(24.0, 8.0, 24.0, 24.0),
                    child: Column(
                      children: [
                        // Log In Button
                        AnimatedBuilder(
                          animation: _pulseAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _pulseAnimation.value,
                              child: Container(
                                width: double.infinity,
                                height: 50,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(25),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.2),
                                      blurRadius: 15,
                                      offset: const Offset(0, 8),
                                    ),
                                  ],
                                ),
                                child: TextButton(
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => const LoginScreen(),
                                      ),
                                    );
                                  },
                                  style: TextButton.styleFrom(
                                    backgroundColor: Colors.transparent,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(25),
                                    ),
                                  ),
                                  child: Text(
                                    'Log in',
                                    style: TextStyle(
                                      color: ColorPalette.unselectedNavItemColor,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ).animate().fadeIn(duration: 1000.ms, delay: 1200.ms).slideY(begin: 0.3),
                        
                        const SizedBox(height: 12),
                        
                        // Sign Up Button
                        Container(
                          width: double.infinity,
                          height: 50,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.white, width: 2),
                            borderRadius: BorderRadius.circular(25),
                          ),
                          child: TextButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const RegistrationScreen(),
                                ),
                              );
                            },
                            style: TextButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25),
                              ),
                            ),
                            child: Text(
                              'Register',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ).animate().fadeIn(duration: 1000.ms, delay: 1400.ms).slideY(begin: 0.3),
                        
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCarouselItem(CarouselItem item, int index) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    
    // Make SVG size responsive to screen size
    final svgSize = (screenHeight * 0.25).clamp(150.0, 200.0);
    final svgInnerSize = svgSize * 0.9;
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: SingleChildScrollView(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minHeight: screenHeight * 0.6, // Ensure minimum height for content
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Animated SVG illustration - made responsive
              AnimatedBuilder(
                animation: _coinAnimation,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(0, _coinAnimation.value),
                    child: SizedBox(
                      width: svgSize,
                      height: svgSize,
                      child: Center(
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            SvgPicture.asset(
                              item.iconPath,
                              width: svgInnerSize,
                              height: svgInnerSize,
                              fit: BoxFit.contain,
                            ),
                            if (index == 0) // Show logo only on the first carousel item
                              Container(
                                width: svgSize * 0.35,
                                height: svgSize * 0.35,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      Colors.white.withValues(alpha: 0.9),
                                      Colors.white.withValues(alpha: 0.7),
                                    ],
                                  ),
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.4),
                                    width: 2,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.1),
                                      blurRadius: 15,
                                      offset: const Offset(0, 5),
                                    ),
                                  ],
                                ),
                                child: ClipOval(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                    ),
                                    padding: const EdgeInsets.all(6.0),
                                    child: ThemeHelper.getClientLogo(
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ).animate().scale(duration: 800.ms, delay: Duration(milliseconds: index * 200)),
              
              SizedBox(height: screenHeight * 0.03), // Responsive spacing
              
              // Title text with better styling - made responsive
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  item.title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: (screenWidth * 0.07).clamp(24.0, 28.0), // Responsive font size
                    fontWeight: FontWeight.w800,
                    height: 1.0,
                    letterSpacing: -0.5,
                    shadows: [
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        offset: const Offset(0, 2),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                ),
              ).animate().fadeIn(duration: 1000.ms, delay: Duration(milliseconds: 600 + index * 200)).slideY(begin: 0.3),
              
              SizedBox(height: screenHeight * 0.02), // Responsive spacing
              
              // Subtitle with enhanced styling - made responsive
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  item.subtitle,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: (screenWidth * 0.04).clamp(14.0, 16.0), // Responsive font size
                    fontWeight: FontWeight.w400,
                    height: 1.3,
                    letterSpacing: 0.2,
                    shadows: [
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        offset: const Offset(0, 1),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                ),
              ).animate().fadeIn(duration: 1000.ms, delay: Duration(milliseconds: 800 + index * 200)).slideY(begin: 0.3),
              
              SizedBox(height: screenHeight * 0.025), // Responsive spacing
              
              // Feature highlight badge - made responsive
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Text(
                  _getFeatureText(index),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: (screenWidth * 0.03).clamp(10.0, 12.0), // Responsive font size
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
              ).animate().fadeIn(duration: 1000.ms, delay: Duration(milliseconds: 1000 + index * 200)).scale(),
            ],
          ),
        ),
      ),
    );
  }

  String _getFeatureText(int index) {
    final clientName = ClientThemeManager().currentClientConfig.displayName.toUpperCase();
    
    switch (index) {
      case 0:
        return "🚀 MODERN BANKING";
      case 1:
        return "⚡ INSTANT TRANSFERS";
      case 2:
        return "💰 QUICK APPROVALS";
      case 3:
        return "📈 WEALTH BUILDING";
      default:
        return "✨ $clientName";
    }
  }
}

class CarouselItem {
  final String title;
  final String subtitle;
  final String iconPath;
  final Color color;
  final Color backgroundColor;

  CarouselItem({
    required this.title,
    required this.subtitle,
    required this.iconPath,
    required this.color,
    required this.backgroundColor,
  });
} 