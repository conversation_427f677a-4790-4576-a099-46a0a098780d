import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/services/api_endpoints.dart';
import '../utils/services/api_service.dart';
import '../utils/services/cryptographer.dart';
import '../utils/services/shared_preferences_helper.dart';
import '../utils/color_palette.dart';
import 'login_screen.dart';

class ForgotPinChangeScreen extends StatefulWidget {
  const ForgotPinChangeScreen({super.key});

  @override
  State<ForgotPinChangeScreen> createState() => _ForgotPinChangeScreenState();
}

class _ForgotPinChangeScreenState extends State<ForgotPinChangeScreen> {
  final TextEditingController tempPinController = TextEditingController();
  final TextEditingController newPinController = TextEditingController();
  final TextEditingController confirmPinController = TextEditingController();

  bool _showTempPin = false;
  bool _showNewPin = false;
  bool _showConfirmPin = false;
  bool _isLoading = false;

  String? _tempPinError;
  String? _newPinError; 
  String? _confirmPinError;

  @override
  void dispose() {
    tempPinController.dispose();
    newPinController.dispose();
    confirmPinController.dispose();
    super.dispose();
  }

  void _validateTempPin(String value) {
    setState(() {
      if (value.isEmpty) {
        _tempPinError = 'Please enter your temporary PIN';
      } else if (value.length != 4) {
        _tempPinError = 'PIN must be 4 digits';
      } else {
        _tempPinError = null;
      }
    });
  }

  void _validateNewPin(String value) {
    setState(() {
      if (value.isEmpty) {
        _newPinError = 'Please enter a new PIN';
      } else if (value.length != 4) {
        _newPinError = 'PIN must be 4 digits';
      } else if (value == tempPinController.text) {
        _newPinError = 'New PIN must be different from temporary PIN';
      } else {
        _newPinError = null;
      }
    });
    
    // Re-validate confirm PIN if it has been entered
    if (confirmPinController.text.isNotEmpty) {
      _validateConfirmPin(confirmPinController.text);
    }
  }

  void _validateConfirmPin(String value) {
    setState(() {
      if (value.isEmpty) {
        _confirmPinError = 'Please confirm your new PIN';
      } else if (value.length != 4) {
        _confirmPinError = 'PIN must be 4 digits';
      } else if (value != newPinController.text) {
        _confirmPinError = 'PINs do not match';
      } else {
        _confirmPinError = null;
      }
    });
  }

  bool _isFormValid() {
    return tempPinController.text.length == 4 &&
           newPinController.text.length == 4 &&
           confirmPinController.text.length == 4 &&
           _tempPinError == null &&
           _newPinError == null &&
           _confirmPinError == null;
  }

  Future<String> changePin() async {
    try {
      final apiService = ApiService();
      final aes = AesEncryption();
      final prefs = await SharedPreferences.getInstance();

      final secretKey = prefs.getString('_tajemnica') ?? '';
      final imei = await apiService.getDeviceId();
      final clientId = await SharedPreferencesHelper.getClientId();
      final spCustId = await SharedPreferencesHelper.getCustId();
      final msisdn = prefs.getString('msisdn') ?? '';

      if (secretKey.isEmpty || spCustId == null || msisdn.isEmpty) {
        throw Exception('Session expired. Please login again.');
      }

      final requestBody = {
        "header": {
          "categoryName": "Spotcash_App",
          "clientId": clientId,
          "msisdn": aes.encryptWithBase64Key(msisdn, secretKey),
          "imei": imei,
        },
        "requestData": {
          "cmp": aes.encryptWithBase64Key(tempPinController.text, secretKey),
          "custid": aes.encryptWithBase64Key(spCustId, secretKey),
          "customerType": 'CUSTOMER',
          "newPin": aes.encryptWithBase64Key(newPinController.text, secretKey),
          "confirmPin": aes.encryptWithBase64Key(confirmPinController.text, secretKey),
          "txnType": 'CP'
        }
      };

      final response = await apiService.postRequest(ApiEndpoints.transaction, requestBody);

      final decryptedResponse = aes.decryptWithBase64Key(response['hashedBody'], secretKey);

      // Parse the decrypted response to get the success message
      final decodedResponse = json.decode(decryptedResponse);
      final responseMessage = decodedResponse['header']['sd'] ?? 'PIN changed successfully';
      return responseMessage;
    } catch (e) {
      // Log error for debugging
      debugPrint('Error changing PIN: $e');
      return 'Failed to change PIN. Please try again.';
    }
  }

  Future<void> _changePin() async {
    if (!_isFormValid()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final message = await changePin();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: message.contains('successfully') ? ColorPalette.success : ColorPalette.error,
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.fromLTRB(16, 0, 16, 16),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        );
        
        if (message.contains('successfully')) {
          _showSuccessDialog();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to change PIN: ${e.toString()}'),
            backgroundColor: ColorPalette.error,
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.fromLTRB(16, 0, 16, 16),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSuccessDialog() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(20),
        child: Container(
          decoration: BoxDecoration(
            color: isDarkMode ? ColorPalette.surface : Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with gradient
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      ColorPalette.success,
                      ColorPalette.success.withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(24),
                  ),
                ),
                child: Column(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Icon(
                        Icons.check_circle_rounded,
                        size: 30,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'PIN Reset Successfully',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Content
              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    Text(
                      'Your PIN has been reset successfully! You can now login with your new PIN.',
                      style: TextStyle(
                        fontSize: 16,
                        color: ColorPalette.textPrimary,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      height: 48,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pushAndRemoveUntil(
                            context,
                            MaterialPageRoute(builder: (context) => LoginScreen()),
                            (route) => false,
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorPalette.primary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'Continue to Login',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    required bool obscureText,
    required VoidCallback toggleVisibility,
    required Function(String) onChanged,
    String? errorText,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextField(
            controller: controller,
            obscureText: obscureText,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(4),
            ],
            style: TextStyle(
              color: ColorPalette.textPrimary,
              fontSize: 16,
            ),
            decoration: InputDecoration(
              labelText: labelText,
              hintText: hintText,
              labelStyle: TextStyle(
                color: ColorPalette.textSecondary,
              ),
              hintStyle: TextStyle(
                color: ColorPalette.textSecondary.withValues(alpha: 0.7),
              ),
              filled: true,
              fillColor: isDark ? ColorPalette.background : ColorPalette.surface,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ColorPalette.primary, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ColorPalette.error, width: 1),
              ),
              prefixIcon: Icon(
                Icons.lock_outline,
                color: ColorPalette.textSecondary,
              ),
              suffixIcon: IconButton(
                icon: Icon(
                  obscureText ? Icons.visibility_off : Icons.visibility,
                  color: ColorPalette.textSecondary,
                ),
                onPressed: toggleVisibility,
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            onChanged: onChanged,
          ),
          if (errorText != null)
            Padding(
              padding: const EdgeInsets.only(left: 8.0, top: 4.0),
              child: Text(
                errorText,
                style: TextStyle(
                  color: ColorPalette.error,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? ColorPalette.background : Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'Reset PIN',
          style: TextStyle(
            color: ColorPalette.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: isDark ? ColorPalette.surface : Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ColorPalette.textPrimary,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              margin: const EdgeInsets.only(bottom: 24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    ColorPalette.primary,
                    ColorPalette.primary.withValues(alpha: 0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: ColorPalette.primary.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.security,
                    color: Colors.white,
                    size: 32,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Reset Your PIN',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Enter the temporary PIN from your SMS/email and set a new PIN',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            // Form Fields
            _buildTextField(
              controller: tempPinController,
              labelText: 'Temporary PIN',
              hintText: 'Enter 4-digit temporary PIN',
              obscureText: _showTempPin,
              toggleVisibility: () => setState(() => _showTempPin = !_showTempPin),
              onChanged: _validateTempPin,
              errorText: _tempPinError,
            ),
            
            _buildTextField(
              controller: newPinController,
              labelText: 'New PIN',
              hintText: 'Enter new 4-digit PIN',
              obscureText: _showNewPin,
              toggleVisibility: () => setState(() => _showNewPin = !_showNewPin),
              onChanged: _validateNewPin,
              errorText: _newPinError,
            ),
            
            _buildTextField(
              controller: confirmPinController,
              labelText: 'Confirm New PIN',
              hintText: 'Re-enter new PIN',
              obscureText: _showConfirmPin,
              toggleVisibility: () => setState(() => _showConfirmPin = !_showConfirmPin),
              onChanged: _validateConfirmPin,
              errorText: _confirmPinError,
            ),

            const SizedBox(height: 24),

            // Submit Button
            SizedBox(
              width: double.infinity,
              height: 52,
              child: ElevatedButton(
                onPressed: _isFormValid() && !_isLoading ? _changePin : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorPalette.primary,
                  disabledBackgroundColor: ColorPalette.primary.withValues(alpha: 0.5),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 0,
                ),
                child: _isLoading
                    ? SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          strokeWidth: 2,
                        ),
                      )
                    : Text(
                        'Reset PIN',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),

            const SizedBox(height: 16),

            // Security tip
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ColorPalette.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: ColorPalette.info.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.security,
                    color: ColorPalette.info,
                    size: 16,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Choose a PIN that\'s easy to remember but hard for others to guess',
                      style: TextStyle(
                        fontSize: 12,
                        color: ColorPalette.info,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
} 