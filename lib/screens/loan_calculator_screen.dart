import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/color_palette.dart';
import 'package:intl/intl.dart';

class LoanCalculatorScreen extends StatefulWidget {
  const LoanCalculatorScreen({super.key});

  @override
  State<LoanCalculatorScreen> createState() => _LoanCalculatorScreenState();
}

class _LoanCalculatorScreenState extends State<LoanCalculatorScreen> {
  double _loanAmount = 100000; // Default loan amount in KES
  double _loanTenure = 1; // Will store either months or years based on mode
  double _interestRate = 5.0; // Default interest rate
  double _monthlyPayment = 0;
  bool _useMonths = true; // Toggle between months and years

  @override
  void initState() {
    super.initState();
    _calculateMonthlyPayment();
  }

  void _calculateMonthlyPayment() {
    final monthlyInterestRate = _interestRate / 100 / 12;
    final numberOfPayments = _useMonths ? _loanTenure : _loanTenure * 12;
    
    if (monthlyInterestRate == 0) {
      _monthlyPayment = _loanAmount / numberOfPayments;
    } else {
      _monthlyPayment = _loanAmount * 
          (monthlyInterestRate * math.pow(1 + monthlyInterestRate, numberOfPayments)) /
          (math.pow(1 + monthlyInterestRate, numberOfPayments) - 1);
    }
    
    setState(() {});
  }

  String _formatCurrency(double amount) {
    return 'KES ${NumberFormat('#,##0').format(amount)}';
  }

  String _formatTenure(double value) {
    if (_useMonths) {
      if (value < 12) {
        return '${value.toInt()} months';
      } else {
        final years = (value / 12).toStringAsFixed(1);
        return '${NumberFormat('#,##0.0').format(double.parse(years))} years';
      }
    } else {
      return '${NumberFormat('#,##0.0').format(value)} years';
    }
  }

  Widget _buildSlider({
    required String title,
    required double value,
    required double min,
    required double max,
    required String unit,
    required ValueChanged<double> onChanged,
    TextStyle? style,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: style,
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: (max - min).toInt(),
          label: '${value.toStringAsFixed(unit == 'KES' ? 0 : 1)} $unit',
          onChanged: (newValue) {
            onChanged(newValue);
            _calculateMonthlyPayment();
          },
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('${min.toStringAsFixed(0)} $unit'),
            Text('${max.toStringAsFixed(0)} $unit'),
          ],
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildTenureToggle() {
    return ToggleButtons(
      isSelected: [_useMonths, !_useMonths],
      onPressed: (index) {
        setState(() {
          _useMonths = index == 0;
          // Convert the current value when switching modes
          _loanTenure = _useMonths ? 
              (_loanTenure * 12).clamp(1, 360) : // Convert years to months, clamp to 1-360
              (_loanTenure / 12).clamp(0.1, 30); // Convert months to years, clamp to 0.1-30
        });
        _calculateMonthlyPayment();
      },
      children: const [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0),
          child: Text('Months'),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0),
          child: Text('Years'),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final textColor = isDark ? Colors.white : Colors.black;

    return Scaffold(
      backgroundColor: isDark ? Theme.of(context).scaffoldBackgroundColor : ColorPalette.greyBackground,
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Loan Calculator',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        centerTitle: true,
      ),
      body: Container(
        color: isDark ? Theme.of(context).scaffoldBackgroundColor : ColorPalette.greyBackground,
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Monthly Payment Display
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDark ? Colors.grey[800] : Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Text(
                    'Monthly Payment',
                    style: TextStyle(
                      fontSize: 18,
                      color: textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _formatCurrency(_monthlyPayment),
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: textColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Loan Details
            Text(
              'Loan Details',
              style: TextStyle(
                color: ColorPalette.primary,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Loan Amount Slider
            _buildSlider(
              title: 'Loan Amount',
              value: _loanAmount,
              min: 1000,
              max: 1000000,
              unit: 'KES',
              onChanged: (value) => setState(() => _loanAmount = value),
            ),

            // Tenure Mode Toggle
            Center(
              child: _buildTenureToggle(),
            ),
            const SizedBox(height: 8),

            // Loan Tenure Slider
            _buildSlider(
              title: 'Loan Tenure',
              value: _loanTenure,
              min: _useMonths ? 1 : 0.1,
              max: _useMonths ? 360 : 30,
              unit: _useMonths ? 'months' : 'years',
              onChanged: (value) => setState(() {
                _loanTenure = value;
                _calculateMonthlyPayment();
              }),
              style: TextStyle(
                color: ColorPalette.primary,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),

            // Interest Rate Slider
            _buildSlider(
              title: 'Interest Rate',
              value: _interestRate,
              min: 1,
              max: 20,
              unit: '%',
              onChanged: (value) => setState(() => _interestRate = value),
              style: TextStyle(
                color: ColorPalette.primary,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),

            // Summary Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDark ? Colors.grey[800] : Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  _buildSummaryRow('Loan Amount', _formatCurrency(_loanAmount)),
                  _buildSummaryRow('Loan Tenure', _formatTenure(_loanTenure)),
                  _buildSummaryRow('Interest Rate', '${_interestRate.toStringAsFixed(1)}%'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 16),
          ),
          Text(
            value,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}