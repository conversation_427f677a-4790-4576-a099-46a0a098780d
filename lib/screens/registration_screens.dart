import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'login_screen.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:async';
import 'package:flutter/services.dart';

import '../utils/app_logger.dart';
class RegistrationScreen extends StatefulWidget {
  const RegistrationScreen({super.key});

  @override
  State<RegistrationScreen> createState() => _RegistrationScreenState();
}

class _RegistrationScreenState extends State<RegistrationScreen> {
  // Form keys
  final _addressFormKey = GlobalKey<FormState>();

  // Controllers
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _idNumberController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _countryController = TextEditingController();
  final TextEditingController _countyController = TextEditingController();
  final TextEditingController _townController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();

  final List<TextEditingController> _codeControllers = List.generate(4, (index) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(4, (index) => FocusNode());

  // Image picker
  File? _frontIdImage;
  File? _backIdImage;
  File? _passportPhoto;
  File? _signature;

  // Country selection
  String _selectedCountry = '';
  String _selectedCounty = '';
  final List<String> _countries = [
    'Kenya','Afghanistan', 'Albania', 'Algeria', 'Andorra', 'Angola', 'Antigua and Barbuda',
    'Argentina', 'Armenia', 'Australia', 'Austria', 'Azerbaijan', 'Bahamas', 'Bahrain',
    'Bangladesh', 'Barbados', 'Belarus', 'Belgium', 'Belize', 'Benin', 'Bhutan',
    'Bolivia', 'Bosnia and Herzegovina', 'Botswana', 'Brazil', 'Brunei', 'Bulgaria',
    'Burkina Faso', 'Burundi', 'Cabo Verde', 'Cambodia', 'Cameroon', 'Canada',
    'Central African Republic', 'Chad', 'Chile', 'China', 'Colombia', 'Comoros',
    'Congo', 'Costa Rica', 'Croatia', 'Cuba', 'Cyprus', 'Czech Republic', 'Denmark',
    'Djibouti', 'Dominica', 'Dominican Republic', 'Ecuador', 'Egypt', 'El Salvador',
    'Equatorial Guinea', 'Eritrea', 'Estonia', 'Eswatini', 'Ethiopia', 'Fiji',
    'Finland', 'France', 'Gabon', 'Gambia', 'Georgia', 'Germany', 'Ghana', 'Greece',
    'Grenada', 'Guatemala', 'Guinea', 'Guinea-Bissau', 'Guyana', 'Haiti', 'Honduras',
    'Hungary', 'Iceland', 'India', 'Indonesia', 'Iran', 'Iraq', 'Ireland', 'Israel',
    'Italy', 'Jamaica', 'Japan', 'Jordan', 'Kazakhstan', 'Kiribati',
    'Korea, North', 'Korea, South', 'Kosovo', 'Kuwait', 'Kyrgyzstan', 'Laos',
    'Latvia', 'Lebanon', 'Lesotho', 'Liberia', 'Libya', 'Liechtenstein', 'Lithuania',
    'Luxembourg', 'Madagascar', 'Malawi', 'Malaysia', 'Maldives', 'Mali', 'Malta',
    'Marshall Islands', 'Mauritania', 'Mauritius', 'Mexico', 'Micronesia', 'Moldova',
    'Monaco', 'Mongolia', 'Montenegro', 'Morocco', 'Mozambique', 'Myanmar', 'Namibia',
    'Nauru', 'Nepal', 'Netherlands', 'New Zealand', 'Nicaragua', 'Niger', 'Nigeria',
    'North Macedonia', 'Norway', 'Oman', 'Pakistan', 'Palau', 'Palestine', 'Panama',
    'Papua New Guinea', 'Paraguay', 'Peru', 'Philippines', 'Poland', 'Portugal',
    'Qatar', 'Romania', 'Russia', 'Rwanda', 'Saint Kitts and Nevis', 'Saint Lucia',
    'Saint Vincent and the Grenadines', 'Samoa', 'San Marino', 'Sao Tome and Principe',
    'Saudi Arabia', 'Senegal', 'Serbia', 'Seychelles', 'Sierra Leone', 'Singapore',
    'Slovakia', 'Slovenia', 'Solomon Islands', 'Somalia', 'South Africa', 'South Sudan',
    'Spain', 'Sri Lanka', 'Sudan', 'Suriname', 'Sweden', 'Switzerland', 'Syria',
    'Taiwan', 'Tajikistan', 'Tanzania', 'Thailand', 'Timor-Leste', 'Togo', 'Tonga',
    'Trinidad and Tobago', 'Tunisia', 'Turkey', 'Turkmenistan', 'Tuvalu', 'Uganda',
    'Ukraine', 'United Arab Emirates', 'United Kingdom', 'United States', 'Uruguay',
    'Uzbekistan', 'Vanuatu', 'Vatican City', 'Venezuela', 'Vietnam', 'Yemen',
    'Zambia', 'Zimbabwe'
  ];
  final List<String> _kenyanCounties = [
    'Baringo', 'Bomet', 'Bungoma', 'Busia', 'Elgeyo Marakwet', 'Embu', 'Garissa',
    'Homa Bay', 'Isiolo', 'Kajiado', 'Kakamega', 'Kericho', 'Kiambu', 'Kilifi',
    'Kirinyaga', 'Kisii', 'Kisumu', 'Kitui', 'Kwale', 'Laikipia', 'Lamu',
    'Machakos', 'Makueni', 'Mandera', 'Marsabit', 'Meru', 'Migori', 'Mombasa',
    'Murang\'a', 'Nairobi', 'Nakuru', 'Nandi', 'Narok', 'Nyamira', 'Nyandarua',
    'Nyeri', 'Samburu', 'Siaya', 'Taita Taveta', 'Tana River', 'Tharaka Nithi',
    'Trans Nzoia', 'Turkana', 'Uasin Gishu', 'Vihiga', 'Wajir', 'West Pokot'
  ];

  // State variables
  final bool _showValidationErrors = false;
  int _currentStep = 0;
  bool _isResendEnabled = false;
  int _remainingTime = 30;
  String? _verifiedEmail;
  String? _verifiedPhone;
  String? _selectedVerificationMethod;
  bool _isEditing = false;
  Timer? _timer;

  @override
  void dispose() {
    _timer?.cancel();
    for (var controller in _codeControllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    // Dispose controllers
    _firstNameController.dispose();
    _lastNameController.dispose();
    _idNumberController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _countryController.dispose();
    _countyController.dispose();
    _townController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  Widget _buildStepIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(6, (index) {
          bool isActive = index <= _currentStep;
          bool isLast = index == 5;
          return Row(
            children: [
              Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isActive ? const Color(0xFFE91E63) : Colors.grey[300],
                  boxShadow: [
                    BoxShadow(
                      color: isActive ? const Color(0xFFE91E63).withValues(alpha: 0.3) : Colors.transparent,
                      blurRadius: 8,
                      spreadRadius: 2,
                    )
                  ],
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ),
              if (!isLast)
                Container(
                  width: 30,
                  height: 2,
                  color: isActive ? const Color(0xFFE91E63) : Colors.grey[300],
                ),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildCurrentStep() {
    switch (_currentStep) {
      case 0:
        return _buildPersonalDetails();
      case 1:
        return _buildAddressDetails();
      case 2:
        return _buildImageUpload();
      case 3:
        return _buildVerification();
      case 4:
        return _buildReviewDetails();
      case 5:
        return _buildEmailVerification();
      default:
        return _buildPersonalDetails();
    }
  }

  Widget _buildPersonalDetails() {
    bool isFormValid = _firstNameController.text.isNotEmpty &&
        _lastNameController.text.isNotEmpty &&
        _phoneController.text.isNotEmpty &&
        RegExp(r'^(07|01)\d{8}$|^254\d{9}$').hasMatch(_phoneController.text) &&
        _emailController.text.isNotEmpty &&
        RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(_emailController.text);

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Personal details',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFFE91E63),
            ),
          )
              .animate()
              .fadeIn(duration: 500.ms)
              .slideX(begin: -0.2, duration: 500.ms),
          const SizedBox(height: 20),
          TextFormField(
            controller: _firstNameController,
            decoration: const InputDecoration(
              labelText: 'First name',
              border: UnderlineInputBorder(),
            ),
            autovalidateMode: AutovalidateMode.onUserInteraction,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your first name';
              }
              return null;
            },
            onChanged: (value) {
              setState(() {}); // Update state to re-evaluate form validity
            },
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: _lastNameController,
            decoration: const InputDecoration(
              labelText: 'Last name',
              border: UnderlineInputBorder(),
            ),
            autovalidateMode: AutovalidateMode.onUserInteraction,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your last name';
              }
              return null;
            },
            onChanged: (value) {
              setState(() {}); // Update state to re-evaluate form validity
            },
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: _idNumberController,
            decoration: const InputDecoration(
              labelText: 'ID/Passport Number',
              border: UnderlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your ID number';
              }
              return null;
            },
            onChanged: (value) {
              setState(() {}); // Update state to re-evaluate form validity
            },
          ),

          const SizedBox(height: 20),
          TextFormField(
            controller: _phoneController,
            decoration: const InputDecoration(
              labelText: 'Phone Number',
              border: UnderlineInputBorder(),
              focusedBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: Color(0xFFE91E63), width: 2),
              ),
            ),
            keyboardType: TextInputType.phone,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
            ],
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your phone number';
              }
              if (!RegExp(r'^(07|01)\d{8}$|^254\d{9}$').hasMatch(value)) {
                return 'Please enter a valid Kenyan phone number';
              }
              return null;
            },
            onChanged: (value) {
              setState(() {});
            },
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: _emailController,
            decoration: const InputDecoration(
              labelText: 'Email Address',
              border: UnderlineInputBorder(),
            ),
            keyboardType: TextInputType.emailAddress,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your email address';
              }
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                return 'Please enter a valid email address';
              }
              return null;
            },
            onChanged: (value) {
              setState(() {}); // Update state to re-evaluate form validity
            },
          ),
          const Spacer(),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFE6EF),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextButton(
                    onPressed: () {
                      setState(() {
                        if (_isEditing) {
                          _currentStep = 4;
                          _isEditing = false;
                        } else {
                          Navigator.pop(context);
                        }
                      });
                    },
                    child: Text(
                      'Back',
                      style: TextStyle(
                        color: Color(0xFFE91E63),
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                    color: isFormValid ? const Color(0xFF6B4E71) : Colors.grey,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextButton(
                    onPressed: isFormValid
                        ? () {
                      setState(() {
                        if (_isEditing) {
                          _currentStep = 4;
                          _isEditing = false;
                        } else {
                          _currentStep = 1;
                        }
                      });
                    }
                        : null,
                    child: Text(
                      _isEditing ? 'Update' : 'Next',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage(String type, {ImageSource? source}) async {
    if (source == null) {
      showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                ListTile(
                  leading: Icon(Icons.photo_camera),
                  title: Text('Take a photo'),
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage(type, source: ImageSource.camera);
                  },
                ),
                ListTile(
                  leading: Icon(Icons.photo_library),
                  title: Text('Choose from gallery'),
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage(type, source: ImageSource.gallery);
                  },
                ),
              ],
            ),
          );
        },
      );
      return;
    }

    try {
      final image = await ImagePicker().pickImage(source: source);
      if (image == null) return;

      setState(() {
        switch (type) {
          case 'front_id':
            _frontIdImage = File(image.path);
            break;
          case 'back_id':
            _backIdImage = File(image.path);
            break;
          case 'passport':
            _passportPhoto = File(image.path);
            break;
          case 'signature':
            _signature = File(image.path);
            break;
        }
      });
    } catch (e) {
      // Handle error
      AppLogger.error('picking image: $e');
    }
  }

  Widget _buildAddressDetails() {
    bool isFormValid = _selectedCountry.isNotEmpty &&
        (_selectedCountry == 'Kenya' ? _selectedCounty.isNotEmpty : _countyController.text.isNotEmpty) &&
        _townController.text.isNotEmpty &&
        _addressController.text.isNotEmpty;

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Form(
        key: _addressFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Address details',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFFE91E63),
              ),
            )
                .animate()
                .fadeIn(duration: 500.ms)
                .slideX(begin: -0.2, duration: 500.ms),
            const SizedBox(height: 30),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    DropdownButtonFormField<String>(
                      value: _selectedCountry.isEmpty ? null : _selectedCountry,
                      decoration: const InputDecoration(
                        labelText: 'Country',
                        border: UnderlineInputBorder(),
                      ),
                      items: _countries.map((String country) {
                        return DropdownMenuItem(
                          value: country,
                          child: Text(country),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          _selectedCountry = newValue ?? '';
                          // Clear county when country changes
                          if (_selectedCountry != 'Kenya') {
                            _selectedCounty = '';
                            _countyController.clear();
                          }
                        });
                      },
                      validator: (value) {
                        if (_showValidationErrors && (value == null || value.isEmpty)) {
                          return 'Please select a country';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),
                    if (_selectedCountry == 'Kenya')
                      DropdownButtonFormField<String>(
                        value: _selectedCounty.isEmpty ? null : _selectedCounty,
                        decoration: const InputDecoration(
                          labelText: 'County',
                          border: UnderlineInputBorder(),
                        ),
                        items: _kenyanCounties.map((String county) {
                          return DropdownMenuItem(
                            value: county,
                            child: Text(county),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          setState(() {
                            _selectedCounty = newValue ?? '';
                            _countyController.text = _selectedCounty;
                          });
                        },
                        validator: (value) {
                          if (_selectedCountry == 'Kenya' && (value == null || value.isEmpty)) {
                            return 'Please select a county';
                          }
                          return null;
                        },
                      )
                    else
                      TextFormField(
                        controller: _countyController,
                        decoration: const InputDecoration(
                          labelText: 'State/Province/Region',
                          border: UnderlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your state/province/region';
                          }
                          return null;
                        },
                      ),
                    const SizedBox(height: 20),
                    TextFormField(
                      controller: _townController,
                      decoration: const InputDecoration(
                        labelText: 'Town',
                        border: UnderlineInputBorder(),
                      ),
                      onChanged: (value) {
                        setState(() {}); // Update state to re-evaluate form validity
                      },
                      validator: (value) {
                        if (_showValidationErrors && (value == null || value.isEmpty)) {
                          return 'Please enter your town';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),
                    TextFormField(
                      controller: _addressController,
                      decoration: const InputDecoration(
                        labelText: 'Address',
                        border: UnderlineInputBorder(),
                      ),
                      onChanged: (value) {
                        setState(() {}); // Update state to re-evaluate form validity
                      },
                      validator: (value) {
                        if (_showValidationErrors && (value == null || value.isEmpty)) {
                          return 'Please enter your address';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const Spacer(),
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFE6EF),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextButton(
                      onPressed: () {
                        setState(() {
                          if (_isEditing) {
                            _currentStep = 4;
                            _isEditing = false;
                          } else {
                            _currentStep = 0;
                          }
                        });
                      },
                      child: Text(
                        'Back',
                        style: TextStyle(
                          color: Color(0xFFE91E63),
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: isFormValid ? const Color(0xFF6B4E71) : Colors.grey,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextButton(
                      onPressed: isFormValid
                          ? () {
                        setState(() {
                          if (_isEditing) {
                            _currentStep = 4;
                            _isEditing = false;
                          } else {
                            _currentStep = 2;
                          }
                        });
                      }
                          : null,
                      child: Text(
                        _isEditing ? 'Update' : 'Next',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageUpload() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ID Verification',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFFE91E63),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Please upload clear images of your National ID',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildImageUploadBox(
                    'Front of ID',
                    _frontIdImage,
                    () => _pickImage('front_id'),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: _buildImageUploadBox(
                    'Back of ID',
                    _backIdImage,
                    () => _pickImage('back_id'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Text(
              'Documents',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFFE91E63),
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildImageUploadBox(
                    'Add Passport Photo',
                    _passportPhoto,
                    () => _pickImage('passport'),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: _buildImageUploadBox(
                    'Add Signature',
                    _signature,
                    () => _pickImage('signature'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 40),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFE6EF),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextButton(
                      onPressed: () {
                        setState(() {
                          _currentStep--;
                        });
                      },
                      child: Text(
                        'Back',
                        style: TextStyle(
                          color: Color(0xFFE91E63),
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: _frontIdImage != null &&
                              _backIdImage != null &&
                              _passportPhoto != null &&
                              _signature != null
                          ? const Color(0xFF6B4E71)
                          : Colors.grey,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextButton(
                      onPressed: _frontIdImage != null &&
                              _backIdImage != null &&
                              _passportPhoto != null &&
                              _signature != null
                          ? () {
                              setState(() {
                                _currentStep++;
                              });
                            }
                          : null,
                      child: Text(
                        'Next',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageUploadBox(String title, File? image, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 150,
        decoration: BoxDecoration(
          color: Colors.grey[50],
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 8,
              spreadRadius: 2,
            )
          ],
        ),
        child: image != null
            ? Stack(
                fit: StackFit.expand,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Image.file(
                      image,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.9),
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: Icon(Icons.close, color: Colors.red, size: 20),
                        onPressed: () {
                          setState(() {
                            switch (title) {
                              case 'Front of ID':
                                _frontIdImage = null;
                                break;
                              case 'Back of ID':
                                _backIdImage = null;
                                break;
                              case 'Add Passport Photo':
                                _passportPhoto = null;
                                break;
                              case 'Add Signature':
                                _signature = null;
                                break;
                            }
                          });
                        },
                      ),
                    ),
                  ),
                ],
              )
            : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.add_photo_alternate,
                    size: 40,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 10),
                  Text(
                    title,
                    style: TextStyle(
                      color: Colors.grey[400],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildVerification() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Verify',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFFE91E63),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Verify your account',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 20),
          ListTile(
            title: Text(
              'Phone (${_phoneController.text})',
              style: const TextStyle(
                color: Colors.black87,
              ),
            ),
            onTap: () {
              setState(() {
                _selectedVerificationMethod = 'phone';
                _currentStep++;
              });
            },
            trailing: Icon(Icons.arrow_forward_ios),
            tileColor: Colors.pink.shade50,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          const SizedBox(height: 40),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _currentStep--;
                  });
                },
                child: Text('Back'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmailVerification() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Confirmation',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFFE91E63),
            ),
          )
          .animate()
          .fadeIn(duration: 500.ms)
          .slideX(begin: -0.2, duration: 500.ms),
          const SizedBox(height: 20),
          Text(
            _selectedVerificationMethod == 'email' ? 'Verify your email' : 'Verify your phone',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            _selectedVerificationMethod == 'email'
                ? 'Please fill in the 4 digit code sent to ${_emailController.text}'
                : 'Please fill in the 4 digit code sent to ${_phoneController.text}',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 40),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(
              4,
              (index) => SizedBox(
                width: 60,
                child: TextFormField(
                  controller: _codeControllers[index],
                  focusNode: _focusNodes[index],
                  textAlign: TextAlign.center,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly
                  ],
                  maxLength: 1,
                  onChanged: (value) {
                    if (value.isNotEmpty) {
                      if (index < 3) {
                        _focusNodes[index + 1].requestFocus();
                      } else {
                        _focusNodes[index].unfocus();
                      }
                    } else if (value.isEmpty && index > 0) {
                      _focusNodes[index - 1].requestFocus();
                    }
                  },
                  decoration: InputDecoration(
                    counterText: '',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Color(0xFFE91E63)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Color(0xFFE91E63), width: 2),
                    ),
                  ),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
          Center(
            child: TextButton(
              onPressed: _isResendEnabled ? () {
                setState(() {
                  _isResendEnabled = false;
                  _remainingTime = 30;
                  _startResendTimer();
                  // Add your resend code logic here
                });
              } : null,
              child: Text(
                _isResendEnabled ? 'Resend CODE' : 'Resend CODE ($_remainingTime)',
                style: TextStyle(
                  color: _isResendEnabled ? const Color(0xFFE91E63) : Colors.grey,
                ),
              ),
            ),
          ),
          const Spacer(),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFE6EF),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextButton(
                    onPressed: () {
                      setState(() {
                        _currentStep = 4; // Go back to review
                      });
                    },
                    child: Text(
                      'Back',
                      style: TextStyle(
                        color: Color(0xFFE91E63),
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                    color: const Color(0xFF6B4E71),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextButton(
                    onPressed: () {
                      // Here you would typically verify the code
                      // For now, we'll just navigate to success
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const RegistrationSuccess(),
                        ),
                      );
                    },
                    child: Text(
                      'Verify',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _startResendTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingTime > 0) {
          _remainingTime--;
        } else {
          _isResendEnabled = true;
          timer.cancel();
        }
      });
    });
  }

  Widget _buildReviewDetails() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Review Details',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFFE91E63),
            ),
          )
              .animate()
              .fadeIn(duration: 500.ms)
              .slideX(begin: -0.2, duration: 500.ms),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildReviewSection(
                    'Personal Information',
                    {
                      'First Name': _firstNameController.text,
                      'Last Name': _lastNameController.text,
                      'ID Number': _idNumberController.text,
                      'Phone': _phoneController.text,
                      'Email': _emailController.text,
                    },
                    0,
                  ),
                  const SizedBox(height: 20),
                  _buildReviewSection(
                    'Address Details',
                    {
                      'Country': _selectedCountry,
                      'County': _selectedCountry == 'Kenya' ? _selectedCounty : _countyController.text,
                      'Town': _townController.text,
                      'Address': _addressController.text,
                    },
                    1,
                  ),
                  const SizedBox(height: 20),
                  _buildReviewSection(
                    'ID Verification',
                    {
                      'Front ID': _frontIdImage != null ? 'Uploaded' : 'Not uploaded',
                      'Back ID': _backIdImage != null ? 'Uploaded' : 'Not uploaded',
                    },
                    2,
                  ),
                  const SizedBox(height: 20),
                  _buildReviewSection(
                    'Verification Method',
                    {
                      'Method': _selectedVerificationMethod == 'email'
                          ? 'Email Verification'
                          : 'Phone Verification',
                      'Verified Contact': _selectedVerificationMethod == 'email'
                          ? _verifiedEmail ?? ''
                          : _verifiedPhone ?? '',
                    },
                    3,
                  ),
                  const SizedBox(height: 20),
                  _buildReviewSection(
                    'Documents',
                    {
                      'Passport Photo': _passportPhoto != null ? 'Uploaded' : 'Not uploaded',
                      'Signature': _signature != null ? 'Uploaded' : 'Not uploaded',
                    },
                    4,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFE6EF),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextButton(
                    onPressed: () {
                      setState(() {
                        if (_isEditing) {
                          _currentStep = 4;
                          _isEditing = false;
                        } else {
                          _currentStep = 3;
                        }
                      });
                    },
                    child: Text(
                      'Back',
                      style: TextStyle(
                        color: Color(0xFFE91E63),
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                    color: const Color(0xFF6B4E71),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextButton(
                    onPressed: () {
                      setState(() {
                        _currentStep = 5;
                      });
                    },
                    child: Center(
                      child: Text(
                        'Verify',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReviewSection(String title, Map<String, String> details, int stepToEdit) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border.all(
          color: Theme.of(context).dividerColor,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            spreadRadius: 2,
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.edit,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                onPressed: () {
                  setState(() {
                    _isEditing = true;
                    _currentStep = stepToEdit;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...details.entries.map((entry) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    '${entry.key}:',
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    entry.value,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            const SizedBox(height: 20),
            _buildStepIndicator(),
            const SizedBox(height: 20),
            Expanded(
              child: _buildCurrentStep(),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _isResendEnabled = true;
    _startResendTimer();
  }
}

class RegistrationSuccess extends StatelessWidget {
  const RegistrationSuccess({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              color: Color(0xFFE91E63),
              size: 100,
            )
                .animate()
                .scale(duration: 500.ms)
                .fade(duration: 500.ms),
            const SizedBox(height: 20),
            Text(
              'Registration Successful!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFFE91E63),
              ),
            )
                .animate()
                .fadeIn(duration: 500.ms, delay: 300.ms)
                .slideY(begin: 0.2, duration: 500.ms),
            const SizedBox(height: 10),
            Text(
              'You will receive an SMS notification once your account is set up.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            )
                .animate()
                .fadeIn(duration: 500.ms, delay: 600.ms)
                .slideY(begin: 0.2, duration: 500.ms),
            const SizedBox(height: 40),
            Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 20),
              height: 50,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6B4E71),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const LoginScreen(),
                    ),
                  );
                },
                child: Text(
                  'Continue to Login',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
