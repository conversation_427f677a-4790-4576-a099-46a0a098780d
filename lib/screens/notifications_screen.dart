import 'package:flutter/material.dart';
import '../utils/color_palette.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  // Sample notifications with read/unread status
  final List<Map<String, dynamic>> _notifications = [
    {
      'message': 'Your loan application has been approved.',
      'isRead': false,
    },
    {
      'message': 'New transaction: Deposit of KES 50,000.',
      'isRead': false,
    },
    {
      'message': 'Reminder: Loan repayment due in 3 days.',
      'isRead': false,
    },
  ];

  // Function to mark a notification as read
  void _markAsRead(int index) {
    setState(() {
      _notifications[index]['isRead'] = true;
    });
  }

  // Function to delete a notification
  void _deleteNotification(int index) {
    setState(() {
      _notifications.removeAt(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text('Notifications'),
        backgroundColor: ColorPalette.secondary,
        foregroundColor: Colors.white,
      ),
      body: Container(
        color: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).scaffoldBackgroundColor
            : ColorPalette.greyBackground,
        child: ListView.builder(
          itemCount: _notifications.length,
          itemBuilder: (context, index) {
            final notification = _notifications[index];
            return Card(
              color: isDark ? ColorPalette.white.withValues(alpha: 0.05) : ColorPalette.white,
              margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: isDark ? 0 : 2,
              child: Dismissible(
                key: Key(notification['message']),
                background: Container(
                  color: Colors.red,
                  alignment: Alignment.centerRight,
                  padding: const EdgeInsets.only(right: 20),
                  child: Icon(
                    Icons.delete,
                    color: Colors.white,
                  ),
                ),
                onDismissed: (direction) {
                  _deleteNotification(index);
                },
                child: ListTile(
                  onTap: () => _markAsRead(index),
                  leading: Icon(
                    Icons.notifications_none,
                    color: ColorPalette.secondary,
                  ),
                  title: Text(
                    notification['message'],
                    style: TextStyle(
                      fontWeight: notification['isRead'] ? FontWeight.normal : FontWeight.bold,
                      color: isDark ? Colors.white : ColorPalette.textDark,
                    ),
                  ),
                  trailing: IconButton(
                    icon: Icon(
                      Icons.delete_outline,
                      color: Colors.red,
                    ),
                    onPressed: () => _deleteNotification(index),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
} 