import 'package:flutter/material.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'registration_screens.dart';
import '../transactions/screens/dashboard_screen.dart';
import '../screens/atm_locator_screen.dart';
import '../screens/loan_calculator_screen.dart';
import 'package:flutter/services.dart';
import '../transactions/screens/otp_verification_screen.dart';
import '../../utils/color_palette.dart';
import '../configuration/client_config.dart';
import '../utils/theme_helper.dart';
import '../utils/services/auth_service.dart';
import '../utils/services/shared_preferences_helper.dart';
import 'security_questions_screen.dart';
import '../utils/idle_timer.dart';

import '../utils/app_logger.dart';
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with SingleTickerProviderStateMixin {
  final TextEditingController _mobileNumberController = TextEditingController();
  final TextEditingController _pinController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  bool _obscureText = true;
  final LocalAuthentication auth = LocalAuthentication();
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;
  bool _isValidPhone = false;
  bool _isValidPin = false;
  bool _showPrefixError = false;
  bool _isLoading = false;
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();

    // Initialize wave animation
    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);

    _waveAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _waveController,
        curve: Curves.easeInOut,
      ),
    );
  }

  Future<void> _handleLogin() async {
    if (!_isValidPhone || !_isValidPin) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Clear any stale tokens before initiating a new login flow
      await SharedPreferencesHelper.removeToken();
      await SharedPreferencesHelper.removeSecretKey();

      // Initialize client ID if not set
      String? clientId = await SharedPreferencesHelper.getClientId();
      if (clientId == null) {
        // Get client ID from build configuration or default
        await SharedPreferencesHelper.setClientId(ClientThemeManager().currentClientConfig.clientId.toString());
      }

      // Step 1: Verify phone number first
      AuthResult phoneResult = await _authService.verifyPhoneNumber(_mobileNumberController.text, context);
      
      if (!phoneResult.isSuccess) {
        _showErrorSnackBar(phoneResult.message);
        return;
      }

      // Step 2: Verify PIN immediately (no delay)
      AppLogger.info('Phone verification successful, immediately verifying PIN...');
      AuthResult pinResult = await _authService.verifyPin(_mobileNumberController.text, _pinController.text, context);
      AppLogger.info('PIN verification result: ${pinResult.isSuccess}, message: ${pinResult.message}');
      
      if (!pinResult.isSuccess) {
        // Clear the PIN field when authentication fails
        _pinController.clear();
        setState(() {
          _isValidPin = false;
        });
        
        if (pinResult.type == AuthResultType.changePasswordRequired) {
          // Handle password change requirement
          _showErrorSnackBar('Password change required. Please contact support.');
        } else {
          _showErrorSnackBar(pinResult.message);
        }
        return;
      }

      // Step 3: Show OTP verification screen
      AppLogger.info('PIN verification successful, showing OTP screen...');
      showDialog(
        context: context,
        barrierColor: Colors.transparent,
        builder: (context) {
          AppLogger.info('Building OTP verification screen...');
          return OtpVerificationScreen(
            phoneNumber: _mobileNumberController.text,
            transactionType: 'login',
            authService: _authService, // Pass the auth service
          );
        },
      ).then((value) {
        AppLogger.info('OTP dialog returned with value: $value');
        // After OTP verification, navigate to dashboard
        if (value == true) {
          // Enable the idle timer on successful login
          IdleTimerService().enableIdleTimer(reason: 'User logged in successfully');
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const DashboardScreen(
                userName: 'John Doe',
              ),
            ),
          );
        }
      });

    } catch (e) {
      AppLogger.info('Login error: $e');
      _showErrorSnackBar('An unexpected error occurred. Please try again.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _handleAtmPress() async {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AtmLocatorScreen(
          clientId: ClientThemeManager().currentClientConfig.clientId,
        ),
      ),
    );
  }

  Future<void> _handleCalcPress() async {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LoanCalculatorScreen(),
      ),
    );
  }

  Future<void> _verifyPhoneForReset(String phoneNumber) async {
    try {
      // Format the phone number
      String formattedPhone = _formatPhoneNumber(phoneNumber);
      
      // Verify phone number using existing auth service
      AuthResult result = await _authService.verifyPhoneNumber(formattedPhone, context);
      
      if (!result.isSuccess) {
        throw Exception(result.message);
      }
      
      // Store the phone number for use in security questions flow
      await SharedPreferencesHelper.setMsisdn(formattedPhone);
      
    } catch (e) {
      throw Exception('Failed to verify phone number: ${e.toString()}');
    }
  }

  String _formatPhoneNumber(String phoneNumber) {
    // Remove any non-digit characters
    String digits = phoneNumber.replaceAll(RegExp(r'\D'), '');
    
    // Format according to the existing logic
    if (digits.length == 12 && digits.startsWith('254')) {
      return digits;
    } else if (digits.length == 10 && digits.startsWith('0')) {
      return '254${digits.substring(1)}';
    } else if (digits.length == 9 && (digits.startsWith('7') || digits.startsWith('1'))) {
      return '254$digits';
    }
    
    return digits;
  }

  void _handleForgotPin() async {
    // First verify the phone number and set it in preferences, then navigate to security questions
    showDialog(
      context: context,
      builder: (BuildContext context) {
        bool isValidPhone = false;
        bool showPrefixError = false;
        bool isLoading = false;
        final isDark = Theme.of(context).brightness == Brightness.dark;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              backgroundColor: isDark ? Theme.of(context).dialogBackgroundColor : Colors.white,
              titlePadding: EdgeInsets.zero,
              title: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: ColorPalette.unselectedNavItemColor,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                ),
                child: Text(
                  'Forgot Pin',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                  textAlign: TextAlign.center,
                ),
              ).animate().fadeIn(duration: 400.ms).slideY(begin: 0.3),
              contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Please enter your registered phone number to reset your PIN',
                    style: TextStyle(
                      color: isDark ? Colors.white70 : Colors.grey[700],
                      fontSize: 14,
                    ),
                  ).animate().fadeIn(duration: 400.ms, delay: 200.ms),
                  const SizedBox(height: 20),
                  TextField(
                    controller: _emailController,
                    keyboardType: TextInputType.phone,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(12), // Max 12 digits for numbers starting with 254
                      TextInputFormatter.withFunction((oldValue, newValue) {
                        final text = newValue.text;
                        // If text starts with 254, limit to 12 digits
                        if (text.startsWith('254')) {
                          if (text.length > 12) {
                            return oldValue;
                          }
                        }
                        // If text starts with 0, limit to 10 digits
                        else if (text.startsWith('0')) {
                          if (text.length > 10) {
                            return oldValue;
                          }
                        }
                        return newValue;
                      }),
                    ],
                    style: TextStyle(
                      color: isDark ? Colors.white : Colors.black,
                    ),
                    decoration: InputDecoration(
                      labelText: 'Phone Number',
                      labelStyle: TextStyle(
                        color: isDark ? Colors.white70 : Theme.of(context).primaryColor,
                      ),
                      prefixIcon: Icon(
                        Icons.phone_outlined,
                        color: isDark ? Colors.white70 : Theme.of(context).primaryColor,
                      ),
                      suffixIcon: isValidPhone
                          ? Icon(
                        Icons.check_circle,
                        color: ColorPalette.primary,
                      ).animate(
                        onPlay: (controller) => controller.repeat(),
                      )
                          .scale(
                        duration: 400.ms,
                        curve: Curves.easeInOut,
                      )
                          .then()
                          .shake(
                        duration: 400.ms,
                        rotation: 0.1,
                      )
                          : null,
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: isDark ? Colors.white38 : Colors.grey[300]!,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Theme.of(context).primaryColor,
                          width: 2,
                        ),
                      ),
                      filled: true,
                      fillColor: isDark ? Colors.grey[900] : Colors.grey[50],
                    ),
                    onChanged: (value) {
                      final isValidPrefix = value.startsWith('07') || value.startsWith('01') || value.startsWith('254');
                      setState(() {
                        isValidPhone = RegExp(r'^(07|01)\d{8}$|^254\d{9}$').hasMatch(value);
                        showPrefixError = !isValidPrefix && value.isNotEmpty;
                      });
                    },
                  ).animate().fadeIn(duration: 400.ms, delay: 400.ms).slideX(begin: 0.3),
                  if (showPrefixError && _emailController.text.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0, top: 4.0),
                      child: Text(
                        'Phone number must start with 07, 01, or 254',
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
              actions: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: SizedBox(
                          height: 40,
                          child: ElevatedButton(
                            onPressed: isLoading ? null : () => Navigator.pop(context),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Container(
                          height: 40,
                          decoration: BoxDecoration(
                            color: (isValidPhone && !isLoading) ? const Color(0xFF6B4E71) : Colors.grey,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: TextButton(
                            onPressed: (isValidPhone && !isLoading)
                                ? () async {
                              setState(() {
                                isLoading = true;
                              });

                              try {
                                // Format and verify the phone number first
                                final String enteredPhone = _emailController.text;
                                await _verifyPhoneForReset(enteredPhone);
                                
                                Navigator.of(context).pop();
                                _emailController.clear();
                                
                                // Navigate to security questions
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => SecurityQuestionsScreen(),
                                  ),
                                );
                              } catch (e) {
                                setState(() {
                                  isLoading = false;
                                });
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Error: ${e.toString()}'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            }
                                : null,
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.white,
                            ),
                            child: isLoading
                                ? SizedBox(
                              height: 16,
                              width: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                                : Text(
                              'Continue',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  bool _validatePhone(String phone) {
    // Use the AuthService validation
    return _authService.validatePhoneNumber(phone);
  }

  bool _validatePin(String pin) {
    // Use the AuthService validation
    return _authService.validatePin(pin);
  }

  /// Show error message via SnackBar
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    bool isPassword = false,
    required Function(String) onChanged,
    double bottomMargin = 20.0,
    bool? showPrefixError,
    bool? isValid,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.only(bottom: bottomMargin),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: isDark ? Colors.black.withValues(alpha: 0.3) : Colors.grey.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: TextFormField(
            controller: controller,
            obscureText: isPassword ? _obscureText : false,
            keyboardType: isPassword ? TextInputType.number : TextInputType.phone,
            inputFormatters: isPassword
                ? [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(4),
            ]
                : [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(12),
              TextInputFormatter.withFunction((oldValue, newValue) {
                final text = newValue.text;
                if (text.startsWith('254')) {
                  if (text.length > 12) {
                    return oldValue;
                  }
                } else if (text.startsWith('0')) {
                  if (text.length > 10) {
                    return oldValue;
                  }
                }
                return newValue;
              }),
            ],
            style: TextStyle(
              color: isDark ? Colors.white : Colors.black,
              fontSize: 16,
            ),
            decoration: InputDecoration(
              labelText: label,
              labelStyle: TextStyle(
                color: isDark ? Colors.white70 : Colors.grey[600],
                fontSize: 14,
              ),
              filled: true,
              fillColor: isDark ? Colors.grey[900] : Colors.white,
              contentPadding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: BorderSide(
                  color: const Color(0xFF6B4E71),
                  width: 2,
                ),
              ),
              prefixIcon: isPassword
                  ? Icon(
                Icons.lock_outline,
                color: isDark ? Colors.white70 : Colors.grey[600],
              )
                  : Icon(
                Icons.phone_android_outlined,
                color: isDark ? Colors.white70 : Colors.grey[600],
              ),
              suffixIcon: isPassword
                  ? IconButton(
                icon: Icon(
                  _obscureText ? Icons.visibility_off : Icons.visibility,
                  color: isDark ? Colors.white70 : Colors.grey[600],
                ),
                onPressed: () {
                  setState(() {
                    _obscureText = !_obscureText;
                  });
                },
              )
                  : isValid != null && isValid
                  ? Icon(
                Icons.check_circle,
                color: ColorPalette.primary,
              ).animate(
                onPlay: (controller) => controller.repeat(),
              ).scale(
                duration: 400.ms,
                curve: Curves.easeInOut,
              ).then().shake(
                duration: 400.ms,
                rotation: 0.1,
              )
                  : null,
            ),
            onChanged: onChanged,
          ).animate()
              .fadeIn(duration: 600.ms, delay: isPassword ? 400.ms : 200.ms)
              .slideX(begin: 0.2, duration: 600.ms, delay: isPassword ? 400.ms : 200.ms)
              .shimmer(duration: 1200.ms, delay: isPassword ? 400.ms : 200.ms),
        ),
        if (showPrefixError != null && showPrefixError && controller.text.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(left: 8.0, top: 4.0),
            child: Text(
              'Phone number must start with 07, 01, or 254',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final keyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;

    return Scaffold(
      backgroundColor: isDark ? theme.scaffoldBackgroundColor : ColorPalette.greyBackground,
      body: SafeArea(
        child: Stack(
          children: [
            SingleChildScrollView(
              padding: EdgeInsets.only(bottom: keyboardVisible ? 0 : 100.0),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(height: 40),
                    Text(
                      'Welcome',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: isDark ? Colors.white : Colors.black,
                      ),
                    )
                        .animate()
                        .fadeIn(duration: 800.ms)
                        .slideY(begin: 0.2, duration: 800.ms),
                    Text(
                      'to ${ClientThemeManager().currentClientConfig.displayName}',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: isDark ? Colors.white : Colors.black,
                      ),
                    )
                        .animate()
                        .fadeIn(duration: 800.ms, delay: 200.ms)
                        .slideY(begin: 0.2, duration: 800.ms),
                    const SizedBox(height: 20),
                    Container(
                      width: 80,
                      height: 80,
                      child: ThemeHelper.getClientLogo(
                        width: 80,
                        height: 80,
                        fit: BoxFit.contain,
                      ),
                    ),
                    const SizedBox(height: 40),
                    Text(
                      'Login',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: isDark ? Colors.white : Colors.black,
                      ),
                    ),
                    const SizedBox(height: 20),
                    _buildTextField(
                      label: 'Mobile Number',
                      controller: _mobileNumberController,
                      onChanged: (value) {
                        final isValidPrefix = value.startsWith('07') || value.startsWith('01') || value.startsWith('254');
                        setState(() {
                          _isValidPhone = _validatePhone(value);
                          _showPrefixError = !isValidPrefix && value.isNotEmpty;
                        });
                      },
                      showPrefixError: _showPrefixError,
                      isValid: _isValidPhone,
                    ),
                    const SizedBox(height: 20),
                    _buildTextField(
                      label: 'Pin',
                      controller: _pinController,
                      isPassword: true,
                      bottomMargin: 5.0,
                      onChanged: (value) {
                        setState(() {
                          _isValidPin = _validatePin(value);
                        });
                      },
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                        onPressed: _handleForgotPin,
                        child: Text(
                          'Forgot Pin?',
                          style: TextStyle(
                            color: isDark ? Colors.white : Colors.black,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 1),
                    Container(
                      width: double.infinity,
                      height: 50,
                      decoration: BoxDecoration(
                        color: (_isValidPhone && _isValidPin && !_isLoading)
                            ? const Color(0xFF6B4E71)
                            : Colors.grey,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TextButton(
                        onPressed: (_isValidPhone && _isValidPin && !_isLoading)
                            ? _handleLogin
                            : null,
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2.0,
                                ),
                              )
                            : Text(
                                'LOGIN',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(height: 30),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: _handleAtmPress,
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 15),
                              decoration: BoxDecoration(
                                color: const Color(0xFF6B4E71),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.location_on,
                                    color: isDark ? Colors.white : Colors.white,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'ATMs',
                                    style: TextStyle(
                                      color: isDark ? Colors.white : Colors.white,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: InkWell(
                            onTap: _handleCalcPress,
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 15),
                              decoration: BoxDecoration(
                                color: const Color(0xFF6B4E71),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.calculate,
                                    color: isDark ? Colors.white : Colors.white,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Calculator',
                                    style: TextStyle(
                                      color: isDark ? Colors.white : Colors.white,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            if (!keyboardVisible)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: AnimatedBuilder(
                  animation: _waveAnimation,
                  builder: (context, child) {
                    return ClipPath(
                      clipper: WaveClipper(_waveAnimation.value),
                      child: Container(
                        height: 100,
                        color: ColorPalette.secondary,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Text(
                              'Don\'t Have an Account?',
                              style: TextStyle(
                                fontSize: 14,
                                color: isDark ? Colors.white : Colors.white,
                              ),
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const RegistrationScreen(),
                                  ),
                                );
                              },
                              style: TextButton.styleFrom(
                                foregroundColor: isDark ? Colors.white : Colors.white,
                              ),
                              child: Text(
                                'Self Register',
                                style: TextStyle(
                                  decoration: TextDecoration.underline,
                                  color: isDark ? Colors.white : Colors.white,
                                ),
                              ),
                            ),
                            const SizedBox(height: 10),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _waveController.dispose();
    _emailController.dispose();
    _mobileNumberController.dispose();
    _pinController.dispose();
    super.dispose();
  }
}

class WaveClipper extends CustomClipper<Path> {
  final double animationValue;

  WaveClipper(this.animationValue);

  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.lineTo(size.width, 30);

    // Animated control points
    final waveHeight = 15.0 * (1 + animationValue * 0.5);
    final waveOffset = 30.0 * animationValue;

    var firstControlPoint = Offset(size.width * 0.75, waveOffset);
    var firstEndPoint = Offset(size.width * 0.5, waveHeight);
    path.quadraticBezierTo(
      firstControlPoint.dx,
      firstControlPoint.dy,
      firstEndPoint.dx,
      firstEndPoint.dy,
    );

    var secondControlPoint = Offset(size.width * 0.25, 30 - waveOffset);
    var secondEndPoint = Offset(0, waveHeight);
    path.quadraticBezierTo(
      secondControlPoint.dx,
      secondControlPoint.dy,
      secondEndPoint.dx,
      secondEndPoint.dy,
    );

    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => true;
}