import 'package:package_info_plus/package_info_plus.dart';

class Utils {
  /// Get the current app version
  static Future<String> getAppVersion() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.version;
    } catch (e) {
      return '1.0.0'; // Default version if package info fails
    }
  }

  /// Get the current app build number
  static Future<String> getBuildNumber() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.buildNumber;
    } catch (e) {
      return '1'; // Default build number if package info fails
    }
  }

  /// Get the app name
  static Future<String> getAppName() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.appName;
    } catch (e) {
      return 'Sacco App'; // Default app name if package info fails
    }
  }
} 