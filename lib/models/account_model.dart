import 'package:flutter/material.dart';

class AccountModel {
  final String name;
  final String balance;
  final Color backgroundColor;
  final IconData icon;
  final List<String> allowedActions;
  final String? accountNo;
  final Map<String, dynamic>? rawAccount;

  const AccountModel({
    required this.name,
    required this.balance,
    required this.backgroundColor,
    required this.icon,
    required this.allowedActions,
    this.accountNo,
    this.rawAccount,
  });
}
