import 'form_field_model.dart';

class FormJourneyModel {
  final String type;
  final List<FormFieldModel> fieldGroup;

  FormJourneyModel({required this.type, required this.fieldGroup});

  factory FormJourneyModel.fromJson(Map<String, dynamic> json) {
    return FormJourneyModel(
      type: json['type'] ?? 'form',
      fieldGroup: List.from(json['fieldGroup'] ?? [])
          .map((field) => FormFieldModel.fromJson(field))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'fieldGroup': fieldGroup.map((f) => f.toJson()).toList(),
    };
  }
} 