/// Model class for representing transaction data from the API
class TransactionModel {
  final String title;
  final double amount;
  final int timeCompleted;
  final bool isPositive;

  const TransactionModel({
    required this.title,
    required this.amount,
    required this.timeCompleted,
    required this.isPositive,
  });

  /// Create TransactionModel from JSON response
  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    final double amount = (json['amount'] ?? 0).toDouble();

    // Handle timeCompleted - if null, use current time
    int timeCompleted;
    if (json['timeCompleted'] != null) {
      timeCompleted = json['timeCompleted'];
    } else {
      // For transactions without timestamp, use current time
      timeCompleted = DateTime.now().millisecondsSinceEpoch;
    }

    return TransactionModel(
      title: json['title']?.toString() ?? 'Unknown Transaction',
      amount: amount,
      timeCompleted: timeCompleted,
      isPositive: amount > 0,
    );
  }

  /// Convert TransactionModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'amount': amount,
      'timeCompleted': timeCompleted,
      'isPositive': isPositive,
    };
  }

  /// Get formatted amount string with currency
  String get formattedAmount {
    // For zero amounts (like balance enquiries), don't show currency
    if (amount == 0) {
      return 'N/A';
    }

    final String amountStr = 'KES ${amount.toStringAsFixed(2)}';
    return isPositive ? '+$amountStr' : '-$amountStr';
  }

  /// Get formatted date string
  String get formattedDate {
    try {
      final DateTime date = DateTime.fromMillisecondsSinceEpoch(timeCompleted);
      return "${_getMonthAbbr(date.month)} ${date.day}, ${date.year} ${_formatTime(date.hour, date.minute)}";
    } catch (e) {
      return 'Date not available';
    }
  }

  /// Helper method to get month abbreviation
  String _getMonthAbbr(int month) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[month - 1];
  }

  /// Helper method to format time
  String _formatTime(int hour, int minute) {
    final period = hour >= 12 ? 'PM' : 'AM';
    final hour12 = hour > 12 ? hour - 12 : hour == 0 ? 12 : hour;
    final minuteStr = minute.toString().padLeft(2, '0');
    return '$hour12:$minuteStr $period';
  }

  @override
  String toString() {
    return 'TransactionModel(title: $title, amount: $amount, timeCompleted: $timeCompleted, isPositive: $isPositive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransactionModel &&
        other.title == title &&
        other.amount == amount &&
        other.timeCompleted == timeCompleted &&
        other.isPositive == isPositive;
  }

  @override
  int get hashCode {
    return title.hashCode ^
        amount.hashCode ^
        timeCompleted.hashCode ^
        isPositive.hashCode;
  }
}
