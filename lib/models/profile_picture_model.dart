import 'dart:typed_data';

/// Model class for profile picture data
class ProfilePictureModel {
  final String? base64Image;
  final Uint8List? imageBytes;
  final String? imagePath;
  final DateTime? lastUpdated;
  final bool isFromServer;

  const ProfilePictureModel({
    this.base64Image,
    this.imageBytes,
    this.imagePath,
    this.lastUpdated,
    this.isFromServer = false,
  });

  /// Creates a ProfilePictureModel from base64 string
  factory ProfilePictureModel.fromBase64(String base64Image) {
    return ProfilePictureModel(
      base64Image: base64Image,
      lastUpdated: DateTime.now(),
      isFromServer: true,
    );
  }

  /// Creates a ProfilePictureModel from image bytes
  factory ProfilePictureModel.fromBytes(Uint8List imageBytes) {
    return ProfilePictureModel(
      imageBytes: imageBytes,
      lastUpdated: DateTime.now(),
      isFromServer: true,
    );
  }

  /// Creates a ProfilePictureModel from local file path
  factory ProfilePictureModel.fromPath(String imagePath) {
    return ProfilePictureModel(
      imagePath: imagePath,
      lastUpdated: DateTime.now(),
      isFromServer: false,
    );
  }

  /// Converts the model to JSON for API requests
  Map<String, dynamic> toJson() {
    return {
      'base64Image': base64Image,
      'imagePath': imagePath,
      'lastUpdated': lastUpdated?.toIso8601String(),
      'isFromServer': isFromServer,
    };
  }

  /// Creates a ProfilePictureModel from JSON response
  factory ProfilePictureModel.fromJson(Map<String, dynamic> json) {
    return ProfilePictureModel(
      base64Image: json['base64Image'],
      imagePath: json['imagePath'],
      lastUpdated: json['lastUpdated'] != null 
          ? DateTime.parse(json['lastUpdated']) 
          : null,
      isFromServer: json['isFromServer'] ?? false,
    );
  }

  /// Creates a copy of this model with updated fields
  ProfilePictureModel copyWith({
    String? base64Image,
    Uint8List? imageBytes,
    String? imagePath,
    DateTime? lastUpdated,
    bool? isFromServer,
  }) {
    return ProfilePictureModel(
      base64Image: base64Image ?? this.base64Image,
      imageBytes: imageBytes ?? this.imageBytes,
      imagePath: imagePath ?? this.imagePath,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isFromServer: isFromServer ?? this.isFromServer,
    );
  }

  @override
  String toString() {
    return 'ProfilePictureModel(hasBase64: ${base64Image != null}, hasBytes: ${imageBytes != null}, imagePath: $imagePath, lastUpdated: $lastUpdated, isFromServer: $isFromServer)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ProfilePictureModel &&
        other.base64Image == base64Image &&
        other.imagePath == imagePath &&
        other.lastUpdated == lastUpdated &&
        other.isFromServer == isFromServer;
  }

  @override
  int get hashCode {
    return base64Image.hashCode ^
        imagePath.hashCode ^
        lastUpdated.hashCode ^
        isFromServer.hashCode;
  }
}

/// Response model for profile picture API operations
class ProfilePictureResponse {
  final bool success;
  final String? message;
  final ProfilePictureModel? profilePicture;
  final String? errorCode;

  const ProfilePictureResponse({
    required this.success,
    this.message,
    this.profilePicture,
    this.errorCode,
  });

  /// Creates a successful response
  factory ProfilePictureResponse.success({
    String? message,
    ProfilePictureModel? profilePicture,
  }) {
    return ProfilePictureResponse(
      success: true,
      message: message,
      profilePicture: profilePicture,
    );
  }

  /// Creates an error response
  factory ProfilePictureResponse.error({
    required String message,
    String? errorCode,
  }) {
    return ProfilePictureResponse(
      success: false,
      message: message,
      errorCode: errorCode,
    );
  }

  /// Creates a ProfilePictureResponse from API response
  factory ProfilePictureResponse.fromApiResponse(Map<String, dynamic> response) {
    final bool isSuccess = response['responseCode'] == '00' || 
                          response['status'] == 'success';
    
    if (isSuccess && response['profilePicture'] != null) {
      return ProfilePictureResponse.success(
        message: response['message'],
        profilePicture: ProfilePictureModel.fromBase64(response['profilePicture']),
      );
    } else if (isSuccess) {
      return ProfilePictureResponse.success(
        message: response['message'] ?? 'Operation completed successfully',
      );
    } else {
      return ProfilePictureResponse.error(
        message: response['message'] ?? 'Unknown error occurred',
        errorCode: response['responseCode'] ?? response['errorCode'],
      );
    }
  }

  @override
  String toString() {
    return 'ProfilePictureResponse(success: $success, message: $message, hasProfilePicture: ${profilePicture != null}, errorCode: $errorCode)';
  }
}
