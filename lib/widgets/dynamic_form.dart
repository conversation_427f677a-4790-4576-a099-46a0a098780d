import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../configuration/client_config.dart';
import '../models/form_field_model.dart';

import '../utils/app_logger.dart';
class DynamicForm extends StatefulWidget {
  final List<FormFieldModel> fields;
  final void Function(String, dynamic) onChanged;
  final Map<String, dynamic> formData;

  const DynamicForm({
    Key? key,
    required this.fields,
    required this.onChanged,
    required this.formData,
  }) : super(key: key);

  @override
  _DynamicFormState createState() => _DynamicFormState();
}

class _DynamicFormState extends State<DynamicForm> {
  late Map<String, dynamic> localFormData;
  Map<String, String?> fieldErrors = {};
  Map<String, TextEditingController> controllers = {};
  Map<String, List<String>> fieldDependencies = {};
  Map<String, bool> fieldVisibility = {};

  ClientColorPalette get colors => ClientThemeManager().colors;

  @override
  void initState() {
    super.initState();
    localFormData = Map<String, dynamic>.from(widget.formData);
    _initializeControllers(widget.fields);
    _buildDependencyMap(widget.fields);
    _updateFieldVisibility();
  }

  void _initializeControllers(List<FormFieldModel> fields) {
    for (var field in fields) {
      if (field.fieldGroup != null && field.fieldGroup!.isNotEmpty) {
        _initializeControllers(field.fieldGroup!);
        continue;
      }

      if (field.fieldArray != null && field.fieldArray!.isNotEmpty) {
        _initializeControllers(field.fieldArray!);
        continue;
      }

      if (['input', 'currency', 'password'].contains(field.type.toLowerCase()) ||
          ['text', 'number', 'currency', 'password'].contains(field.templateOptions['type'])) {
        controllers[field.key] = TextEditingController(
            text: widget.formData[field.key]?.toString() ?? ''
        );
      }
    }
  }

  void _buildDependencyMap(List<FormFieldModel> fields) {
    fieldDependencies.clear();
    for (var field in fields) {
      if (field.fieldGroup != null && field.fieldGroup!.isNotEmpty) {
        _buildDependencyMap(field.fieldGroup!);
        continue;
      }

      if (field.fieldArray != null && field.fieldArray!.isNotEmpty) {
        _buildDependencyMap(field.fieldArray!);
      }

      if (field.hideExpression != null) {
        List<String> dependencies = _extractDependenciesFromExpression(field.hideExpression!);
        if (dependencies.isNotEmpty) {
          fieldDependencies[field.key] = dependencies;
        }
      }
    }
  }

  List<String> _extractDependenciesFromExpression(String expression) {
    List<String> dependencies = [];
    RegExp regex = RegExp(r'model\.(\w+)');
    Iterable<RegExpMatch> matches = regex.allMatches(expression);

    for (RegExpMatch match in matches) {
      String fieldName = match.group(1)!;
      if (!dependencies.contains(fieldName)) {
        dependencies.add(fieldName);
      }
    }
    return dependencies;
  }

  void _updateFieldVisibility() {
    Map<String, bool> previousVisibility = Map.from(fieldVisibility);
    List<FormFieldModel> allFields = _getAllFieldsFlat(widget.fields);

    for (var field in allFields) {
      bool shouldHide = field.shouldHide(localFormData);
      fieldVisibility[field.key] = !shouldHide;

      if (previousVisibility[field.key] != fieldVisibility[field.key]) {
        AppLogger.info("Visibility changed for ${field.key}: ${!shouldHide}");
      }
    }
  }

  List<FormFieldModel> _getAllFieldsFlat(List<FormFieldModel> fields) {
    List<FormFieldModel> flatFields = [];
    for (var field in fields) {
      if (field.fieldGroup != null && field.fieldGroup!.isNotEmpty) {
        flatFields.addAll(_getAllFieldsFlat(field.fieldGroup!));
      } else if (field.fieldArray != null && field.fieldArray!.isNotEmpty) {
        flatFields.addAll(_getAllFieldsFlat(field.fieldArray!));
      } else {
        flatFields.add(field);
      }
    }
    return flatFields;
  }

  void _handleFieldChange(FormFieldModel field, dynamic value) {
    setState(() {
      localFormData[field.key] = value;
      
      if (field.isRequiredForFormData(localFormData)) {
        if (value != null && value.toString().isNotEmpty) {
          fieldErrors.remove(field.key);
        } else {
          fieldErrors[field.key] = 'This field is required';
        }
      }

      if (fieldDependencies.containsKey(field.key)) {
        _updateFieldVisibility();
      }
    });

    widget.onChanged(field.key, value);
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: _buildFormFields(widget.fields));
  }

  List<Widget> _buildFormFields(List<FormFieldModel> fields) {
    List<Widget> formWidgets = [];

    for (var field in fields) {
      if (field.fieldGroup != null && field.fieldGroup!.isNotEmpty) {
        formWidgets.add(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (field.templateOptions.containsKey('label') &&
                  field.templateOptions['label'] != null)
                Padding(
                  padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
                  child: Text(
                    field.templateOptions['label'],
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: colors.primary,
                      fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
                    ),
                  ),
                ),
              ..._buildFormFields(field.fieldGroup!),
            ],
          ),
        );
        continue;
      }

      if (field.shouldHide(localFormData)) {
        continue;
      }

      Widget fieldWidget;
      switch (field.type.toLowerCase()) {
        case 'select':
          fieldWidget = _buildSelectField(field);
          break;
        case 'input':
          if (field.templateOptions['type'] == 'date') {
            fieldWidget = _buildDateField(field);
          } else {
            fieldWidget = _buildInputField(field);
          }
          break;
        case 'currency':
          fieldWidget = _buildCurrencyField(field);
          break;
        case 'password':
          fieldWidget = _buildPasswordField(field);
          break;
        case 'radio':
          fieldWidget = _buildRadioField(field);
          break;
        default:
          if (field.templateOptions['type'] == 'currency') {
            fieldWidget = _buildCurrencyField(field);
          } else if (field.templateOptions['type'] == 'password') {
            fieldWidget = _buildPasswordField(field);
          } else {
            fieldWidget = _buildInputField(field);
          }
      }

      formWidgets.add(
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12.0),
          child: fieldWidget,
        ),
      );
    }

    return formWidgets;
  }

  Widget _buildSelectField(FormFieldModel field) {
    final theme = Theme.of(context);
    final isRequired = field.isRequiredForFormData(localFormData);
    final label = field.templateOptions['label'] ?? '';
    final options = field.templateOptions['options'] ?? [];
    final errorText = fieldErrors[field.key];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(label, isRequired, theme),
        const SizedBox(height: 5),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: colors.primary, width: 2.0),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: colors.error),
            ),
            errorText: errorText,
            filled: true,
            fillColor: colors.surface,
            hintText: 'Select an option',
          ),
          isExpanded: true,
          value: localFormData[field.key] as String?,
          items: (options as List<dynamic>)
              .map<DropdownMenuItem<String>>((option) => DropdownMenuItem<String>(
                    value: option['id']?.toString() ?? option['value']?.toString(),
                    child: Text(
                      option['name']?.toString() ?? option['label']?.toString() ?? '',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colors.textPrimary,
                        fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ))
              .toList(),
          onChanged: (value) => _handleFieldChange(field, value),
          style: theme.textTheme.bodyMedium?.copyWith(color: colors.textPrimary),
          dropdownColor: colors.surface,
          icon: Icon(Icons.arrow_drop_down, color: colors.textSecondary),
        ),
      ],
    );
  }

  Widget _buildInputField(FormFieldModel field) {
    final theme = Theme.of(context);
    final isRequired = field.isRequiredForFormData(localFormData);
    final isNumber = field.templateOptions['type'] == 'number';
    final label = field.templateOptions['label'] ?? '';
    final errorText = fieldErrors[field.key];

    if (!controllers.containsKey(field.key)) {
      controllers[field.key] = TextEditingController(
        text: localFormData[field.key]?.toString() ?? '',
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(label, isRequired, theme),
        const SizedBox(height: 5),
        TextField(
          controller: controllers[field.key],
          decoration: InputDecoration(
            hintText: field.templateOptions['placeholder'] ?? 'Enter ${label.toLowerCase()}',
            hintStyle: theme.textTheme.bodyMedium?.copyWith(color: colors.textSecondary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: colors.primary, width: 2.0),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: colors.error),
            ),
            errorText: errorText,
            filled: true,
            fillColor: colors.surface,
          ),
          style: theme.textTheme.bodyMedium?.copyWith(color: colors.textPrimary),
          keyboardType: isNumber ? TextInputType.number : TextInputType.text,
          inputFormatters: isNumber ? [FilteringTextInputFormatter.digitsOnly] : [],
          onChanged: (value) {
            final dynamic parsedValue = isNumber ? (double.tryParse(value) ?? value) : value;
            _handleFieldChange(field, parsedValue);
          },
        ),
      ],
    );
  }

  Widget _buildCurrencyField(FormFieldModel field) {
    final theme = Theme.of(context);
    final isRequired = field.isRequiredForFormData(localFormData);
    final label = field.templateOptions['label'] ?? '';
    final errorText = fieldErrors[field.key];

    if (!controllers.containsKey(field.key)) {
      controllers[field.key] = TextEditingController(
        text: localFormData[field.key]?.toString() ?? '',
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(label, isRequired, theme),
        const SizedBox(height: 5),
        TextField(
          controller: controllers[field.key],
          decoration: InputDecoration(
            hintText: 'Enter amount',
            hintStyle: theme.textTheme.bodyMedium?.copyWith(color: colors.textSecondary),
            prefixIcon: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              child: Text(
                'KES',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colors.primary,
                  fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
                ),
              ),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: colors.primary, width: 2.0),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: colors.error),
            ),
            errorText: errorText,
            filled: true,
            fillColor: colors.surface,
          ),
          style: theme.textTheme.bodyMedium?.copyWith(color: colors.textPrimary),
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          onChanged: (value) => _handleFieldChange(field, value),
        ),
      ],
    );
  }

  Widget _buildPasswordField(FormFieldModel field) {
    final theme = Theme.of(context);
    final isRequired = field.isRequiredForFormData(localFormData);
    final label = field.templateOptions['label'] ?? '';
    final errorText = fieldErrors[field.key];
    final maxLength = field.templateOptions['maxLength'] != null
        ? int.tryParse(field.templateOptions['maxLength'].toString())
        : null;

    if (!controllers.containsKey(field.key)) {
      controllers[field.key] = TextEditingController(
        text: localFormData[field.key]?.toString() ?? '',
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(label, isRequired, theme),
        const SizedBox(height: 5),
        TextField(
          controller: controllers[field.key],
          obscureText: true,
          decoration: InputDecoration(
            hintText: 'Enter ${label.toLowerCase()}',
            hintStyle: theme.textTheme.bodyMedium?.copyWith(color: colors.textSecondary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: colors.primary, width: 2.0),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: colors.error),
            ),
            errorText: errorText,
            filled: true,
            fillColor: colors.surface,
          ),
          style: theme.textTheme.bodyMedium?.copyWith(color: colors.textPrimary),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            if (maxLength != null) LengthLimitingTextInputFormatter(maxLength),
          ],
          onChanged: (value) => _handleFieldChange(field, value),
        ),
      ],
    );
  }

  Widget _buildRadioField(FormFieldModel field) {
    final isRequired = field.isRequiredForFormData(localFormData);
    final label = field.templateOptions['label'] ?? field.key;
    final options = field.templateOptions['options'] ?? [];
    final theme = Theme.of(context);
    final errorText = fieldErrors[field.key];

    if (options.isEmpty) {
      return Text('No options available for $label');
    }

    String? currentValue = localFormData[field.key]?.toString();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(label, isRequired, theme),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: currentValue,
          onChanged: (value) {
            if (value != null) {
              _handleFieldChange(field, value);
            }
          },
          items: options.map<DropdownMenuItem<String>>((option) {
            final String optionValue = option['key']?.toString() ??
                option['value']?.toString() ??
                option['id']?.toString() ?? '';
            final String optionLabel = option['value']?.toString() ??
                option['label']?.toString() ??
                option['name']?.toString() ?? optionValue;

            return DropdownMenuItem<String>(
              value: optionValue,
              child: Text(
                optionLabel,
                style: TextStyle(
                  fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
                ),
              ),
            );
          }).toList(),
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: colors.primary, width: 2.0),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(color: colors.error),
            ),
            errorText: errorText,
            filled: true,
            fillColor: colors.surface,
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            hintText: 'Select an option',
          ),
          hint: Text(
            'Select an option',
            style: TextStyle(
              fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
            ),
          ),
          isExpanded: true,
          icon: Icon(Icons.arrow_drop_down, color: colors.textSecondary),
        ),
      ],
    );
  }

  Widget _buildDateField(FormFieldModel field) {
    final theme = Theme.of(context);
    final isRequired = field.isRequiredForFormData(localFormData);
    final label = field.templateOptions['label'] ?? '';
    final errorText = fieldErrors[field.key];

    DateTime? selectedDate;
    if (localFormData[field.key] != null) {
      if (localFormData[field.key] is DateTime) {
        selectedDate = localFormData[field.key];
      } else if (localFormData[field.key] is String) {
        try {
          selectedDate = DateTime.parse(localFormData[field.key]);
        } catch (e) {
          selectedDate = null;
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(label, isRequired, theme),
        const SizedBox(height: 5),
        InkWell(
          onTap: () async {
            final pickedDate = await showDatePicker(
              context: context,
              initialDate: selectedDate ?? DateTime.now(),
              firstDate: DateTime(2000),
              lastDate: DateTime.now(),
              builder: (context, child) {
                return Theme(
                  data: theme.copyWith(
                    colorScheme: theme.colorScheme.copyWith(primary: colors.primary),
                  ),
                  child: child!,
                );
              },
            );
            if (pickedDate != null) {
              _handleFieldChange(field, pickedDate);
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 15.0, horizontal: 12.0),
            decoration: BoxDecoration(
              border: Border.all(
                color: errorText != null ? colors.error : theme.colorScheme.outline,
              ),
              borderRadius: BorderRadius.circular(10.0),
              color: colors.surface,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  selectedDate == null
                      ? 'Select Date'
                      : DateFormat('MM-dd-yyyy').format(selectedDate),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: selectedDate == null ? colors.textSecondary : colors.textPrimary,
                    fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
                  ),
                ),
                Icon(Icons.calendar_today, color: colors.textSecondary, size: 20),
              ],
            ),
          ),
        ),
        if (errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 6.0, left: 12.0),
            child: Text(
              errorText,
              style: TextStyle(
                color: colors.error,
                fontSize: 12,
                fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildFieldLabel(String label, bool isRequired, ThemeData theme) {
    return Row(
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colors.textSecondary,
            fontWeight: FontWeight.bold,
            fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
          ),
        ),
        if (isRequired)
          Text(
            ' *',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colors.error,
              fontWeight: FontWeight.bold,
              fontFamily: ClientThemeManager().currentClientConfig.fontFamily,
            ),
          ),
      ],
    );
  }

  @override
  void dispose() {
    controllers.values.forEach((controller) => controller.dispose());
    super.dispose();
  }
} 