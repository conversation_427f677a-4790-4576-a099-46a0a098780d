import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'app_logger.dart';

class SmsListener {
  static const MethodChannel _channel = MethodChannel('sms_listener');
  static Function(String)? _onSmsReceived;

  /// Initialize SMS listener
  static Future<bool> initialize() async {
    try {
      // Check permissions
      final smsPermission = await Permission.sms.status;
      if (!smsPermission.isGranted) {
        AppLogger.warning('SMS permission not granted');
        return false;
      }

      // Set up method channel handler
      _channel.setMethodCallHandler(_handleMethodCall);
      
      // Initialize native SMS listener
      final result = await _channel.invokeMethod('initializeSmsListener');
      AppLogger.info('SMS listener initialized: $result');
      return result == true;
    } catch (e) {
      AppLogger.error('Failed to initialize SMS listener: $e');
      return false;
    }
  }

  /// Start listening for SMS
  static Future<bool> startListening(Function(String) onSmsReceived) async {
    try {
      _onSmsReceived = onSmsReceived;
      final result = await _channel.invokeMethod('startListening');
      AppLogger.info('SMS listener started: $result');
      return result == true;
    } catch (e) {
      AppLogger.error('Failed to start SMS listener: $e');
      return false;
    }
  }

  /// Stop listening for SMS
  static Future<bool> stopListening() async {
    try {
      _onSmsReceived = null;
      final result = await _channel.invokeMethod('stopListening');
      AppLogger.info('SMS listener stopped: $result');
      return result == true;
    } catch (e) {
      AppLogger.error('Failed to stop SMS listener: $e');
      return false;
    }
  }

  /// Handle method calls from native
  static Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onSmsReceived':
        final smsBody = call.arguments as String;
        AppLogger.info('SMS received: $smsBody');
        
        // Extract OTP
        final otp = _extractOtpFromSms(smsBody);
        if (otp != null && _onSmsReceived != null) {
          _onSmsReceived!(otp);
        }
        break;
      default:
        AppLogger.warning('Unknown method call: ${call.method}');
    }
  }

  /// Extract OTP from SMS body
  static String? _extractOtpFromSms(String smsBody) {
    final otpPatterns = [
      RegExp(r'\b(\d{4})\b'),
      RegExp(r'\b(\d{6})\b'),
      RegExp(r'(?:otp|code|verification|pin):\s*(\d{4,6})', caseSensitive: false),
    ];

    for (final pattern in otpPatterns) {
      final match = pattern.firstMatch(smsBody);
      if (match != null) {
        final code = match.group(1) ?? match.group(0);
        if (code != null && (code.length == 4 || code.length == 6)) {
          return code;
        }
      }
    }

    return null;
  }
} 