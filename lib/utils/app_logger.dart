import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// Centralized logging utility for the application
/// 
/// This class provides a consistent logging interface throughout the app.
/// It uses the logger package with nice emojis and different log levels.
/// 
/// Usage:
/// ```dart
/// AppLogger.debug('Debug message');
/// AppLogger.info('Info message');
/// AppLogger.warning('Warning message');
/// AppLogger.error('Error message');
/// AppLogger.wtf('What a terrible failure message');
/// ```
class AppLogger {
  // Toggle this to disable verbose logging even in debug mode
  static const bool _enableVerboseLogging = true; // Set to true to see all logs
  
  // Configure logging level based on build mode
  static Level get _logLevel {
    if (kDebugMode && _enableVerboseLogging) {
      return Level.debug; // Show all logs in debug mode
    } else if (kDebugMode) {
      return Level.warning; // Show only warnings and errors in debug mode when verbose is disabled
    } else {
      return Level.warning; // Only show warnings and errors in production
    }
  }

  // Categories to suppress in production for cleaner logs
  static const List<String> _suppressedCategories = [
    'TOKEN REFRESH',
    'TRANSACTION',
    'API',
    'CACHE',
    'PERIODIC',
  ];

  // Check if a log message should be suppressed based on category
  static bool _shouldSuppressLog(String message) {
    if (kDebugMode) return false; // Never suppress in debug mode
    
    for (String category in _suppressedCategories) {
      if (message.contains(category)) {
        return true;
      }
    }
    return false;
  }

  static final Logger _logger = Logger(
    level: _logLevel,
    printer: PrettyPrinter(
      methodCount: kDebugMode ? 2 : 0, // Show stack trace only in debug
      errorMethodCount: 8, // Number of method calls to show when errors occur
      lineLength: 120, // Width of the output
      colors: true, // Colorful log messages
      printEmojis: kDebugMode, // Print emojis only in debug mode
      printTime: true, // Print timestamp for each log message
    ),
  );

  /// Log a debug message
  /// Use for detailed information when diagnosing problems
  static void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    if (_shouldSuppressLog(message)) return;
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  /// Log an info message
  /// Use for general information about app flow
  static void info(String message, [dynamic error, StackTrace? stackTrace]) {
    if (_shouldSuppressLog(message)) return;
    //_logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log a warning message
  /// Use for potentially harmful situations
  static void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Log an error message
  /// Use for error events that might allow the app to continue running
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Log a "What a Terrible Failure" message
  /// Use for errors that should never happen
  static void wtf(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }

  /// Log a trace message
  /// Use for very detailed information
  static void trace(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.t(message, error: error, stackTrace: stackTrace);
  }

  /// Get the underlying logger instance if needed for advanced usage
  static Logger get instance => _logger;
}

/// Extension methods for easier logging in different contexts
extension LoggerExtensions on Object {
  /// Log debug message with class context
  void logDebug(String message, [dynamic error, StackTrace? stackTrace]) {
    AppLogger.debug('[$runtimeType] $message', error, stackTrace);
  }

  /// Log info message with class context
  void logInfo(String message, [dynamic error, StackTrace? stackTrace]) {
    AppLogger.info('[$runtimeType] $message', error, stackTrace);
  }

  /// Log warning message with class context
  void logWarning(String message, [dynamic error, StackTrace? stackTrace]) {
    AppLogger.warning('[$runtimeType] $message', error, stackTrace);
  }

  /// Log error message with class context
  void logError(String message, [dynamic error, StackTrace? stackTrace]) {
    AppLogger.error('[$runtimeType] $message', error, stackTrace);
  }

  /// Log wtf message with class context
  void logWtf(String message, [dynamic error, StackTrace? stackTrace]) {
    AppLogger.wtf('[$runtimeType] $message', error, stackTrace);
  }
}
