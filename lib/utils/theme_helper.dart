import 'package:flutter/material.dart';
import '../configuration/client_config.dart';

/// Helper class to provide easy access to client-specific theme configurations
class ThemeHelper {
  // Private constructor to prevent instantiation
  ThemeHelper._();

  /// Get the current client colors
  static ClientColorPalette get colors => ClientThemeManager().colors;

  /// Get the current client config
  static ClientConfig get clientConfig => ClientThemeManager().currentClientConfig;

  /// Get the current client ID
  static String get clientId => ClientThemeManager().currentClientId;

  /// Get a themed TextStyle with client-specific font family
  static TextStyle getTextStyle(TextStyle? baseStyle) {
    return (baseStyle ?? const TextStyle()).copyWith(
      fontFamily: clientConfig.fontFamily,
    );
  }

  /// Get a themed AppBar with client colors
  static AppBar getThemedAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
    double? toolbarHeight,
  }) {
    return AppBar(
      backgroundColor: colors.primary,
      centerTitle: centerTitle,
      title: Text(
        title,
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontFamily: clientConfig.fontFamily,
        ),
      ),
      leading: leading,
      actions: actions,
      toolbarHeight: toolbarHeight,
      iconTheme: const IconThemeData(color: Colors.white),
    );
  }

  /// Get a themed ElevatedButton
  static ElevatedButton getThemedButton({
    required String text,
    required VoidCallback? onPressed,
    bool isPrimary = true,
    EdgeInsetsGeometry? padding,
    double borderRadius = 20,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isPrimary ? colors.primary : colors.secondary,
        disabledBackgroundColor: colors.primary.withValues(alpha: 0.4),
        padding: padding ?? const EdgeInsets.symmetric(vertical: 15),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 16,
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontFamily: clientConfig.fontFamily,
        ),
      ),
    );
  }

  /// Get a themed InputDecoration
  static InputDecoration getThemedInputDecoration({
    String? hintText,
    String? labelText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    String? errorText,
    bool filled = true,
  }) {
    return InputDecoration(
      hintText: hintText,
      labelText: labelText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      errorText: errorText,
      filled: filled,
      fillColor: colors.surface,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0),
        borderSide: BorderSide(color: colors.textSecondary),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0),
        borderSide: BorderSide(color: colors.textSecondary),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0),
        borderSide: BorderSide(
          color: colors.primary,
          width: 2.0,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0),
        borderSide: BorderSide(color: colors.error),
      ),
      hintStyle: TextStyle(
        color: colors.textSecondary,
        fontFamily: clientConfig.fontFamily,
      ),
      labelStyle: TextStyle(
        color: colors.textSecondary,
        fontFamily: clientConfig.fontFamily,
      ),
    );
  }

  /// Get a themed Card
  static Card getThemedCard({
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    double elevation = 2,
  }) {
    return Card(
      color: colors.surface,
      elevation: elevation,
      margin: margin,
      child: Padding(
        padding: padding ?? const EdgeInsets.all(16.0),
        child: child,
      ),
    );
  }

  /// Get themed SnackBar
  static SnackBar getThemedSnackBar({
    required String message,
    bool isError = false,
    SnackBarAction? action,
  }) {
    return SnackBar(
      content: Text(
        message,
        style: TextStyle(
          fontFamily: clientConfig.fontFamily,
          color: Colors.white,
        ),
      ),
      backgroundColor: isError ? colors.error : colors.success,
      action: action,
    );
  }

  /// Get themed CircularProgressIndicator
  static CircularProgressIndicator getThemedProgressIndicator({
    double? value,
  }) {
    return CircularProgressIndicator(
      value: value,
      valueColor: AlwaysStoppedAnimation<Color>(colors.primary),
    );
  }

  /// Get themed Divider
  static Divider getThemedDivider({
    double height = 1,
    double thickness = 1,
  }) {
    return Divider(
      height: height,
      thickness: thickness,
      color: colors.textDisabled,
    );
  }

  /// Get themed modal dialog
  static Widget getThemedModal({
    required Widget child,
    bool dismissible = true,
    EdgeInsetsGeometry? padding,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: colors.modalBackground,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: padding ?? const EdgeInsets.all(20),
      child: child,
    );
  }

  /// Get modal background color
  static Color get modalBackgroundColor => colors.modalBackground;

  /// Get client logo widget
  static Widget getClientLogo({
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
  }) {
    final logoPath = clientConfig.logoAsset;
    if (logoPath != null) {
      return Image.asset(
        logoPath,
        width: width,
        height: height,
        fit: fit,
        errorBuilder: (context, error, stackTrace) {
          // Fallback to a placeholder if logo can't be loaded
          return Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: colors.primary,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.business,
              color: Colors.white,
              size: (width != null && height != null) ? (width < height ? width : height) * 0.6 : 40,
            ),
          );
        },
      );
    } else {
      return Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: colors.primary,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.business,
          color: Colors.white,
          size: (width != null && height != null) ? (width < height ? width : height) * 0.6 : 40,
        ),
      );
    }
  }
} 