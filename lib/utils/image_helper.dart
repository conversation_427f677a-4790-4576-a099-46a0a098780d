import 'dart:typed_data';
import 'app_logger.dart';
import 'package:image/image.dart' as img;

/// Validates if the given bytes represent a valid image.
///
/// This function attempts to decode the byte data. If it succeeds, the data
/// is considered a valid image.
///
/// Returns `true` if the image data is valid, `false` otherwise.
bool isValidImage(Uint8List imageBytes) {
  try {
    // Attempt to decode the image. If this doesn't throw, it's a valid image.
    final image = img.decodeImage(imageBytes);
    // You can add more checks here, e.g., for minimum dimensions
    return image != null;
  } catch (e) {
    AppLogger.info('Image validation failed: $e');
    return false;
  }
} 