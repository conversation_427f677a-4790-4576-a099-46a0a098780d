import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'app_logger.dart';

class SmsAutofillHelper {
  static const String _channelName = 'sms_autofill_debug';
  static const MethodChannel _channel = MethodChannel(_channelName);

  /// Check SMS autofill compatibility and permissions
  static Future<Map<String, dynamic>> getAutofillStatus() async {
    Map<String, dynamic> status = {
      'smsPermission': false,
      'phonePermission': false,
      'androidVersion': 'Unknown',
      'autofillSupported': false,
      'errors': <String>[],
    };

    try {
      // Check SMS permission
      final smsPermission = await Permission.sms.status;
      status['smsPermission'] = smsPermission.isGranted;

      // Check phone permission (needed for Android 15)
      final phonePermission = await Permission.phone.status;
      status['phonePermission'] = phonePermission.isGranted;

      // Get Android version
      if (defaultTargetPlatform == TargetPlatform.android) {
        try {
          final androidInfo = await _channel.invokeMethod('getAndroidInfo');
          status['androidVersion'] = androidInfo['version'];
          status['sdkInt'] = androidInfo['sdkInt'];
          status['autofillSupported'] = androidInfo['sdkInt'] >= 26; // Android 8.0+
        } catch (e) {
          status['errors'].add('Failed to get Android info: $e');
        }
      }

      AppLogger.info('SMS Autofill Status: $status');
      return status;
    } catch (e) {
      status['errors'].add('Error checking autofill status: $e');
      AppLogger.error('SMS Autofill Status Error: $e');
      return status;
    }
  }

  /// Request necessary permissions for SMS autofill
  static Future<bool> requestAutofillPermissions() async {
    try {
      // Request SMS permission
      final smsStatus = await Permission.sms.request();
      
      // Request phone permission for Android 15
      final phoneStatus = await Permission.phone.request();
      
      final granted = smsStatus.isGranted && phoneStatus.isGranted;
      
      AppLogger.info('SMS Autofill Permissions - SMS: ${smsStatus.isGranted}, Phone: ${phoneStatus.isGranted}');
      
      return granted;
    } catch (e) {
      AppLogger.error('Error requesting SMS autofill permissions: $e');
      return false;
    }
  }

  /// Log SMS autofill debugging information
  static void logAutofillDebugInfo(String context) {
    AppLogger.info('SMS Autofill Debug - Context: $context');
    getAutofillStatus().then((status) {
      AppLogger.info('SMS Autofill Debug Status: $status');
    });
  }

  /// Common SMS OTP patterns for validation
  static bool isValidOtpSms(String smsBody) {
    // Common OTP patterns
    final otpPatterns = [
      RegExp(r'\b\d{4}\b'),           // 4 digit code
      RegExp(r'\b\d{6}\b'),           // 6 digit code
      RegExp(r'code:\s*(\d{4,6})', caseSensitive: false),
      RegExp(r'otp:\s*(\d{4,6})', caseSensitive: false),
      RegExp(r'verification:\s*(\d{4,6})', caseSensitive: false),
      RegExp(r'pin:\s*(\d{4,6})', caseSensitive: false),
    ];

    return otpPatterns.any((pattern) => pattern.hasMatch(smsBody));
  }

  /// Extract OTP from SMS body
  static String? extractOtpFromSms(String smsBody) {
    final otpPatterns = [
      RegExp(r'\b(\d{4})\b'),           // 4 digit code
      RegExp(r'\b(\d{6})\b'),           // 6 digit code
      RegExp(r'code:\s*(\d{4,6})', caseSensitive: false),
      RegExp(r'otp:\s*(\d{4,6})', caseSensitive: false),
      RegExp(r'verification:\s*(\d{4,6})', caseSensitive: false),
      RegExp(r'pin:\s*(\d{4,6})', caseSensitive: false),
    ];

    for (final pattern in otpPatterns) {
      final match = pattern.firstMatch(smsBody);
      if (match != null) {
        final code = match.group(1) ?? match.group(0);
        if (code != null && (code.length == 4 || code.length == 6)) {
          return code;
        }
      }
    }

    return null;
  }
} 