import 'package:shared_preferences/shared_preferences.dart';
import '../models/contact_details_model.dart';

import 'app_logger.dart';
class ContactDetailsHelper {
  // Keys for storing contact details
  static const String _clientNameKey = 'client_name';
  static const String _clientEmailKey = 'client_email';
  static const String _clientMsisdnKey = 'client_msisdn';
  static const String _clientWebsiteKey = 'client_website';
  static const String _clientPostalAddrKey = 'client_postal_addr';
  static const String _clientFacebookKey = 'client_facebook';
  static const String _clientXKey = 'client_x';
  static const String _clientInstagramKey = 'client_instagram';
  static const String _clientYoutubeKey = 'client_youtube';

  // Save contact details from spclients data
  static Future<void> saveContactDetails(Map<String, dynamic> spClientsData) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString(_clientNameKey, spClientsData['clientName'] ?? '');
    await prefs.setString(_clientEmail<PERSON><PERSON>, spClientsData['email'] ?? '');
    await prefs.setString(_clientMsisdnKey, spClientsData['msisdn'] ?? '');
    await prefs.setString(_clientWebsiteKey, spClientsData['website'] ?? '');
    await prefs.setString(_clientPostalAddrKey, spClientsData['postalAddr'] ?? '');
    await prefs.setString(_clientFacebookKey, spClientsData['facebook'] ?? '');
    await prefs.setString(_clientXKey, spClientsData['x'] ?? '');
    await prefs.setString(_clientInstagramKey, spClientsData['instagram'] ?? '');
    await prefs.setString(_clientYoutubeKey, spClientsData['youtube'] ?? '');

    AppLogger.info('Contact details saved successfully');
  }

  // Get contact details with fallbacks
  static Future<ContactDetails> getContactDetails() async {
    final prefs = await SharedPreferences.getInstance();

    return ContactDetails(
      name: prefs.getString(_clientNameKey) ?? 'Tower Sacco',
      email: prefs.getString(_clientEmailKey) ?? '<EMAIL>',
      phoneNumber: prefs.getString(_clientMsisdnKey) ?? '+254 792 333 111',
      website: prefs.getString(_clientWebsiteKey) ?? 'https://www.towersacco.co.ke/',
      postalAddress: prefs.getString(_clientPostalAddrKey) ?? 'OLKALOU TOWN, TOWER SACCO PLAZA\nAlong Olkalou-Nakuru Road\nP.O BOX 259-20303 OLKALOU',
      facebook: prefs.getString(_clientFacebookKey) ?? 'https://www.facebook.com/TowerSaccoLtd',
      x: prefs.getString(_clientXKey) ?? 'https://x.com/towersaccoltd?lang=en',
      instagram: prefs.getString(_clientInstagramKey) ?? 'https://www.instagram.com/towersacco.ltd/',
      youtube: prefs.getString(_clientYoutubeKey) ?? 'https://www.youtube.com/@towersaccoltd9864',
    );
  }

  // Clear contact details (useful for logout)
  static Future<void> clearContactDetails() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.remove(_clientNameKey);
    await prefs.remove(_clientEmailKey);
    await prefs.remove(_clientMsisdnKey);
    await prefs.remove(_clientWebsiteKey);
    await prefs.remove(_clientPostalAddrKey);
    await prefs.remove(_clientFacebookKey);
    await prefs.remove(_clientXKey);
    await prefs.remove(_clientInstagramKey);
    await prefs.remove(_clientYoutubeKey);
  }
} 