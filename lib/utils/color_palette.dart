import 'package:flutter/material.dart';
import '../configuration/client_config.dart';

class ColorPalette {
  // Get current client colors
  static ClientColorPalette get _clientColors {
    try {
      return ClientThemeManager().colors;
    } catch (e) {
      // Fallback to default colors if theme system not initialized
      return const ClientColorPalette(
        primary: Color(0xFF00A651),
        secondary: Color(0xFFEC008C),
        background: Color(0xFFF5F5F5),
        surface: Color(0xFFFFFFFF),
        error: Color(0xFFD32F2F),
        success: Color(0xFF43A047),
        warning: Color(0xFFFFA000),
        info: Color(0xFF1976D2),
        textPrimary: Color(0xFF212121),
        textSecondary: Color(0xFF757575),
        textDisabled: Color(0xFFBDBDBD),
        modalBackground: Color(0xFF6B4779), // Original modal purple
        unselectedNavItem: Color(0xFF6B4779), // Original unselected nav purple
      );
    }
  }

  // Client-aware colors that adapt based on current configuration
  static Color get selectedNavItemColor => _clientColors.primary;
  static Color get btnColor => _clientColors.secondary;
  static Color get primary => _clientColors.primary;
  static Color get secondary => _clientColors.secondary;
  static Color get tertiary => Colors.grey.shade100;
  static Color get pinkButton => _clientColors.secondary.withValues(alpha: 0.3);
  static Color get unselectedNavItemColor => _clientColors.unselectedNavItem;
  
  // Static colors that don't change
  static Color white = Colors.white;
  static Color black = Colors.black;
  static const cardLight = Color(0xFFFEFDFD);
  static const textDark = Colors.black87;
  static const textLight = Colors.white;
  static const danger = Colors.red;
  static const Color greyBackground = Color(0xFFEEEEEE);

  // Additional client-aware colors
  static Color get background => _clientColors.background;
  static Color get surface => _clientColors.surface;
  static Color get error => _clientColors.error;
  static Color get success => _clientColors.success;
  static Color get warning => _clientColors.warning;
  static Color get info => _clientColors.info;
  static Color get textPrimary => _clientColors.textPrimary;
  static Color get textSecondary => _clientColors.textSecondary;
  static Color get textDisabled => _clientColors.textDisabled;
  static Color get modalBackground => _clientColors.modalBackground;
}