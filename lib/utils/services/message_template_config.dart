import 'dart:convert';

import '../app_logger.dart';
class MessageTemplateConfig {
  final ConfirmationTemplate? confirmationTemplate;
  final SuccessTemplate? successTemplate;
  final ErrorTemplate? errorTemplate;

  MessageTemplateConfig({
    this.confirmationTemplate,
    this.successTemplate,
    this.errorTemplate,
  });

  factory MessageTemplateConfig.fromServiceConfig(Map<String, dynamic> serviceConfig) {
    ConfirmationTemplate? confirmationTemplate;
    SuccessTemplate? successTemplate;
    ErrorTemplate? errorTemplate;

    // Parse confirmation template
    if (serviceConfig.containsKey('confirmationTemplate')) {
      var confirmationData = serviceConfig['confirmationTemplate'];
      if (confirmationData is String) {
        try {
          confirmationData = jsonDecode(confirmationData);
        } catch (e) {
          AppLogger.error('parsing confirmation template: $e');
        }
      }
      if (confirmationData is Map) {
        confirmationTemplate = ConfirmationTemplate.fromJson(
          Map<String, dynamic>.from(confirmationData),
        );
      }
    }

    // Parse success template
    if (serviceConfig.containsKey('successTemplate')) {
      var successData = serviceConfig['successTemplate'];
      if (successData is String) {
        try {
          successData = jsonDecode(successData);
        } catch (e) {
          AppLogger.error('parsing success template: $e');
        }
      }
      if (successData is Map) {
        successTemplate = SuccessTemplate.fromJson(
          Map<String, dynamic>.from(successData),
        );
      }
    }

    // Parse error template
    if (serviceConfig.containsKey('errorTemplate')) {
      var errorData = serviceConfig['errorTemplate'];
      if (errorData is String) {
        try {
          errorData = jsonDecode(errorData);
        } catch (e) {
          AppLogger.error('parsing error template: $e');
        }
      }
      if (errorData is Map) {
        errorTemplate = ErrorTemplate.fromJson(
          Map<String, dynamic>.from(errorData),
        );
      }
    }

    return MessageTemplateConfig(
      confirmationTemplate: confirmationTemplate,
      successTemplate: successTemplate,
      errorTemplate: errorTemplate,
    );
  }
}

class ConfirmationTemplate {
  final String title;
  final String subtitle;
  final Map<String, String> fieldMappings;

  ConfirmationTemplate({
    required this.title,
    required this.subtitle,
    required this.fieldMappings,
  });

  factory ConfirmationTemplate.fromJson(Map<String, dynamic> json) {
    return ConfirmationTemplate(
      title: json['title'] ?? 'Confirm Transaction',
      subtitle: json['subtitle'] ?? 'Please verify the details before proceeding',
      fieldMappings: Map<String, String>.from(json['fieldMappings'] ?? {}),
    );
  }

  Map<String, String> generateTransactionDetails(Map<String, dynamic> formData) {
    Map<String, String> details = {};

    fieldMappings.forEach((fieldKey, displayLabel) {
      if (formData.containsKey(fieldKey) && formData[fieldKey] != null) {
        String value = formData[fieldKey].toString();
        
        // Format specific field types
        if (fieldKey == 'amt' || fieldKey.toLowerCase().contains('amount')) {
          value = 'KES $value';
        } else if (fieldKey.toLowerCase().contains('date')) {
          // Format date if needed
          try {
            DateTime date = DateTime.parse(value);
            value = '${date.day}/${date.month}/${date.year}';
          } catch (e) {
            // Keep original value if parsing fails
          }
        }
        
        details[displayLabel] = value;
      }
    });

    return details;
  }
}

class SuccessTemplate {
  final String title;
  final String message;
  final Map<String, String> fieldMappings;

  SuccessTemplate({
    required this.title,
    required this.message,
    required this.fieldMappings,
  });

  factory SuccessTemplate.fromJson(Map<String, dynamic> json) {
    return SuccessTemplate(
      title: json['title'] ?? 'Transaction Successful',
      message: json['message'] ?? 'Your transaction has been processed successfully.',
      fieldMappings: Map<String, String>.from(json['fieldMappings'] ?? {}),
    );
  }

  Map<String, String> generateSuccessDetails(
    Map<String, dynamic> response,
    Map<String, dynamic> formData,
  ) {
    Map<String, String> details = {};

    // Add response data
    if (response.containsKey('header')) {
      var header = response['header'];
      if (header['txnId'] != null) {
        details['Transaction ID'] = header['txnId'].toString();
      }
      if (header['customerName'] != null) {
        details['Customer Name'] = header['customerName'].toString();
      }
    }

    if (response.containsKey('responseData')) {
      var responseData = response['responseData'];
      
      // Apply field mappings from template
      fieldMappings.forEach((responseKey, displayLabel) {
        if (responseData.containsKey(responseKey) && responseData[responseKey] != null) {
          String value = responseData[responseKey].toString();
          
          // Format specific field types
          if (responseKey.toLowerCase().contains('amount') || responseKey == 'amt') {
            value = 'KES $value';
          }
          
          details[displayLabel] = value;
        }
      });
    }

    // Add form data if not already included
    formData.forEach((key, value) {
      String displayKey = _formatFieldKey(key);
      if (!details.containsKey(displayKey) && value != null) {
        String formattedValue = value.toString();
        
        if (key == 'amt' || key.toLowerCase().contains('amount')) {
          formattedValue = 'KES $formattedValue';
        }
        
        details[displayKey] = formattedValue;
      }
    });

    return details;
  }

  String _formatFieldKey(String key) {
    // Convert camelCase to Title Case
    return key.replaceAllMapped(
        RegExp(r'([A-Z])'),
            (match) => ' ${match.group(0)}'
    ).split(' ')
        .map((word) => word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1))
        .join(' ')
        .trim();
  }
}

class ErrorTemplate {
  final String title;
  final String defaultMessage;
  final Map<String, String> errorMappings;

  ErrorTemplate({
    required this.title,
    required this.defaultMessage,
    required this.errorMappings,
  });

  factory ErrorTemplate.fromJson(Map<String, dynamic> json) {
    return ErrorTemplate(
      title: json['title'] ?? 'Transaction Failed',
      defaultMessage: json['defaultMessage'] ?? 'Transaction could not be completed. Please try again.',
      errorMappings: Map<String, String>.from(json['errorMappings'] ?? {}),
    );
  }

  String getErrorMessage(String errorCode, String? originalMessage) {
    // Check if we have a custom mapping for this error code
    if (errorMappings.containsKey(errorCode)) {
      return errorMappings[errorCode]!;
    }

    // Return original message if available, otherwise default
    return originalMessage ?? defaultMessage;
  }
} 