import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesHelper {
  static Future<void> setClientId(String clientId) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('clientId', clientId);
  }

  static Future<void> setMsisdn(String msisdn) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('msisdn', msisdn);
  }

  static Future<String?> getClientId() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('clientId');
  }

  static Future<void> clearSharedPreferences() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  static Future<String?> getCustId() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('spCustId');
  }

  static Future<String?> getCustomerName() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('customerName');
  }

  static Future<void> removeToken() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('_utopia');
  }

  static Future<void> removeSecretKey() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('_tajemnica');
  }
} 