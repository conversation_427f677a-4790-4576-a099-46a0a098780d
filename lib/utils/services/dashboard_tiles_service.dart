import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'api_service.dart';
import 'api_endpoints.dart';
import 'cryptographer.dart';
import 'shared_preferences_helper.dart';

import '../app_logger.dart';
/// Service for managing dashboard tiles configuration
class DashboardTilesService {
  static const String _dashboardTilesKey = 'dashboardTiles';
  static const String _moreServicesKey = 'moreServices';
  static const String _tilesFileName = 'dashboard_tiles_log.txt';
  static const String _moreServicesFileName = 'more_services_log.txt';

  /// Fetch device configuration from API and save dashboard tiles
  static Future<bool> fetchDashboardTiles() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? secretKey = prefs.getString('_tajemnica');
      if (secretKey == null) {
        AppLogger.info('No secret key found');
        return false;
      }

      ApiService apiService = ApiService();
      String phoneNumber = prefs.getString('msisdn') ?? "";
      String clientId = await SharedPreferencesHelper.getClientId() ?? "";
      
      AesEncryption aes = AesEncryption();
      String encryptedMsisdn = aes.encryptWithBase64Key(phoneNumber, secretKey);
      // Send clientId unencrypted - backend expects it as a plain number
      String unencryptedClientId = clientId;

      var requestBody = {
        'msisdn': encryptedMsisdn,
        'clientId': unencryptedClientId,  // Send unencrypted
        'categoryName': 'Spotcash_App',
        'action': 'device_config',
      };

      var response = await apiService.postRequest(
        ApiEndpoints.getDeviceConfig,
        requestBody,
        isAuth: true,
      );

      if (response != null && response['hashedBody'] != null) {
        AppLogger.info('API response received, processing...');
        AppLogger.info('Response keys: ${response.keys.toList()}');
        
        // Handle potential token refresh like in auth service
        String decryptionKey = secretKey;
        AppLogger.info('Using initial secret key: ${secretKey.substring(0, 10)}...');
        
        // Handle token refresh if a new token is provided
        if (response.containsKey('znak') && response['znak'] != null && response['znak'].toString().isNotEmpty) {
          String responseToken = response['znak'].toString();
          AppLogger.info('New token found in response: ${responseToken.substring(0, 20)}...');
          
          try {
            // Decode the response token to get the updated secret key
            Map<String, dynamic> tokenPayload = JwtDecoder.decode(responseToken);
            AppLogger.info('Token payload keys: ${tokenPayload.keys.toList()}');
            
            if (tokenPayload['tajemnica'] != null) {
              decryptionKey = tokenPayload['tajemnica'];
              AppLogger.info('Using updated secret key from response token: ${decryptionKey.substring(0, 10)}...');
              
              // Save the new token and secret key
              await prefs.setString('token', responseToken);
              await prefs.setString('_tajemnica', decryptionKey);
              AppLogger.info('Token and secret key updated from response');
            } else {
              AppLogger.info('No tajemnica found in response token payload, using stored secret key');
            }
          } catch (e) {
            AppLogger.error('processing response token: $e');
            AppLogger.info('Using stored secret key for decryption');
          }
        } else {
          AppLogger.info('No new token in response, using stored secret key');
        }

        AppLogger.info('Attempting to decrypt with key: ${decryptionKey.substring(0, 10)}...');
        String decrypt = aes.decryptWithBase64Key(response['hashedBody'], decryptionKey);
        Map<String, dynamic> configJson = jsonDecode(decrypt);

        AppLogger.info('Device config response received for client: $clientId');
        
        // Process dashboard tiles
        if (configJson['clientsAppConfiguration'] != null) {
          AppLogger.info('clientsAppConfiguration found in response');
          
          if (configJson['clientsAppConfiguration']['dashboardTiles'] != null) {
            String dashboardTilesString = configJson['clientsAppConfiguration']['dashboardTiles'];
            List<dynamic> dashboardTiles = jsonDecode(dashboardTilesString);
            
            await saveDashboardTiles(dashboardTilesString);
            await _logDashboardTilesToFile(dashboardTilesString);
            AppLogger.info('✅ Dashboard Tiles saved: ${dashboardTiles.length} tiles for client $clientId');
          } else {
            AppLogger.info('⚠️ dashboardTiles is null for client $clientId - no tiles configured in database');
          }

          // Process more services
          if (configJson['clientsAppConfiguration']['moreServices'] != null) {
            String moreServicesString = configJson['clientsAppConfiguration']['moreServices'];
            List<dynamic> moreServices = jsonDecode(moreServicesString);
            
            await saveMoreServices(moreServicesString);
            await _logMoreServicesToFile(moreServicesString);
            AppLogger.info('✅ More Services saved: ${moreServices.length} services for client $clientId');
          } else {
            AppLogger.info('⚠️ moreServices is null for client $clientId');
          }
        } else {
          AppLogger.info('❌ clientsAppConfiguration not found in response for client $clientId');
        }

        return true;
      } else {
        AppLogger.info('❌ Invalid response from device config API');
        return false;
      }
    } catch (e) {
      AppLogger.error('fetching device config: $e');
      return false;
    }
  }

  /// Save dashboard tiles to SharedPreferences
  static Future<void> saveDashboardTiles(String dashboardTilesJson) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(_dashboardTilesKey, dashboardTilesJson);
    AppLogger.info('Dashboard tiles saved to SharedPreferences');
  }

  /// Save more services to SharedPreferences
  static Future<void> saveMoreServices(String moreServicesJson) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(_moreServicesKey, moreServicesJson);
    AppLogger.info('More services saved to SharedPreferences');
  }

  /// Load dashboard tiles from file
  static Future<List<dynamic>?> loadDashboardTilesFromFile() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/$_tilesFileName';
      final file = File(path);

      if (await file.exists()) {
        String contents = await file.readAsString();
        List<dynamic> tiles = jsonDecode(contents);
        return tiles;
      } else {
        AppLogger.info('Dashboard Tiles file not found.');
        return null;
      }
    } catch (e) {
      AppLogger.error('reading dashboard tiles from file: $e');
      return null;
    }
  }

  /// Load dashboard tiles from SharedPreferences
  static Future<List<dynamic>?> loadDashboardTilesFromPrefs() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? tilesJson = prefs.getString(_dashboardTilesKey);
      
      if (tilesJson != null) {
        return jsonDecode(tilesJson);
      }
      return null;
    } catch (e) {
      AppLogger.error('reading dashboard tiles from SharedPreferences: $e');
      return null;
    }
  }

  /// Load more services from SharedPreferences
  static Future<List<dynamic>?> loadMoreServicesFromPrefs() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? servicesJson = prefs.getString(_moreServicesKey);
      
      if (servicesJson != null) {
        return jsonDecode(servicesJson);
      }
      return null;
    } catch (e) {
      AppLogger.error('reading more services from SharedPreferences: $e');
      return null;
    }
  }

  /// Get filtered dashboard tiles (first 6 for dashboard)
  static Future<List<Map<String, dynamic>>> getFilteredDashboardTiles() async {
    List<dynamic>? tiles = await loadDashboardTilesFromFile();
    tiles ??= await loadDashboardTilesFromPrefs();

    if (tiles != null) {
      AppLogger.info('Raw tiles from storage: ${tiles.length} tiles');
      
      List<Map<String, dynamic>> filteredTiles = tiles
          .where((tile) {
            String tab = tile['tab'] ?? '';
            String name = tile['name'] ?? '';

            // Tiles to exclude from dashboard (child services that should not be top-level)
            List<String> excludedChildTiles = [
              'shareBalance',
              'nwdBalance', 
              'loanBalance',
              'savingsBalance'
            ];

            // Services that are better suited for services screen
            List<String> excludedTiles = [
              'approveLoans',  // Keep this excluded
              // Remove 'myDigitalLoans' from exclusion since it's in your data
            ];

            if (excludedChildTiles.contains(name) || excludedTiles.contains(name)) {
              AppLogger.info('Excluding tile: $name (reason: child tile or excluded)');
              return false;
            }

            // Include tiles from transactions, loans, and enquiries tabs
            bool included = (tab == 'transactions' || tab == 'loans' || tab == 'enquiries');
            AppLogger.info('Tile: $name, tab: $tab, included: $included');
            return included;
          })
          .toList()
          .cast<Map<String, dynamic>>();

      // Sort by position if available
      filteredTiles.sort((a, b) {
        int posA = a['position'] ?? 999;
        int posB = b['position'] ?? 999;
        return posA.compareTo(posB);
      });

      AppLogger.info('Filtered tiles: ${filteredTiles.length} tiles');
      for (var tile in filteredTiles) {
        AppLogger.info('- ${tile['title']} (${tile['name']}) - position: ${tile['position']}');
      }

      // Return first 6 tiles for dashboard
      return filteredTiles.take(6).toList();
    }

    AppLogger.info('No tiles found in storage - client may not have dashboard tiles configured');
    return [];
  }

  /// Get extra tiles (beyond first 6) for services screen
  static Future<List<Map<String, String>>> getExtraTiles() async {
    List<dynamic>? tiles = await loadDashboardTilesFromFile();
    tiles ??= await loadDashboardTilesFromPrefs();

    if (tiles != null) {
      List<Map<String, dynamic>> filteredTiles = tiles
          .where((tile) {
            String tab = tile['tab'] ?? '';
            String name = tile['name'] ?? '';

            List<String> balanceTiles = [
              'shareBalance',
              'nwdBalance',
              'loanBalance', 
              'savingsBalance'
            ];

            List<String> excludedTiles = [
              'b2bTransfer',
              'myDigitalLoans',
              'approveLoans'
            ];

            if (balanceTiles.contains(name) || excludedTiles.contains(name)) {
              return false;
            }

            return (tab == 'transactions' || tab == 'loans' || tab == 'enquiries');
          })
          .toList()
          .cast<Map<String, dynamic>>();

      // Return tiles beyond first 6 for services screen
      return filteredTiles
          .skip(6)
          .map((tile) => {
                'title': tile['title']?.toString() ?? '',
                'iconPath': tile['iconPath']?.toString() ?? 'assets/images/default.png',
                'name': tile['name']?.toString() ?? '',
              })
          .toList()
          .cast<Map<String, String>>();
    }

    return [];
  }

  /// Get tile by name
  static Future<Map<String, dynamic>?> getTileByName(String name) async {
    List<dynamic>? tiles = await loadDashboardTilesFromFile();
    tiles ??= await loadDashboardTilesFromPrefs();

    if (tiles != null) {
      try {
        return tiles.firstWhere(
          (tile) => tile['name'] == name,
        );
      } catch (e) {
        AppLogger.info('Tile not found: $name');
        return null;
      }
    }

    return null;
  }

  /// Log dashboard tiles to file
  static Future<void> _logDashboardTilesToFile(String dashboardTiles) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/$_tilesFileName';
      final file = File(path);
      await file.writeAsString(dashboardTiles, mode: FileMode.write);
      AppLogger.info('Dashboard Tiles logged to file at: $path');
    } catch (e) {
      AppLogger.error('logging dashboard tiles to file: $e');
    }
  }

  /// Log more services to file
  static Future<void> _logMoreServicesToFile(String moreServices) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/$_moreServicesFileName';
      final file = File(path);
      await file.writeAsString(moreServices, mode: FileMode.write);
      AppLogger.info('More Services logged to file at: $path');
    } catch (e) {
      AppLogger.error('logging more services to file: $e');
    }
  }

  /// Clear cached tiles (for testing or refresh)
  static Future<void> clearCachedTiles() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.remove(_dashboardTilesKey);
      await prefs.remove(_moreServicesKey);

      final directory = await getApplicationDocumentsDirectory();
      
      final tilesFile = File('${directory.path}/$_tilesFileName');
      if (await tilesFile.exists()) {
        await tilesFile.delete();
      }

      final servicesFile = File('${directory.path}/$_moreServicesFileName');
      if (await servicesFile.exists()) {
        await servicesFile.delete();
      }

      AppLogger.info('Cached tiles cleared');
    } catch (e) {
      AppLogger.error('clearing cached tiles: $e');
    }
  }

  /// Manually save tiles data (for testing/development)
  static Future<bool> saveManualTilesData(List<dynamic> tiles) async {
    try {
      // Save to SharedPreferences
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String tilesJson = jsonEncode(tiles);
      await prefs.setString(_dashboardTilesKey, tilesJson);

      // Save to file
      await _logDashboardTilesToFile(tilesJson);

      AppLogger.info('✅ Manually saved ${tiles.length} tiles to storage');
      return true;
    } catch (e) {
      AppLogger.info('❌ Error saving manual tiles data: $e');
      return false;
    }
  }
} 