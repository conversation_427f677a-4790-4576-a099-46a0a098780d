/// A class that defines the API endpoints used in the application.
///
/// This class contains static constants representing various API endpoints
/// for performing operations related to device configuration, phone verification,
/// PIN validation, OTP validation, account balances, transactions, and withdrawals.
///
/// Each endpoint is represented as a static string, making it easy to reference
/// them throughout the application without hardcoding strings.
///
/// Example usage:
/// ```dart
/// final String endpoint = ApiEndpoints.getDeviceConfig;
/// ```
class ApiEndpoints {
  /// Endpoint for fetching the device configuration.
  static const String getDeviceConfig = '/deviceconfig';

  static const String saveProfilePicture = '/saveProfilePicture';

  static const String fetchProfilePicture = '/fetchProfilePicture';

  ///Endpoint for fetching Digital loans
  static const String getDigitalLoans= '/digitaloansEncrypted';

  ///Endpoint for fetching E-loans
  static const String getELoans= '/eloanTypesEncrypted';

  static const String checkEligibility= '/checkeligibilityEncrpyted';

  /// Endpoint for refreshing token on dashboard page
  static const String refreshToken = '/refreshToken';

  /// Endpoint for verifying the MSISDN (Mobile Station International Subscriber Directory Number).
  static const String verifyMsisdn = '/verifyPhoneNumber';
  static const String validateCustomer = '/validateCustomer';

  /// Endpoint for verifying the PIN securely.
  static const String verifyPin = '/verifyPinEncryptedNew';

  /// Endpoint for verifying the PIN securely.
  static const String verifyPinNew = '/verifyPinEncryptedForApp2';

  /// Endpoint for validating the OTP (One-Time Password) securely.
  static const String validateOtp = '/validateOtpEncryptedForApp2';

  /// Endpoint for retrieving account balances.
  /// Note: Currently, this endpoint is not specified.
  static const String accounts = '/accounts';

  /// Endpoint for fetching the latest transactions.
  /// Note: Currently, this endpoint is not specified.
  static const String latestTransactions = '/getLatestTransactions';

  /// Endpoint for verifying account.
  static const String verifyAccount = '/verifyaccountEncrpyted';

  /// Endpoint for validating the OTP (One-Time Password) securely.
  static const String getMemberAccounts = '/memberaccountsEncrypted';

  /// Endpoint for processing transactions.
  static const String transaction = '/serviceEncrypted';

  static const String verifySecurityQuestion = '/verifySecurityQuestion';

  static const String fetchCustomerSecurityQuestions = '/fetchCustomerSecurityQuestions';

  static const String fetchSecurityQuestions = '/fetchSecurityQuestions';

  static const String addSecurityQuestions = '/addSecurityQuestions';

  ///Endpoint for referring a friend
  static const String referFriend = '/referFriendEncrypted';

  ///Endpoint for fetching utility services
  static const String fetchUtilities = '/fetchutilities';

  /// endpoint to fetch faqs
  static const String getClientFaqs = '/getClientFaqs';

  ///endpoint to fetch banks
  static const String getBanks = '/getProviders';

  ///endpoint to get service charge
  static const String getServiceCharge = '/getServiceCharge';

  /// endpoint to validate account
  static const String validateAccount = '/sasaPayAccountsValidate';

  /// endpoint to checkMerchantBalance
  static const String checkMerchantBalance = '/sasapay/checkMerchantBalance';

  /// endpoint to fetch app flyers
  static const String getAppFlyers = '/appFlyers';

  /// endpoint to get service configs
  static const String getServiceJourney = '/getserviceconfig';

  /// Endpoint for merchant registration
  static const String merchantRegistration = '/merchantRegistration';

  /// Endpoint for getting business types
  static const String getBusinessTypes = '/getBusinessTypes';

  static const String getSalesAgentDetails = '/getSalesAgentDetails';

  static const String getOwnerDetailsById = '/getOwnerDetailsById';

  static const String merchantBusinessOwnerRegistration = '/merchantBusinessOwnerRegistration';
} 