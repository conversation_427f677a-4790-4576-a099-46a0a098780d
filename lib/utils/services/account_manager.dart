import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'api_endpoints.dart';
import 'api_service.dart';
import 'cryptographer.dart';
import 'shared_preferences_helper.dart';

import '../app_logger.dart';
class AccountManager {
  static const String _accountsKey = 'cached_accounts';
  static const String _memberNameKey = 'cached_member_name';

  /// Save accounts to SharedPreferences
  static Future<void> saveAccounts(List<dynamic> accounts, String memberName) async {
    final prefs = await SharedPreferences.getInstance();
    final accountsJson = json.encode(accounts);
    await prefs.setString(_accountsKey, accountsJson);
    await prefs.setString(_memberNameKey, memberName);
  }

  /// Get accounts from SharedPreferences
  static Future<List<dynamic>?> getCachedAccounts() async {
    final prefs = await SharedPreferences.getInstance();
    final accountsJson = prefs.getString(_accountsKey);

    if (accountsJson != null) {
      return json.decode(accountsJson);
    }
    return null;
  }

  /// Get cached member name
  static Future<String?> getCachedMemberName() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_memberNameKey);
  }

  /// Clear cached accounts
  static Future<void> clearCachedAccounts() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_accountsKey);
    await prefs.remove(_memberNameKey);
  }

  /// Invalidate cache
  static Future<void> invalidateCache() async {
    await clearCachedAccounts();
  }

  /// Fetch fresh accounts from API
  static Future<List<dynamic>> fetchFreshAccounts() async {
    ApiService apiService = ApiService();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? phoneNumber = prefs.getString('msisdn');
    
    if (phoneNumber == null) {
      throw Exception('Phone number not found in preferences');
    }

    var clientId = await SharedPreferencesHelper.getClientId();

    var requestBody = {
      'phoneNumber': phoneNumber,
      'clientId': clientId,
    };

    try {
      AppLogger.info('Fetching accounts with request body: $requestBody');
      
      var response = await apiService.postRequest(
        ApiEndpoints.accounts,
        requestBody,
      );

      AesEncryption aesEncryption = AesEncryption();
      String? secretKey = prefs.getString('_tajemnica');

      if (secretKey == null) {
        throw Exception('Secret key not found in preferences');
      }

      String decrypt = aesEncryption.decryptWithBase64Key(
          response['hashedBody'], secretKey);
      
      AppLogger.info('Decrypted accounts response: $decrypt');

      var jsonResponse = jsonDecode(decrypt);
      var fetchedAccounts = jsonResponse['accounts']['accounts'];
      String memberName = jsonResponse['accounts']['member']['name'].trim();

      // Cache both accounts and member name
      await saveAccounts(fetchedAccounts, memberName);

      return fetchedAccounts;
    } catch (e) {
      AppLogger.error('fetching fresh accounts: $e');
      rethrow;
    }
  }

  /// Refresh accounts after transaction
  static Future<List<dynamic>> refreshAccountsAfterTransaction() async {
    await invalidateCache();
    return await fetchFreshAccounts();
  }
} 