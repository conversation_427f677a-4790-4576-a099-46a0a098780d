import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'api_service.dart';
import 'api_endpoints.dart';
import 'cryptographer.dart';
import 'shared_preferences_helper.dart';
import '../debug/image_debug_helper.dart';

import '../app_logger.dart';
/// Service class for handling profile picture operations with the backend
class ProfilePictureService {
  final ApiService _apiService = ApiService();
  final AesEncryption _aes = AesEncryption();

  /// Saves a profile picture to the backend
  /// 
  /// [imageFile] - The image file to upload
  /// [context] - BuildContext for error handling (optional)
  /// Returns true if successful, false otherwise
  Future<bool> saveProfilePicture(File imageFile, {BuildContext? context}) async {
    try {
      // Get required data from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      String? phoneNumber = prefs.getString('msisdn');
      String? clientId = await SharedPreferencesHelper.getClientId();
      String? secretKey = prefs.getString('_tajemnica');

      if (phoneNumber == null || clientId == null || secretKey == null) {
        AppLogger.error('Missing required data for profile picture upload');
        return false;
      }

      // Read image file and convert to base64
      Uint8List imageBytes = await imageFile.readAsBytes();
      String base64Image = base64Encode(imageBytes);

      // Basic validation
      AppLogger.info('Image file size: ${imageBytes.length} bytes');

      // WORKAROUND: Backend expects direct parameters, not encrypted hashedBody
      // This matches the current backend implementation which doesn't handle encryption
      var requestBody = {
        'msisdn': phoneNumber,
        'clientId': clientId,
        'image': base64Image, // Backend expects 'image', not 'profilePicture'
      };

      AppLogger.info('Saving profile picture for user: $phoneNumber');

      var response = await _apiService.postRequest(
        ApiEndpoints.saveProfilePicture,
        requestBody, // Send direct parameters instead of encrypted hashedBody
        context: context,
        isAuth: true,
      );

      AppLogger.info('Raw response received: $response');
      AppLogger.info('Response type: ${response.runtimeType}');
      if (response != null) {
        AppLogger.info('Response keys: ${response.keys.toList()}');
      }

      // Process response - Backend returns direct ApiResponse, not encrypted
      if (response != null) {
        AppLogger.info('Profile picture save response: $response');

        // Check if the save was successful
        if (response['responseCode'] == '00') {
          AppLogger.info('Profile picture saved successfully');
          return true;
        } else {
          AppLogger.info('Failed to save profile picture: ${response['message'] ?? 'Unknown error'}');
          return false;
        }
      } else {
        AppLogger.info('Invalid response from server');
        return false;
      }
    } catch (e) {
      AppLogger.error('saving profile picture: $e');
      return false;
    }
  }

  /// Fetches the profile picture from the backend
  /// 
  /// [context] - BuildContext for error handling (optional)
  /// Returns the image bytes if successful, null otherwise
  Future<Uint8List?> fetchProfilePicture({BuildContext? context}) async {
    try {
      // Get required data from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      String? phoneNumber = prefs.getString('msisdn');
      String? clientId = await SharedPreferencesHelper.getClientId();
      String? secretKey = prefs.getString('_tajemnica');

      if (phoneNumber == null || clientId == null || secretKey == null) {
        AppLogger.error('Missing required data for profile picture fetch');
        return null;
      }

      // WORKAROUND: Backend expects direct parameters, not encrypted hashedBody
      var requestBody = {
        'msisdn': phoneNumber,
        'clientId': clientId,
      };

      AppLogger.info('Fetching profile picture for user: $phoneNumber');

      // Send request to backend with direct parameters (no encryption)
      var response = await _apiService.postRequest(
        ApiEndpoints.fetchProfilePicture,
        requestBody, // Send direct parameters instead of encrypted hashedBody
        context: context,
        isAuth: true,
      );

      // Process response - Backend returns direct ApiResponse, not encrypted
      if (response != null) {
        AppLogger.info('Profile picture fetch response received: $response');

        // Check if the fetch was successful and contains image data
        if (response['responseCode'] == '00' && response['responseDescription'] != null) {
          try {
            // Backend puts the base64 image in responseDescription field
            String base64Image = response['responseDescription'].toString().trim();
            
            // Validate base64 string
            if (base64Image.isEmpty) {
              AppLogger.error('Empty base64 image data received from backend');
              return null;
            }
            
            // Additional validation: Check if it looks like valid base64
            if (!RegExp(r'^[A-Za-z0-9+/]*={0,2}$').hasMatch(base64Image)) {
              AppLogger.error('Invalid base64 format received from backend');
              return null;
            }
            
            AppLogger.info('Attempting to decode base64 image data (length: ${base64Image.length})');
            
            // Decode the base64 string
            Uint8List imageBytes = base64Decode(base64Image);
            
            // Validate that the decoded bytes represent a valid image
            if (imageBytes.isEmpty) {
              AppLogger.error('Decoded image bytes are empty');
              return null;
            }
            
            AppLogger.info('Successfully decoded image bytes (size: ${imageBytes.length} bytes)');
            
            // Additional validation: Check if it's a valid image format
            // Check for common image file signatures
            if (!_isValidImageFormat(imageBytes)) {
              AppLogger.error('Decoded data does not appear to be a valid image format');
              return null;
            }
            
            AppLogger.info('Profile picture fetched and validated successfully');
            return imageBytes;
            
          } catch (e) {
            AppLogger.error('Error decoding base64 image data: $e');
            return null;
          }
        } else if (response['responseCode'] == '01') {
          AppLogger.info('No profile picture found for user: ${response['message']}');
          return null;
        } else {
          AppLogger.info('Failed to fetch profile picture: ${response['message'] ?? 'Unknown error'}');
          return null;
        }
      } else {
        AppLogger.info('Invalid response from server');
        return null;
      }
    } catch (e) {
      AppLogger.error('fetching profile picture: $e');
      return null;
    }
  }

  /// Deletes the profile picture from the backend
  /// 
  /// [context] - BuildContext for error handling (optional)
  /// Returns true if successful, false otherwise
  Future<bool> deleteProfilePicture({BuildContext? context}) async {
    try {
      // Get required data from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      String? phoneNumber = prefs.getString('msisdn');
      String? clientId = await SharedPreferencesHelper.getClientId();
      String? secretKey = prefs.getString('_tajemnica');

      if (phoneNumber == null || clientId == null || secretKey == null) {
        AppLogger.error('Missing required data for profile picture deletion');
        return false;
      }

      // Prepare request body
      var requestBody = {
        'msisdn': phoneNumber,
        'clientId': clientId,
        'action': 'delete_profile_picture',
      };

      AppLogger.info('Deleting profile picture for user: $phoneNumber');

      // Encrypt the request body
      String plainTextRequestBody = jsonEncode(requestBody);
      String encryptedRequestBody = _aes.encryptWithBase64Key(plainTextRequestBody, secretKey);

      // Send request to backend
      var response = await _apiService.postRequest(
        ApiEndpoints.saveProfilePicture, // Using save endpoint with delete action
        {'hashedBody': encryptedRequestBody},
        context: context,
        isAuth: true,
      );

      // Process response
      if (response != null && response['hashedBody'] != null) {
        String decryptedResponse = _aes.decryptWithBase64Key(
          response['hashedBody'],
          secretKey,
        );

        Map<String, dynamic> responseData = jsonDecode(decryptedResponse);
        AppLogger.info('Profile picture delete response: $responseData');

        // Check if the deletion was successful
        if (responseData['responseCode'] == '00' || responseData['status'] == 'success') {
          AppLogger.info('Profile picture deleted successfully');
          return true;
        } else {
          AppLogger.info('Failed to delete profile picture: ${responseData['message'] ?? 'Unknown error'}');
          return false;
        }
      } else {
        AppLogger.info('Invalid response from server');
        return false;
      }
    } catch (e) {
      AppLogger.error('deleting profile picture: $e');
      return false;
    }
  }

  /// Test backend connectivity with a small payload
  Future<bool> testBackendConnectivity() async {
    try {
      AppLogger.info('=== TESTING BACKEND CONNECTIVITY ===');

      // Get required data from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      String? phoneNumber = prefs.getString('msisdn');
      String? clientId = await SharedPreferencesHelper.getClientId();
      String? secretKey = prefs.getString('_tajemnica');

      if (phoneNumber == null || clientId == null || secretKey == null) {
        AppLogger.error('Missing required data for backend test');
        return false;
      }

      // Prepare a small test request body (no image) - using direct parameters
      var requestBody = {
        'msisdn': phoneNumber,
        'clientId': clientId,
        'image': 'test_small_image_data', // Small test image data
      };

      AppLogger.info('Test request body: $requestBody');
      AppLogger.info('Using direct parameters (no encryption) to match backend implementation');

      // Send request to backend with direct parameters
      var response = await _apiService.postRequest(
        ApiEndpoints.saveProfilePicture,
        requestBody, // Send direct parameters instead of encrypted hashedBody
        isAuth: true,
      );

      AppLogger.info('Backend connectivity test response: $response');
      return response != null;

    } catch (e) {
      AppLogger.info('Backend connectivity test failed: $e');
      return false;
    }
  }

  /// Validates if the byte data represents a valid image format
  /// by checking common image file signatures
  bool _isValidImageFormat(Uint8List bytes) {
    if (bytes.length < 4) return false;
    
    // Check for common image file signatures
    // JPEG: FF D8 FF
    if (bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) {
      return true;
    }
    
    // PNG: 89 50 4E 47
    if (bytes.length >= 8 && 
        bytes[0] == 0x89 && bytes[1] == 0x50 && 
        bytes[2] == 0x4E && bytes[3] == 0x47) {
      return true;
    }
    
    // GIF: 47 49 46 38
    if (bytes.length >= 6 && 
        bytes[0] == 0x47 && bytes[1] == 0x49 && 
        bytes[2] == 0x46 && bytes[3] == 0x38) {
      return true;
    }
    
    // WebP: 52 49 46 46 (RIFF) + WebP signature
    if (bytes.length >= 12 && 
        bytes[0] == 0x52 && bytes[1] == 0x49 && 
        bytes[2] == 0x46 && bytes[3] == 0x46 &&
        bytes[8] == 0x57 && bytes[9] == 0x45 && 
        bytes[10] == 0x42 && bytes[11] == 0x50) {
      return true;
    }
    
    // BMP: 42 4D
    if (bytes[0] == 0x42 && bytes[1] == 0x4D) {
      return true;
    }
    
    return false;
  }

  /// Test method to verify image encoding/decoding without backend
  Future<bool> testImageEncoding(File imageFile) async {
    try {
      AppLogger.info('=== TESTING IMAGE ENCODING ===');

      // Get debug info for original file
      Map<String, dynamic> originalDebugInfo = await ImageDebugHelper.getImageDebugInfo(imageFile);
      AppLogger.info('ORIGINAL IMAGE:');
      ImageDebugHelper.printDebugInfo(originalDebugInfo);

      // Read and encode
      Uint8List originalBytes = await imageFile.readAsBytes();
      String base64String = base64Encode(originalBytes);

      // Decode back
      Uint8List decodedBytes = base64Decode(base64String);

      // Compare
      bool isIdentical = ImageDebugHelper.compareImages(originalBytes, decodedBytes);
      AppLogger.info('Encoding/Decoding integrity: $isIdentical');

      // Test what happens when we put it through JSON
      Map<String, dynamic> testJson = {
        'profilePicture': base64String,
        'msisdn': 'test',
        'clientId': 'test',
      };

      String jsonString = jsonEncode(testJson);
      Map<String, dynamic> parsedJson = jsonDecode(jsonString);
      String retrievedBase64 = parsedJson['profilePicture'];

      bool jsonIntegrity = base64String == retrievedBase64;
      AppLogger.info('JSON encoding integrity: $jsonIntegrity');

      // Test what happens with encryption
      final prefs = await SharedPreferences.getInstance();
      String? secretKey = prefs.getString('_tajemnica');

      if (secretKey != null) {
        String encryptedJson = _aes.encryptWithBase64Key(jsonString, secretKey);
        String decryptedJson = _aes.decryptWithBase64Key(encryptedJson, secretKey);
        Map<String, dynamic> decryptedParsed = jsonDecode(decryptedJson);
        String finalBase64 = decryptedParsed['profilePicture'];

        bool encryptionIntegrity = base64String == finalBase64;
        AppLogger.info('Encryption/Decryption integrity: $encryptionIntegrity');

        // Final decode test
        Uint8List finalBytes = base64Decode(finalBase64);
        bool finalIntegrity = ImageDebugHelper.compareImages(originalBytes, finalBytes);
        AppLogger.info('Final end-to-end integrity: $finalIntegrity');

        return finalIntegrity;
      } else {
        AppLogger.info('No secret key found - cannot test encryption');
        return jsonIntegrity;
      }

    } catch (e) {
      AppLogger.error('in encoding test: $e');
      return false;
    }
  }
}
