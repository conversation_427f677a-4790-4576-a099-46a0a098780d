import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/transaction_model.dart';
import 'api_service.dart';
import 'api_endpoints.dart';
import 'cryptographer.dart';
import 'shared_preferences_helper.dart';

import '../app_logger.dart';
/// Service for fetching and managing latest transactions
class LatestTransactionsService {
  static const String _cacheKey = 'latest_transactions_cache';
  static const String _cacheTimestampKey = 'latest_transactions_timestamp';
  static const int _cacheValidityMinutes = 2; // Cache valid for 2 minutes (shorter for faster updates)

  /// Fetch latest transactions from API
  static Future<List<TransactionModel>> fetchLatestTransactions() async {
    try {
      // Check cache first
      final cachedTransactions = await _getCachedTransactions();
      if (cachedTransactions != null && cachedTransactions.isNotEmpty) {
        return cachedTransactions;
      }
      
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? phoneNumber = prefs.getString('msisdn');
      
      if (phoneNumber == null) {
        throw Exception('Phone number not found in preferences');
      }

      String? clientId = await SharedPreferencesHelper.getClientId();
      if (clientId == null) {
        throw Exception('Client ID not found');
      }

      var requestBody = {
        'msisdn': phoneNumber,
        'clientId': clientId,
      };

      ApiService apiService = ApiService();
      var response = await apiService.postRequest(
        ApiEndpoints.latestTransactions,
        requestBody,
      );

      if (response == null || response['hashedBody'] == null) {
        throw Exception('Invalid response from server');
      }

      // Decrypt response
      String? secretKey = prefs.getString('_tajemnica');
      if (secretKey == null) {
        throw Exception('Secret key not found');
      }

      AesEncryption aesEncryption = AesEncryption();
      String decrypt = aesEncryption.decryptWithBase64Key(
        response['hashedBody'],
        secretKey,
      );

      Map<String, dynamic> jsonResponse = jsonDecode(decrypt);

      // Parse transactions from response
      List<dynamic> transactionsData = jsonResponse['responseBody'] ?? [];

      List<TransactionModel> transactions = [];
      for (int i = 0; i < transactionsData.length; i++) {
        var data = transactionsData[i];

        // For transactions without timestamps, add a small offset to distinguish them
        if (data['timeCompleted'] == null) {
          data = Map<String, dynamic>.from(data);
          // Add a small offset (in seconds) to distinguish between similar transactions
          data['timeCompleted'] = DateTime.now().millisecondsSinceEpoch - (i * 1000);
        }

        transactions.add(TransactionModel.fromJson(data));
      }

      // Cache the transactions
      await _cacheTransactions(transactions);

      return transactions;

    } catch (e) {
      // Try to return cached data even if expired
      final cachedTransactions = await _getCachedTransactions(ignoreExpiry: true);
      if (cachedTransactions != null && cachedTransactions.isNotEmpty) {
        return cachedTransactions;
      }
      
      // Return empty list if no cache available
      return [];
    }
  }

  /// Get cached transactions if still valid
  static Future<List<TransactionModel>?> _getCachedTransactions({bool ignoreExpiry = false}) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      
      if (!ignoreExpiry) {
        // Check cache validity
        int? cacheTimestamp = prefs.getInt(_cacheTimestampKey);
        if (cacheTimestamp == null) return null;
        
        DateTime cacheTime = DateTime.fromMillisecondsSinceEpoch(cacheTimestamp);
        DateTime now = DateTime.now();
        
        if (now.difference(cacheTime).inMinutes > _cacheValidityMinutes) {
          return null;
        }
      }

      String? cachedData = prefs.getString(_cacheKey);
      if (cachedData == null) return null;

      List<dynamic> transactionsJson = jsonDecode(cachedData);
      List<TransactionModel> transactions = transactionsJson
          .map((data) => TransactionModel.fromJson(data))
          .toList();

      return transactions;
    } catch (e) {
      AppLogger.error('reading cached transactions: $e');
      return null;
    }
  }

  /// Cache transactions with timestamp
  static Future<void> _cacheTransactions(List<TransactionModel> transactions) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      
      List<Map<String, dynamic>> transactionsJson = transactions
          .map((transaction) => transaction.toJson())
          .toList();
      
      await prefs.setString(_cacheKey, jsonEncode(transactionsJson));
      await prefs.setInt(_cacheTimestampKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      AppLogger.error('caching transactions: $e');
    }
  }

  /// Clear cached transactions
  static Future<void> clearCache() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_cacheTimestampKey);
    } catch (e) {
      // Silently handle cache clearing errors
    }
  }

  /// Force refresh transactions (bypass cache)
  static Future<List<TransactionModel>> refreshTransactions() async {
    await clearCache();
    return await fetchLatestTransactions();
  }

  /// Refresh transactions with retry logic for recent transactions
  static Future<List<TransactionModel>> refreshWithRetry({int maxRetries = 3, int delaySeconds = 10}) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      await clearCache();
      List<TransactionModel> transactions = await fetchLatestTransactions();

      // If we got transactions or this is the last attempt, return what we have
      if (transactions.isNotEmpty || attempt == maxRetries) {
        return transactions;
      }

      // Wait before next attempt
      if (attempt < maxRetries) {
        await Future.delayed(Duration(seconds: delaySeconds));
      }
    }

    return [];
  }


}
