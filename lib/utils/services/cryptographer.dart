import 'dart:convert';

import 'package:encrypt/encrypt.dart';

/// A class for AES encryption and decryption using a fixed key.
class AesEncryption {
  final String _keyString = 'UBVJ6AIGrB3Itw39';
  final int _keySize = 16;

  Encrypter? _encrypter;
  Key? _key;

  /// Initializes the AES encryption with a 128-bit key.
  ///
  /// The key is padded to ensure it is exactly 128 bits (16 bytes).
  AesEncryption() {
    _key = Key.fromUtf8(_keyString.padRight(_keySize, ' '));
    _encrypter = Encrypter(AES(_key!, mode: AESMode.ecb, padding: 'PKCS7'));
  }

  /// Encrypts the provided plain text using AES encryption.
  ///
  /// Returns the encrypted text as a base64-encoded string.
  ///
  /// [plainText] - The text to be encrypted.
  String encrypt(String plainText) {
    final encrypted = _encrypter!.encrypt(plainText);
    return encrypted
        .base64; // Return the base64 representation of encrypted text
  }

  /// Decrypts the provided cipher text using AES decryption.
  ///
  /// Returns the decrypted plain text.
  ///
  /// [cipherText] - The base64-encoded string of the text to be decrypted.
  String decrypt(String cipherText) {
    final decrypted = _encrypter!.decrypt64(cipherText);
    return decrypted; // Return the plain text
  }

  /// Encrypts the provided plain text using AES encryption with a custom key.
  ///
  /// [plainText] - The text to be encrypted.
  /// [keyString] - The custom key to be used for encryption.
  /// Returns the encrypted text as a base64-encoded string.
  String encryptWithKey(String plainText, String keyString) {
    final key =
        Key.fromUtf8(keyString.padRight(_keySize, ' ')); // Ensure 16-byte key
    final encrypter = Encrypter(AES(key, mode: AESMode.ecb, padding: 'PKCS7'));
    final encrypted = encrypter.encrypt(plainText);
    return encrypted.base64;
  }

  /// Decrypts the provided cipher text using AES decryption with a custom key.
  ///
  /// [cipherText] - The base64-encoded string of the text to be decrypted.
  /// [keyString] - The custom key to be used for decryption.
  /// Returns the decrypted plain text.
  String decryptWithKey(String cipherText, String keyString) {
    final key =
        Key.fromUtf8(keyString.padRight(_keySize, ' ')); // Ensure 16-byte key
    final encrypter = Encrypter(AES(key, mode: AESMode.ecb, padding: 'PKCS7'));
    final decrypted = encrypter.decrypt64(cipherText);
    return decrypted;
  }

  String decryptWithBase64Key(String cipherText, String base64Key) {
    // Decode the Base64 key into raw bytes
    final keyBytes = base64.decode(base64Key);

    // Create the AES key from the decoded bytes
    final key = Key(keyBytes);

    // Initialize the encrypter with AES ECB mode and PKCS7 padding
    final encrypter = Encrypter(AES(key, mode: AESMode.ecb, padding: 'PKCS7'));

    // Decrypt the cipher text which is Base64 encoded
    final decrypted = encrypter.decrypt64(cipherText);

    return decrypted;
  }

  String encryptWithBase64Key(String plainText, String base64Key) {
    // Decode the Base64 key into raw bytes
    final keyBytes = base64.decode(base64Key);

    // Create the AES key from the decoded bytes
    final key = Key(keyBytes);

    // Initialize the encrypter with AES ECB mode and PKCS7 padding
    final encrypter = Encrypter(AES(key, mode: AESMode.ecb, padding: 'PKCS7'));

    // Encrypt the plain text
    final encrypted = encrypter.encrypt(plainText);

    // Return the encrypted text as a Base64 encoded string
    return encrypted.base64;
  }
} 