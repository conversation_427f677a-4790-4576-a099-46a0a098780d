import 'dart:convert';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:path_provider/path_provider.dart';

import 'api_service.dart';
import 'api_endpoints.dart';
import 'cryptographer.dart';
import 'shared_preferences_helper.dart';
import '../../utils.dart';
import '../contact_details_helper.dart';

import '../app_logger.dart';
class AuthService {
  final ApiService _apiService = ApiService();
  final AesEncryption _aes = AesEncryption();

  /// Format phone number to international format
  String _formatMsisdn(String data) {
    data = data.replaceAll(RegExp(r'^\+'), '').replaceAll(' ', '');
    if (data.startsWith('254') && data.length == 12) {
      return data;
    } else if (data.startsWith('0') && data.length == 10) {
      return '254${data.substring(1)}'; // 07..., 01... etc.
    } else if ((data.startsWith('7') || data.startsWith('1')) && data.length == 9) {
      return '254$data';
    }
    return data;
  }

  /// Validate phone number format
  bool validatePhoneNumber(String phone) {
    final formattedPhone = _formatMsisdn(phone);
    return formattedPhone.length == 12 && formattedPhone.startsWith('254');
  }

  /// Validate PIN format
  bool validatePin(String pin) {
    return pin.length == 4 && RegExp(r'^\d{4}$').hasMatch(pin);
  }

  /// Clear app cache
  Future<void> _clearAppCache() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      if (cacheDir.existsSync()) {
        cacheDir.deleteSync(recursive: true);
      }
    } catch (e) {
      AppLogger.error('clearing cache: $e');
    }
  }

  /// Clear authentication token
  Future<void> _clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('_utopia');
  }

  /// Step 1: Verify phone number with backend
  Future<AuthResult> verifyPhoneNumber(String phoneNumber, BuildContext context) async {
    try {
      // Clear any stale tokens before starting a new phone verification
      await SharedPreferencesHelper.removeToken();
      await SharedPreferencesHelper.removeSecretKey();
      
      await _clearAppCache();
      
      // Get device ID and client ID
      String? deviceId = await _apiService.getDeviceId();
      String formattedPhone = _formatMsisdn(phoneNumber);
      var clientId = await SharedPreferencesHelper.getClientId();
      
      // Store phone number
      await SharedPreferencesHelper.setMsisdn(formattedPhone);

      // Prepare request body
      var requestBody = {
        'msisdn': formattedPhone,
        'clientId': clientId,
        'categoryName': 'Spotcash_App',
        'imei': deviceId,
        'appVersion': await Utils.getAppVersion()
      };

      AppLogger.info("Phone verification request: $requestBody");

      // Send request
      var response = await _apiService.postRequestWithoutToken(
        ApiEndpoints.verifyMsisdn,
        requestBody,
        context: context,
        isAuth: true,
        navigateToLoginCallback: () {
          // Handle timeout navigation if needed
        }
      );

      // Process response
      if (response['znak'] != null) {
        return await _processTokenAndResponse(response, formattedPhone);
      } else {
        return AuthResult.error('Invalid response from server');
      }
    } on TimeoutException {
      return AuthResult.error('Request timed out. Please try again.');
    } catch (e) {
      AppLogger.error('verifying phone: $e');
      await _clearToken();
      return AuthResult.error('Service is currently unavailable. Please try again later.');
    }
  }

  /// Process JWT token and decrypt response
  Future<AuthResult> _processTokenAndResponse(
      Map<String, dynamic> response, String phoneNumber) async {
    try {
      AppLogger.info('Processing token and response...');
      AppLogger.info('Response keys: ${response.keys}');
      
      String token = response['znak'];
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.remove('_utopia');
      await prefs.setString('_utopia', token);
      AppLogger.info('Token saved: $token');

      if (JwtDecoder.isExpired(token)) {
        AppLogger.info('Token is expired');
        return AuthResult.error('Session expired. Please try again.');
      }

      Map<String, dynamic> decodedClaims = JwtDecoder.decode(token);
      AppLogger.info('Decoded JWT claims: $decodedClaims');
      
      String tajemnica = decodedClaims['tajemnica'];
      await prefs.setString('_tajemnica', tajemnica);
      AppLogger.info('Secret key saved: $tajemnica');

      // Decrypt the response
      AppLogger.info('Attempting to decrypt hashedBody...');
      String decryptedResponse = _aes.decryptWithBase64Key(response['hashedBody'], tajemnica);
      AppLogger.info('Decrypted response: $decryptedResponse');

      // Parse and handle the decrypted JSON response
      Map<String, dynamic> responseData = jsonDecode(decryptedResponse);
      AppLogger.info('Parsed response data: $responseData');

      if (responseData['responseCode'] == '00') {
        AppLogger.info('Response code is 00, checking responseBody...');
        var responseBody = responseData['responseBody'][0];
        AppLogger.info('Response body: $responseBody');
        
        if (responseBody['resCode'] == '00') {
          AppLogger.info('Phone verification successful!');
          return AuthResult.success('Phone verified successfully');
        } else {
          AppLogger.info('Phone verification failed with resCode: ${responseBody['resCode']}');
          return AuthResult.error(responseBody['response_description'] ?? 'Phone verification failed');
        }
      } else {
        AppLogger.info('Response code is not 00: ${responseData['responseCode']}');
        String errorMessage = 'Service is currently unavailable. Please try again later.';
        if (responseData['responseBody'] != null &&
            responseData['responseBody'].isNotEmpty &&
            responseData['responseBody'][0]['response_description'] != null) {
          errorMessage = responseData['responseBody'][0]['response_description'];
        } else if (responseData['responseDescription'] != null) {
          errorMessage = responseData['responseDescription'];
        }
        AppLogger.error('message: $errorMessage');
        return AuthResult.error(errorMessage);
      }
    } catch (e) {
      AppLogger.error('processing token: $e');
      AppLogger.info('Stack trace: ${StackTrace.current}');
      return AuthResult.error('Error processing response');
    }
  }

  /// Step 2: Verify PIN
  Future<AuthResult> verifyPin(String phoneNumber, String pin, BuildContext context) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('_utopia');
      String? secretKey = prefs.getString('_tajemnica');

      if (token == null || secretKey == null) {
        return AuthResult.error('Session expired. Please restart the login process.');
      }

      String formattedPhone = _formatMsisdn(phoneNumber);
      var clientId = await SharedPreferencesHelper.getClientId();
      String? deviceId = await _apiService.getDeviceId();

      // Encrypt the request data
      _aes.encryptWithBase64Key(formattedPhone, secretKey);
      _aes.encryptWithBase64Key(pin, secretKey);
      _aes.encryptWithBase64Key(clientId.toString(), secretKey);
      _aes.encryptWithBase64Key(deviceId ?? '', secretKey);

      var requestBody = {
        'msisdn': formattedPhone,
        'pin': pin,
        'clientId': clientId,
        'imei': deviceId,
        'categoryName': 'Spotcash_App'
      };

      String encryptedRequestBody = _aes.encryptWithBase64Key(jsonEncode(requestBody), secretKey);

      AppLogger.info("PIN verification request prepared >>> $requestBody");

      // Send request
      AppLogger.info('Sending PIN verification request...');
      var response = await _apiService.postRequest(
        ApiEndpoints.verifyPinNew,
          {'hashedBody': encryptedRequestBody},
        context: context,
        isAuth: true,
        navigateToLoginCallback: () {
          // Handle timeout navigation if needed
        }
      );
      AppLogger.info('PIN verification response received: $response');

      // Decrypt and process response
      if (response['hashedBody'] != null) {
        // Check if response contains a new token with updated secret key
        String currentSecretKey = secretKey;
        if (response['znak'] != null) {
          // Extract new secret key from the new token
          String newToken = response['znak'];
          Map<String, dynamic> newTokenClaims = JwtDecoder.decode(newToken);
          if (newTokenClaims['tajemnica'] != null) {
            currentSecretKey = newTokenClaims['tajemnica'];
            AppLogger.info('Using updated secret key from new token: $currentSecretKey');
            // Update stored token and secret key
            await prefs.setString('_utopia', newToken);
            await prefs.setString('_tajemnica', currentSecretKey);
          }
        }
        
        String decryptedResponse = _aes.decryptWithBase64Key(response['hashedBody'], currentSecretKey);
        Map<String, dynamic> responseData = jsonDecode(decryptedResponse);
        
        AppLogger.info('PIN verification response: $decryptedResponse');

        if (responseData.containsKey('responseBody') && 
            responseData['responseBody'] is List && 
            responseData['responseBody'].isNotEmpty) {
          Map<String, dynamic> innerResponse = responseData['responseBody'][0];
          String responseCode = innerResponse['responseCode'];

          if (innerResponse.containsKey('customer') && innerResponse['customer'] != null) {
            try {
              Map<String, dynamic> customerData = jsonDecode(innerResponse['customer']);

              if (customerData.containsKey('spclients')) {
                Map<String, dynamic> spClients = customerData['spclients'];
                
                // Save contact details dynamically
                await ContactDetailsHelper.saveContactDetails(spClients);
                AppLogger.info('Contact details extracted and saved: ${spClients['clientName']}');
              }
            } catch (e) {
              AppLogger.error('parsing customer data: $e');
            }
          }

          if (responseCode == '00') {
            // PIN verified successfully, proceed to OTP
            return AuthResult.success('PIN verified successfully');
          } else if (responseCode == '04') {
            // Device validation required - proceed to OTP
            AppLogger.info('Device validation required, proceeding to OTP');
            return AuthResult.success('PIN verified successfully - device validation required');
          } else if (responseCode == '01') {
            return AuthResult.error('Invalid PIN. Please try again.');
          } else if (responseCode == '02') {
            // Need to change password
            String? spCustId = responseData['responseDescription'];
            if (spCustId != null) {
              await prefs.setString('spCustId', spCustId);
            }
            return AuthResult.changePasswordRequired('Password change required');
          } else {
            return AuthResult.error(innerResponse['response_description'] ?? 'PIN verification failed');
          }
        } else {
          return AuthResult.error('Invalid response format');
        }
      } else {
        return AuthResult.error('No valid response received');
      }
    } on TimeoutException {
      return AuthResult.error('Request timed out. Please try again.');
    } catch (e) {
      AppLogger.error('verifying PIN: $e');
      return AuthResult.error('Error during PIN verification');
    }
  }

  /// Step 3: Validate OTP
  Future<AuthResult> validateOtp(String phoneNumber, String otp, BuildContext context) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? secretKey = prefs.getString('_tajemnica');

      if (secretKey == null) {
        return AuthResult.error('Session expired. Please restart the login process.');
      }

      String formattedPhone = _formatMsisdn(phoneNumber);
      String? clientId = await SharedPreferencesHelper.getClientId();
      String? deviceId = await _apiService.getDeviceId();

      // Encrypt request data
      String encryptedMsisdn = _aes.encryptWithBase64Key(formattedPhone, secretKey);
      String encryptedOtp = _aes.encryptWithBase64Key(otp, secretKey);
      String encryptedImei = _aes.encryptWithBase64Key(deviceId ?? '', secretKey);
      String encryptedClientId = _aes.encryptWithBase64Key(clientId ?? '', secretKey);

      var requestBody = {
        'msisdn': encryptedMsisdn,
        'otp': encryptedOtp,
        'imei': encryptedImei,
        'clientId': encryptedClientId,
        'categoryName': 'Spotcash_App',
      };

      AppLogger.info("OTP validation request prepared");

      // Send request
      var response = await _apiService.postRequest(
        ApiEndpoints.validateOtp,
        requestBody,
        context: context,
        isAuth: true,
        navigateToLoginCallback: () {
          // Handle timeout navigation if needed
        }
      );

      if (response != null && response['responseCode'] == '00') {
        String spCustId = response['responseDescription'].toString();
        await prefs.setString('spCustId', spCustId);
        return AuthResult.success('OTP validated successfully');
      } else if (response != null && response['responseCode'] == '800') {
        return AuthResult.securityQuestionsRequired('Security questions setup required');
      } else {
        return AuthResult.error(response?['message'] ?? 'Invalid OTP');
      }
    } on TimeoutException {
      return AuthResult.error('Request timed out. Please try again.');
    } catch (e) {
      AppLogger.error('validating OTP: $e');
      return AuthResult.error('Error during OTP validation');
    }
  }

  /// Resend OTP
  Future<AuthResult> resendOtp(String phoneNumber, BuildContext context) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? secretKey = prefs.getString('_tajemnica');

      if (secretKey == null) {
        return AuthResult.error('Session expired. Please restart the login process.');
      }

      String formattedPhone = _formatMsisdn(phoneNumber);
      String? clientId = await SharedPreferencesHelper.getClientId();

      // Encrypt request data
      String encryptedMsisdn = _aes.encryptWithBase64Key(formattedPhone, secretKey);
      String encryptedClientId = _aes.encryptWithBase64Key(clientId ?? '', secretKey);

      var requestBody = {
        'msisdn': encryptedMsisdn,
        'clientId': encryptedClientId,
        'categoryName': 'Spotcash_App',
        'action': 'resend_otp',
      };

      AppLogger.info("Resend OTP request prepared");

      // Send request to the same validateOtp endpoint with action parameter
      var response = await _apiService.postRequest(
        ApiEndpoints.validateOtp,
        requestBody,
        context: context,
        isAuth: true,
        navigateToLoginCallback: () {
          // Handle timeout navigation if needed
        }
      );

      if (response != null && response['responseCode'] == '00') {
        return AuthResult.success('OTP has been resent successfully');
      } else {
        String errorMessage = response?['responseDescription'] ?? 
                             response?['message'] ?? 
                             'Failed to resend OTP';
        return AuthResult.error(errorMessage);
      }
    } on TimeoutException {
      return AuthResult.error('Request timed out. Please try again.');
    } catch (e) {
      AppLogger.error('resending OTP: $e');
      return AuthResult.error('Error during OTP resend');
    }
  }

  /// Complete login process (phone + PIN + OTP in sequence)
  Future<AuthResult> completeLogin(String phoneNumber, String pin, String otp, BuildContext context) async {
    // Step 1: Verify phone
    AuthResult phoneResult = await verifyPhoneNumber(phoneNumber, context);
    if (!phoneResult.isSuccess) {
      return phoneResult;
    }

    // Step 2: Verify PIN
    AuthResult pinResult = await verifyPin(phoneNumber, pin, context);
    if (!pinResult.isSuccess) {
      return pinResult;
    }

    // Step 3: Validate OTP
    AuthResult otpResult = await validateOtp(phoneNumber, otp, context);
    return otpResult;
  }

  /// Logout user
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('_utopia');
    await prefs.remove('_tajemnica');
    await prefs.remove('spCustId');
    await SharedPreferencesHelper.clearSharedPreferences();
  }
}

/// Result class for authentication operations
class AuthResult {
  final bool isSuccess;
  final String message;
  final AuthResultType type;

  AuthResult._(this.isSuccess, this.message, this.type);

  factory AuthResult.success(String message) {
    return AuthResult._(true, message, AuthResultType.success);
  }

  factory AuthResult.error(String message) {
    return AuthResult._(false, message, AuthResultType.error);
  }

  factory AuthResult.changePasswordRequired(String message) {
    return AuthResult._(false, message, AuthResultType.changePasswordRequired);
  }

  factory AuthResult.securityQuestionsRequired(String message) {
    return AuthResult._(false, message, AuthResultType.securityQuestionsRequired);
  }
}

enum AuthResultType {
  success,
  error,
  changePasswordRequired,
  securityQuestionsRequired,
} 