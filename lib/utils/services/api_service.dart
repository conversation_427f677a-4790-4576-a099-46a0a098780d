import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:synchronized/synchronized.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../app_logger.dart';
/// A service class for making HTTP requests with thread safety using a lock.
class ApiService {
  final String? baseUrl = dotenv.env['BASE_URL'];

  // Lock to ensure thread safety
  final _lock = Lock();

  // Default headers for requests
  final Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  /// Set the authorization token from SharedPreferences if available.
  Future<void> _setAuthToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('_utopia'); // Retrieve token

    if (token != null && token.isNotEmpty) {
      // AppLogger.info('Setting auth token: ${token.substring(0, 50)}...');
      _headers['Ulinzi'] = 'Bearer $token'; // Add token to headers
    } else {
      AppLogger.info('No auth token found in SharedPreferences');
      _headers.remove('Ulinzi'); // Remove if not available
    }
  }

  /// Get timeout duration based on request type
  Future<Duration> _getRequestTimeout(bool isTransactional) async {
    // Default timeout values
    int seconds = isTransactional ? 60 : 30;
    return Duration(seconds: seconds);
  }

  /// Show custom timeout dialog
  void _showTimeoutDialog(BuildContext context, bool isTransactional,
      bool isAuth, Function? retryCallback,
      {VoidCallback? navigateToHomeCallback,
        VoidCallback? navigateToLoginCallback}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Request Timeout'),
          content: Text('The request has timed out. Please try again.'),
          actions: [
            if (!isTransactional && !isAuth && retryCallback != null)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  retryCallback();
                },
                child: Text('Retry'),
              ),
            if (isAuth && navigateToLoginCallback != null)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  navigateToLoginCallback();
                },
                child: Text('Back to Login'),
              ),
            if (isTransactional && !isAuth && navigateToHomeCallback != null)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  navigateToHomeCallback();
                },
                child: Text('Go Home'),
              ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Get device ID
  Future<String?> getDeviceId() async {
    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        return androidInfo.id; // Use Android ID
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        return iosInfo.identifierForVendor; // Use iOS Vendor ID
      }
    } catch (e) {
      AppLogger.error('getting device ID: $e');
    }
    return null;
  }

  /// Sends a GET request to the specified endpoint with timeout.
  Future<dynamic> getRequest(String endpoint,
      {BuildContext? context,
        bool isTransactional = false,
        bool isAuth = false,
        Function? retryCallback,
        VoidCallback? navigateToHomeCallback,
        VoidCallback? navigateToLoginCallback}) async {
    await _setAuthToken(); // Set the token before making the request
    final timeout = await _getRequestTimeout(isTransactional);

    return _lock.synchronized(() async {
      final url = Uri.parse('$baseUrl$endpoint');
      try {
        final response = await http.get(url, headers: _headers).timeout(timeout,
            onTimeout: () {
              if (context != null) {
                _showTimeoutDialog(context, isTransactional, isAuth, retryCallback,
                    navigateToHomeCallback: navigateToHomeCallback,
                    navigateToLoginCallback: navigateToLoginCallback);
              }
              throw TimeoutException('Request timed out');
            });

        return _processResponse(response);
      } on TimeoutException {
        throw TimeoutException('Request timed out');
      } catch (e) {
        throw Exception('Error fetching data: $e');
      }
    });
  }

  /// Sends a POST request to the specified endpoint with the provided data and timeout.
  Future<dynamic> postRequest(String endpoint, Map<String, dynamic> data,
      {BuildContext? context,
        bool isTransactional = false,
        bool isAuth = false,
        Map<String, String>? headers,
        Function? retryCallback,
        VoidCallback? navigateToHomeCallback,
        VoidCallback? navigateToLoginCallback}) async {
    // Set token from SharedPreferences if available
    await _setAuthToken();
    final timeout = await _getRequestTimeout(isTransactional);

    // Use provided headers if passed; otherwise, use default _headers
    final Map<String, String> requestHeaders = headers ?? _headers;

    return _lock.synchronized(() async {
      final url = Uri.parse('$baseUrl$endpoint');

      AppLogger.info("This is the url being accessed >>> $url");
      try {
        // Make the POST request with custom headers and timeout
        final response = await http
            .post(url, headers: requestHeaders, body: jsonEncode(data))
            .timeout(timeout, onTimeout: () {
          if (context != null) {
            _showTimeoutDialog(context, isTransactional, isAuth, retryCallback,
                navigateToHomeCallback: navigateToHomeCallback,
                navigateToLoginCallback: navigateToLoginCallback);
          }
          throw TimeoutException('Request timed out');
        });

        return _processResponse(response);
      } on TimeoutException {
        throw TimeoutException('Request timed out');
      } catch (e) {
        throw Exception('Error posting data: $e');
      }
    });
  }

  ///Sends a POST request without the token but with timeout
  Future<dynamic> postRequestWithoutToken(
      String endpoint, Map<String, dynamic> data,
      {BuildContext? context,
        bool isTransactional = false,
        bool isAuth = false,
        Map<String, String>? headers,
        Function? retryCallback,
        VoidCallback? navigateToHomeCallback,
        VoidCallback? navigateToLoginCallback}) async {
    final timeout = await _getRequestTimeout(isTransactional);

    // Use provided headers if passed, but start with a clean default map
    // to ensure no stale 'Ulinzi' token is ever sent.
    final Map<String, String> requestHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...(headers ?? {}),
    };

    return _lock.synchronized(() async {
      final url = Uri.parse('$baseUrl$endpoint');

      AppLogger.info("This is the url being accessed >>> $url");
      try {
        // Make the POST request with custom headers and timeout
        final response = await http
            .post(url, headers: requestHeaders, body: jsonEncode(data))
            .timeout(timeout, onTimeout: () {
          if (context != null) {
            _showTimeoutDialog(context, isTransactional, isAuth, retryCallback,
                navigateToHomeCallback: navigateToHomeCallback,
                navigateToLoginCallback: navigateToLoginCallback);
          }
          throw TimeoutException('Request timed out');
        });

        return _processResponse(response);
      } on TimeoutException {
        throw TimeoutException('Request timed out');
      } catch (e) {
        throw Exception('Error posting data: $e');
      }
    });
  }

  /// Process HTTP response
  dynamic _processResponse(http.Response response) {
    try {
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return jsonDecode(response.body);
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      if (e is FormatException) {
        throw Exception('Invalid JSON response: ${response.body}');
      }
      rethrow;
    }
  }
} 