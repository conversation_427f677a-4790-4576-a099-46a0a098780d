import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/api_service.dart';
import '../services/api_endpoints.dart';
import '../services/cryptographer.dart';
import '../services/shared_preferences_helper.dart';
import 'image_debug_helper.dart';

import '../app_logger.dart';
/// Helper class for inspecting what's actually stored in the database
class DatabaseInspector {
  final ApiService _apiService = ApiService();
  final AesEncryption _aes = AesEncryption();

  /// Fetch raw profile picture data from database for inspection
  Future<Map<String, dynamic>> inspectProfilePictureInDatabase() async {
    try {
      // Get required data from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      String? phoneNumber = prefs.getString('msisdn');
      String? clientId = await SharedPreferencesHelper.getClientId();
      String? secretKey = prefs.getString('_tajemnica');

      if (phoneNumber == null || clientId == null || secretKey == null) {
        return {
          'error': 'Missing required data for database inspection',
          'phoneNumber': phoneNumber,
          'clientId': clientId,
          'hasSecretKey': secretKey != null,
        };
      }

      // Prepare request body
      var requestBody = {
        'msisdn': phoneNumber,
        'clientId': clientId,
        'action': 'fetch_profile_picture',
      };

      AppLogger.info('Inspecting database for user: $phoneNumber');

      // Encrypt the request body
      String plainTextRequestBody = jsonEncode(requestBody);
      String encryptedRequestBody = _aes.encryptWithBase64Key(plainTextRequestBody, secretKey);

      // Send request to backend
      var response = await _apiService.postRequest(
        ApiEndpoints.fetchProfilePicture,
        {'hashedBody': encryptedRequestBody},
        isAuth: true,
      );

      Map<String, dynamic> result = {
        'user': phoneNumber,
        'clientId': clientId,
        'requestSent': true,
        'rawResponse': response,
      };

      // Process response
      if (response != null && response['hashedBody'] != null) {
        try {
          String decryptedResponse = _aes.decryptWithBase64Key(
            response['hashedBody'],
            secretKey,
          );

          Map<String, dynamic> responseData = jsonDecode(decryptedResponse);
          result['decryptedResponse'] = responseData;
          result['responseCode'] = responseData['responseCode'];
          result['message'] = responseData['message'];

          // Check if there's profile picture data
          if (responseData['profilePicture'] != null) {
            String base64Image = responseData['profilePicture'];
            result['hasProfilePicture'] = true;
            result['base64Length'] = base64Image.length;
            result['base64Preview'] = base64Image.substring(0, base64Image.length > 200 ? 200 : base64Image.length);
            
            // Analyze the base64 data
            Map<String, dynamic> base64Analysis = ImageDebugHelper.getBase64DebugInfo(base64Image);
            result['base64Analysis'] = base64Analysis;

            // Try to save the image for manual inspection
            try {
              String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
              String debugPath = '/tmp/db_profile_image_$timestamp.jpg';
              await ImageDebugHelper.saveBase64ToFile(base64Image, debugPath);
              result['debugImageSaved'] = debugPath;
            } catch (e) {
              result['debugImageSaveError'] = e.toString();
            }

          } else {
            result['hasProfilePicture'] = false;
            result['profilePictureField'] = responseData.containsKey('profilePicture') ? 'null' : 'missing';
          }

        } catch (decryptionError) {
          result['decryptionError'] = decryptionError.toString();
          result['encryptedResponseLength'] = response['hashedBody']?.length;
        }
      } else {
        result['responseError'] = 'No hashedBody in response';
      }

      return result;

    } catch (e) {
      return {
        'error': 'Exception during database inspection: $e',
      };
    }
  }

  /// Compare what we send vs what we get back
  Future<Map<String, dynamic>> compareUploadAndFetch(File imageFile) async {
    try {
      Map<String, dynamic> result = {
        'timestamp': DateTime.now().toIso8601String(),
      };

      // First, get original image info
      Map<String, dynamic> originalInfo = await ImageDebugHelper.getImageDebugInfo(imageFile);
      result['originalImage'] = originalInfo;

      // Simulate the upload process (without actually uploading)
      Uint8List originalBytes = await imageFile.readAsBytes();
      String originalBase64 = base64Encode(originalBytes);
      result['originalBase64Length'] = originalBase64.length;
      result['originalBase64Hash'] = ImageDebugHelper.generateImageHash(originalBytes);

      // Test the full encryption/decryption cycle
      final prefs = await SharedPreferences.getInstance();
      String? secretKey = prefs.getString('_tajemnica');
      String? phoneNumber = prefs.getString('msisdn');
      String? clientId = await SharedPreferencesHelper.getClientId();

      if (secretKey != null && phoneNumber != null && clientId != null) {
        // Simulate the request body that would be sent
        var requestBody = {
          'msisdn': phoneNumber,
          'clientId': clientId,
          'profilePicture': originalBase64,
          'action': 'save_profile_picture',
        };

        String plainTextRequestBody = jsonEncode(requestBody);
        String encryptedRequestBody = _aes.encryptWithBase64Key(plainTextRequestBody, secretKey);
        
        // Simulate decryption (what the server would do)
        String decryptedRequestBody = _aes.decryptWithBase64Key(encryptedRequestBody, secretKey);
        Map<String, dynamic> decryptedRequest = jsonDecode(decryptedRequestBody);
        String retrievedBase64 = decryptedRequest['profilePicture'];

        result['encryptionCycleIntegrity'] = originalBase64 == retrievedBase64;
        result['retrievedBase64Length'] = retrievedBase64.length;
        
        if (originalBase64 != retrievedBase64) {
          result['base64Difference'] = {
            'originalLength': originalBase64.length,
            'retrievedLength': retrievedBase64.length,
            'firstDifferenceAt': _findFirstDifference(originalBase64, retrievedBase64),
          };
        }

        // Test decoding the retrieved base64
        try {
          Uint8List retrievedBytes = base64Decode(retrievedBase64);
          result['retrievedImageHash'] = ImageDebugHelper.generateImageHash(retrievedBytes);
          result['imageIntegrity'] = ImageDebugHelper.compareImages(originalBytes, retrievedBytes);
        } catch (e) {
          result['retrievedImageDecodeError'] = e.toString();
        }
      }

      return result;

    } catch (e) {
      return {
        'error': 'Exception during comparison: $e',
      };
    }
  }

  int? _findFirstDifference(String str1, String str2) {
    int minLength = str1.length < str2.length ? str1.length : str2.length;
    for (int i = 0; i < minLength; i++) {
      if (str1[i] != str2[i]) {
        return i;
      }
    }
    return str1.length != str2.length ? minLength : null;
  }

  /// Print inspection results in a readable format
  static void printInspectionResults(Map<String, dynamic> results) {
    AppLogger.info('=== DATABASE INSPECTION RESULTS ===');
    _printMap(results, 0);
    AppLogger.info('===================================');
  }

  static void _printMap(Map<String, dynamic> map, int indent) {
    String indentStr = '  ' * indent;
    map.forEach((key, value) {
      if (value is Map<String, dynamic>) {
        AppLogger.info('$indentStr$key:');
        _printMap(value, indent + 1);
      } else {
        AppLogger.info('$indentStr$key: $value');
      }
    });
  }
}
