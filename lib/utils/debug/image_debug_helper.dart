import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';

import '../app_logger.dart';
/// Helper class for debugging image encoding/decoding issues
class ImageDebugHelper {
  
  /// Generates a hash of the image bytes for comparison
  static String generateImageHash(Uint8List imageBytes) {
    var digest = sha256.convert(imageBytes);
    return digest.toString();
  }

  /// Validates if a base64 string is a valid image
  static bool isValidBase64Image(String base64String) {
    try {
      // Try to decode the base64
      Uint8List bytes = base64Decode(base64String);
      
      // Check if it has image file signatures
      if (bytes.length < 4) return false;
      
      // Check for common image signatures
      // JPEG: FF D8 FF
      if (bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) {
        return true;
      }
      
      // PNG: 89 50 4E 47
      if (bytes.length >= 8 && 
          bytes[0] == 0x89 && bytes[1] == 0x50 && 
          bytes[2] == 0x4E && bytes[3] == 0x47) {
        return true;
      }
      
      // GIF: 47 49 46 38
      if (bytes.length >= 6 &&
          bytes[0] == 0x47 && bytes[1] == 0x49 && 
          bytes[2] == 0x46 && bytes[3] == 0x38) {
        return true;
      }
      
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Gets image format from bytes
  static String getImageFormat(Uint8List bytes) {
    if (bytes.length < 4) return 'Unknown';
    
    // JPEG
    if (bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) {
      return 'JPEG';
    }
    
    // PNG
    if (bytes.length >= 8 && 
        bytes[0] == 0x89 && bytes[1] == 0x50 && 
        bytes[2] == 0x4E && bytes[3] == 0x47) {
      return 'PNG';
    }
    
    // GIF
    if (bytes.length >= 6 &&
        bytes[0] == 0x47 && bytes[1] == 0x49 && 
        bytes[2] == 0x46 && bytes[3] == 0x38) {
      return 'GIF';
    }
    
    return 'Unknown';
  }

  /// Comprehensive debug info for an image file
  static Future<Map<String, dynamic>> getImageDebugInfo(File imageFile) async {
    try {
      Uint8List originalBytes = await imageFile.readAsBytes();
      String base64String = base64Encode(originalBytes);
      Uint8List decodedBytes = base64Decode(base64String);
      
      return {
        'file_path': imageFile.path,
        'file_exists': await imageFile.exists(),
        'original_size': originalBytes.length,
        'original_hash': generateImageHash(originalBytes),
        'original_format': getImageFormat(originalBytes),
        'base64_length': base64String.length,
        'base64_valid': isValidBase64Image(base64String),
        'base64_preview': base64String.substring(0, base64String.length > 100 ? 100 : base64String.length),
        'decoded_size': decodedBytes.length,
        'decoded_hash': generateImageHash(decodedBytes),
        'decoded_format': getImageFormat(decodedBytes),
        'encoding_integrity': generateImageHash(originalBytes) == generateImageHash(decodedBytes),
      };
    } catch (e) {
      return {
        'error': e.toString(),
      };
    }
  }

  /// Debug info for base64 string from server
  static Map<String, dynamic> getBase64DebugInfo(String base64String) {
    try {
      Uint8List decodedBytes = base64Decode(base64String);
      
      return {
        'base64_length': base64String.length,
        'base64_valid': isValidBase64Image(base64String),
        'base64_preview': base64String.substring(0, base64String.length > 100 ? 100 : base64String.length),
        'decoded_size': decodedBytes.length,
        'decoded_hash': generateImageHash(decodedBytes),
        'decoded_format': getImageFormat(decodedBytes),
        'first_bytes': decodedBytes.take(10).toList(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'base64_length': base64String.length,
        'base64_preview': base64String.substring(0, base64String.length > 100 ? 100 : base64String.length),
      };
    }
  }

  /// Compare two images by hash
  static bool compareImages(Uint8List image1, Uint8List image2) {
    return generateImageHash(image1) == generateImageHash(image2);
  }

  /// Save base64 string to file for manual inspection
  static Future<void> saveBase64ToFile(String base64String, String filePath) async {
    try {
      Uint8List bytes = base64Decode(base64String);
      File file = File(filePath);
      await file.writeAsBytes(bytes);
      AppLogger.info('Base64 image saved to: $filePath');
    } catch (e) {
      AppLogger.error('saving base64 to file: $e');
    }
  }

  /// Print comprehensive debug info
  static void printDebugInfo(Map<String, dynamic> debugInfo) {
    AppLogger.info('=== IMAGE DEBUG INFO ===');
    debugInfo.forEach((key, value) {
      AppLogger.info('$key: $value');
    });
    AppLogger.info('========================');
  }
}
