# Tower Sacco Mobile App

![Flutter](https://img.shields.io/badge/Flutter-%2302569B.svg?style=for-the-badge&logo=Flutter&logoColor=white)
![Dart](https://img.shields.io/badge/Dart-%230175C2.svg?style=for-the-badge&logo=Dart&logoColor=white)
![Version](https://img.shields.io/badge/Version-1.0.0-brightgreen.svg?style=for-the-badge)

Tower Sacco Mobile App is a modern, feature-rich financial services application designed specifically for Tower Sacco members. Built with Flutter for seamless cross-platform compatibility, the app provides a comprehensive suite of banking and financial services with a beautiful, intuitive user interface.

## ✨ Key Features

### 🔐 **Authentication & Security**
- **Secure Login**: Multi-factor authentication with OTP verification
- **PIN Security**: Secure PIN-based transactions
- **Session Management**: Automatic logout for enhanced security

### 🏠 **Modern Dashboard**
- **Account Overview**: Multiple account types (Savings, Shares, NWD, Loan)
- **Interactive Carousel**: Swipe through different accounts with smooth animations
- **Balance Visibility Toggle**: Show/hide sensitive financial information
- **Recent Transactions**: Real-time transaction history with categorization
- **Customizable Favorites**: Personalize quick access to frequently used services

### 💰 **Comprehensive Transaction Services**

#### **General Banking**
- **Deposits**: Easy money deposits to various account types
- **Withdrawals**: Secure cash withdrawals with multiple verification steps
- **Inter-account Transfers**: Seamless transfers between your accounts
- **Account Statements**: Mini and full statements with email delivery options

#### **Loan Management**
- **Loan Applications**: Digital loan application with real-time processing
- **Active Loan Tracking**: Monitor loan balances, payments, and schedules
- **Loan Calculator**: Built-in calculator for loan planning
- **Guarantor Management**: View and manage loan guarantors

#### **B2B Services**
- **Bank Transfers**: Transfer funds to external bank accounts
- **Pay to Till**: M-Pesa till payments for business transactions
- **M-Pesa Float**: Purchase M-Pesa float for business operations

#### **Utility Payments**
- **DSTV**: Satellite TV subscription payments
- **Zuku**: Cable TV and internet bill payments
- **Star Times**: Digital TV subscription management
- **Nairobi Water**: Water bill payments and account management

### 🎨 **Modern UI/UX Design**
- **Welcome Screen**: Beautiful carousel with custom SVG illustrations
- **Dark/Light Mode**: Adaptive themes for comfortable viewing
- **Smooth Animations**: Flutter Animate integration for fluid interactions
- **Material Design 3**: Latest design principles with custom color schemes
- **Responsive Layout**: Optimized for various screen sizes

### 📱 **Enhanced User Experience**
- **Profile Management**: Complete profile customization with image upload
- **Notifications**: Real-time alerts and updates
- **Support Integration**: Direct access to customer support channels
- **Social Media Links**: Connect with Tower Sacco on various platforms
- **ATM Locator**: Find nearby ATMs with Google Maps integration

### 🔧 **Technical Features**
- **State Management**: Provider pattern for efficient state handling
- **Local Storage**: Secure local data persistence
- **Image Handling**: Profile picture upload and management
- **Contact Integration**: Access device contacts for transfers
- **Share Functionality**: Share transaction receipts and statements
- **Screenshot Capture**: Generate and share transaction proofs

## 📱 Screenshots

![Welcome Screen](img.png)
*Modern welcome screen with custom SVG illustrations*

![Dashboard](imgTwo.png)
*Feature-rich dashboard with account carousel and services*

## 🚀 Getting Started

### Prerequisites

- **Flutter SDK**: Version 3.6.0 or higher
- **Dart SDK**: Version 3.6.0 or higher
- **Development Environment**: Android Studio, VS Code, or IntelliJ IDEA
- **Platform Requirements**: 
  - Android: API level 21+ (Android 5.0+)
  - iOS: iOS 12.0+

### Installation

1. **Clone the repository:**
   ```bash
   git clone https://<EMAIL>/scm/spot/spotcash-flutter-app---option-2.git
   cd tangazoletusacco
   ```

2. **Install dependencies:**
   ```bash
   flutter pub get
   ```

3. **Configure assets:**
   ```bash
   flutter packages pub run flutter_launcher_icons:main
   ```

4. **Run the application:**
   ```bash
   flutter run
   ```

## 🏗️ Project Architecture

```
lib/
├── auth/                    # Authentication related files
├── models/                  # Data models and entities
├── onboarding/             # App onboarding screens
├── providers/              # State management providers
├── screens/                # Main application screens
│   ├── welcome_screen.dart
│   ├── login_screen.dart
│   ├── profile_screen.dart
│   ├── support_screen.dart
│   └── ...
├── transactions/           # Transaction management
│   ├── screens/           # Transaction screens
│   ├── widgets/           # Transaction widgets
│   ├── services/          # Transaction services
│   └── models/            # Transaction models
├── utils/                  # Utilities and constants
├── widgets/               # Reusable UI components
└── main.dart              # Application entry point
```

## 📦 Dependencies

### **Core Dependencies**
- `flutter`: SDK framework
- `provider`: State management
- `shared_preferences`: Local data persistence

### **UI & Animation**
- `flutter_animate`: Advanced animations
- `carousel_slider`: Interactive carousels
- `google_nav_bar`: Modern navigation bar
- `flutter_svg`: SVG image support
- `smooth_page_indicator`: Page indicators

### **Functionality**
- `local_auth`: Biometric authentication
- `image_picker`: Image selection and capture
- `url_launcher`: External URL handling
- `geolocator`: Location services
- `google_maps_flutter`: Maps integration
- `flutter_contacts`: Contact access
- `share_plus`: Content sharing
- `screenshot`: Screen capture
- `fl_chart`: Data visualization

### **Utilities**
- `intl`: Internationalization
- `path_provider`: File system access
- `permission_handler`: Device permissions
- `font_awesome_flutter`: Icon library

## 🎨 Design System

### **Color Palette**
- **Primary**: `#6B4E71` (Purple)
- **Secondary**: `#E91E63` (Pink)
- **Background**: Adaptive (Light/Dark mode)
- **Text**: High contrast for accessibility

### **Typography**
- **Headers**: Bold, clear hierarchy
- **Body Text**: Readable, consistent sizing
- **Interactive Elements**: Emphasized styling

### **Components**
- **Cards**: Elevated with subtle shadows
- **Buttons**: Rounded corners with haptic feedback
- **Forms**: Clean, validated inputs
- **Navigation**: Intuitive bottom navigation

## 🔒 Security Features

- **End-to-end Encryption**: Secure data transmission
- **PIN Protection**: Transaction-level security
- **Session Management**: Automatic timeout
- **Secure Storage**: Encrypted local data

## 🌐 Platform Support

- ✅ **Android**: Full feature support
- ✅ **iOS**: Full feature support
- ✅ **Web**: Progressive Web App capabilities
- ✅ **Desktop**: Windows, macOS, Linux support

## 🤝 Contributing

We welcome contributions! Please follow these guidelines:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit changes**: `git commit -m 'Add amazing feature'`
4. **Push to branch**: `git push origin feature/amazing-feature`
5. **Open a Pull Request**

### **Development Guidelines**
- Follow Flutter/Dart style guidelines
- Write comprehensive tests
- Update documentation
- Ensure responsive design
- Test on multiple devices

## 📄 License

This project is proprietary software owned by Tower Sacco Ltd. All rights reserved.

## 📞 Support & Contact

**Tower Sacco Ltd**
- **Website**: [towersacco.co.ke](https://towersacco.co.ke)
- **Email**: <EMAIL>
- **Phone**: +254 792 333 111
- **Address**: Olkalou Town, Kenya

**Social Media**
- **Facebook**: [@TowerSaccoLtd](https://www.facebook.com/TowerSaccoLtd)
- **Twitter**: [@towersaccoltd](https://x.com/towersaccoltd)

## 🏆 Acknowledgments

- **Development Team**: Tangazoletu Sacco PSD Department
- **Design Inspiration**: Modern fintech applications
- **Flutter Community**: For excellent packages and support
- **Tower Sacco Members**: For valuable feedback and testing

---

**Built with ❤️ for Tower Sacco members**
