<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="phoneGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1F2937"/>
      <stop offset="100%" style="stop-color:#374151"/>
    </linearGradient>
    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981"/>
      <stop offset="100%" style="stop-color:#059669"/>
    </linearGradient>
    <linearGradient id="transferGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B"/>
      <stop offset="100%" style="stop-color:#D97706"/>
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Left phone (sender) -->
  <rect x="30" y="50" width="45" height="80" rx="12" fill="url(#phoneGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2" filter="url(#glow)"/>
  <rect x="35" y="60" width="35" height="60" rx="6" fill="url(#screenGradient)"/>
  
  <!-- M-PESA logo on left phone -->
  <text x="52.5" y="75" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9" font-weight="bold">M-PESA</text>
  <circle cx="52.5" cy="90" r="12" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.5)" stroke-width="2"/>
  <text x="52.5" y="95" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">KSh</text>
  
  <!-- Send button -->
  <rect x="40" y="105" width="25" height="8" rx="4" fill="rgba(245, 158, 11, 0.9)"/>
  <text x="52.5" y="111" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="6" font-weight="bold">SEND</text>
  
  <!-- Right phone (receiver) -->
  <rect x="125" y="50" width="45" height="80" rx="12" fill="url(#phoneGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2" filter="url(#glow)"/>
  <rect x="130" y="60" width="35" height="60" rx="6" fill="url(#screenGradient)"/>
  
  <!-- Received notification -->
  <text x="147.5" y="75" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="8" font-weight="bold">RECEIVED</text>
  <circle cx="147.5" cy="90" r="12" fill="rgba(16, 185, 129, 0.3)" stroke="rgba(16, 185, 129, 0.8)" stroke-width="2"/>
  <text x="147.5" y="95" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">✓</text>
  
  <!-- Amount received -->
  <rect x="135" y="105" width="25" height="8" rx="4" fill="rgba(16, 185, 129, 0.9)"/>
  <text x="147.5" y="111" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="6" font-weight="bold">KSh 5,000</text>
  
  <!-- Transfer arrow with animation effect -->
  <path d="M 80 95 Q 100 75 120 95" stroke="url(#transferGradient)" stroke-width="5" fill="none" filter="url(#glow)"/>
  <polygon points="115,92 125,95 115,98" fill="url(#transferGradient)" filter="url(#glow)"/>
  
  <!-- Money symbols floating around transfer -->
  <circle cx="100" cy="40" r="8" fill="url(#transferGradient)" filter="url(#glow)"/>
  <text x="100" y="45" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">KSh</text>
  
  <!-- Speed lines for instant transfer -->
  <path d="M 85 90 L 95 90" stroke="rgba(255,255,255,0.8)" stroke-width="3" stroke-linecap="round"/>
  <path d="M 85 95 L 95 95" stroke="rgba(255,255,255,0.6)" stroke-width="3" stroke-linecap="round"/>
  <path d="M 85 100 L 95 100" stroke="rgba(255,255,255,0.8)" stroke-width="3" stroke-linecap="round"/>
  
  <!-- Network signals -->
  <rect x="35" y="45" width="3" height="6" rx="1.5" fill="rgba(16, 185, 129, 0.8)"/>
  <rect x="40" y="42" width="3" height="9" rx="1.5" fill="rgba(16, 185, 129, 0.9)"/>
  <rect x="45" y="39" width="3" height="12" rx="1.5" fill="rgba(16, 185, 129, 1)"/>
  
  <rect x="152" y="45" width="3" height="6" rx="1.5" fill="rgba(16, 185, 129, 0.8)"/>
  <rect x="157" y="42" width="3" height="9" rx="1.5" fill="rgba(16, 185, 129, 0.9)"/>
  <rect x="162" y="39" width="3" height="12" rx="1.5" fill="rgba(16, 185, 129, 1)"/>
  
  <!-- Instant transfer badge -->
  <ellipse cx="100" cy="140" rx="25" ry="12" fill="rgba(16, 185, 129, 0.2)" stroke="rgba(16, 185, 129, 0.8)" stroke-width="2"/>
  <text x="100" y="137" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-family="Arial, sans-serif" font-size="8" font-weight="bold">INSTANT</text>
  <text x="100" y="145" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="8" font-weight="bold">TRANSFER</text>
  
  <!-- Security elements -->
  <rect x="90" y="160" width="20" height="12" rx="3" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)" stroke-width="1"/>
  <path d="M 95 160 Q 100 155 105 160" stroke="rgba(255,255,255,0.6)" stroke-width="2" fill="none"/>
  <text x="100" y="169" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="6" font-weight="bold">SECURE</text>
  
  <!-- Transaction success indicators -->
  <circle cx="20" cy="100" r="6" fill="rgba(16, 185, 129, 0.8)" filter="url(#glow)"/>
  <text x="20" y="104" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="8" font-weight="bold">✓</text>
  
  <circle cx="180" cy="100" r="6" fill="rgba(16, 185, 129, 0.8)" filter="url(#glow)"/>
  <text x="180" y="104" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="8" font-weight="bold">✓</text>
</svg> 