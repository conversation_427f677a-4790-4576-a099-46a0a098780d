<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="coinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981"/>
      <stop offset="100%" style="stop-color:#059669"/>
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6"/>
      <stop offset="100%" style="stop-color:#1D4ED8"/>
    </linearGradient>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B"/>
      <stop offset="100%" style="stop-color:#D97706"/>
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main credit card -->
  <rect x="50" y="80" width="100" height="60" rx="12" fill="url(#cardGradient)" filter="url(#glow)"/>
  <rect x="50" y="80" width="100" height="60" rx="12" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  
  <!-- Card chip -->
  <rect x="65" y="95" width="20" height="15" rx="3" fill="rgba(255,255,255,0.9)"/>
  <rect x="67" y="97" width="16" height="11" rx="2" fill="rgba(59, 130, 246, 0.3)"/>
  
  <!-- Card number -->
  <rect x="65" y="115" width="8" height="2" rx="1" fill="rgba(255,255,255,0.8)"/>
  <rect x="75" y="115" width="8" height="2" rx="1" fill="rgba(255,255,255,0.8)"/>
  <rect x="85" y="115" width="8" height="2" rx="1" fill="rgba(255,255,255,0.8)"/>
  <rect x="95" y="115" width="8" height="2" rx="1" fill="rgba(255,255,255,0.8)"/>
  
  <!-- Mastercard/Visa circles -->
  <circle cx="120" cy="100" r="8" fill="none" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
  <circle cx="130" cy="100" r="8" fill="none" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
  
  <!-- TOWER branding -->
  <text x="100" y="130" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-family="Arial, sans-serif" font-size="10" font-weight="bold">TOWER SACCO</text>
  
  <!-- Floating coins with glow -->
  <circle cx="30" cy="50" r="15" fill="url(#coinGradient)" filter="url(#glow)"/>
  <text x="30" y="56" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">$</text>
  
  <circle cx="170" cy="60" r="12" fill="url(#goldGradient)" filter="url(#glow)"/>
  <text x="170" y="66" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">€</text>
  
  <circle cx="25" cy="150" r="10" fill="url(#coinGradient)" filter="url(#glow)"/>
  <text x="25" y="155" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">¥</text>
  
  <circle cx="175" cy="140" r="13" fill="url(#goldGradient)" filter="url(#glow)"/>
  <text x="175" y="146" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">KSh</text>
  
  <!-- Money transfer animation -->
  <path d="M 60 160 Q 100 145 140 160" stroke="rgba(255,255,255,0.9)" stroke-width="3" fill="none" stroke-dasharray="6,4" filter="url(#glow)"/>
  <polygon points="135,157 143,160 135,163" fill="rgba(255,255,255,0.9)"/>
  
  <!-- Digital payment symbols -->
  <rect x="160" y="30" width="4" height="25" rx="2" fill="rgba(16, 185, 129, 0.8)"/>
  <rect x="167" y="25" width="4" height="30" rx="2" fill="rgba(16, 185, 129, 0.9)"/>
  <rect x="174" y="20" width="4" height="35" rx="2" fill="rgba(16, 185, 129, 1)"/>
  
  <!-- Mobile phone -->
  <rect x="15" y="90" width="25" height="40" rx="6" fill="rgba(31, 41, 55, 0.9)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
  <rect x="18" y="95" width="19" height="30" rx="3" fill="url(#cardGradient)"/>
  <circle cx="27.5" cy="127" r="2" fill="rgba(255,255,255,0.8)"/>
  
  <!-- Contactless payment waves -->
  <path d="M 45 70 Q 55 65 65 70" stroke="rgba(255,255,255,0.6)" stroke-width="2" fill="none"/>
  <path d="M 47 75 Q 55 70 63 75" stroke="rgba(255,255,255,0.4)" fill="none" stroke-width="2"/>
  <path d="M 49 80 Q 55 75 61 80" stroke="rgba(255,255,255,0.3)" fill="none" stroke-width="2"/>
</svg> 